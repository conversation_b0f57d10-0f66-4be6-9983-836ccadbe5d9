"""
智能记忆引擎 MVP v2.0 - Neo4j知识图谱服务模块

Neo4j知识图谱服务提供了完整的图数据库操作接口：
- Neo4j连接管理和连接池配置
- 数据库索引创建和维护
- 节点CRUD操作 (Episode, Entity, Statement)
- 关系构建和查询优化
- 连接状态检查和错误处理
- GDS图数据科学插件验证

核心功能：
1. 异步Neo4j驱动管理
2. Episode(情节)节点的创建、查询、更新
3. Entity(实体)节点管理和去重逻辑
4. Statement(陈述)节点和关系构建
5. Cypher查询优化和缓存
6. 图数据的向量化存储和检索

作者: CORE Team
版本: v2.0
创建时间: 2025年08月28日
"""

import asyncio
import logging
import time
import uuid
import json
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
from contextlib import asynccontextmanager

import neo4j
from neo4j import AsyncGraphDatabase, AsyncDriver, AsyncSession
from config import settings
from models import (
    Episode, Entity, Statement,
    ContentSource, RelationshipType, ProcessingStatus, NodeType
)

# 配置日志记录器
logger = logging.getLogger("smart_memory.knowledge_service")


# ================== 异常类定义 ==================

class Neo4jConnectionError(Exception):
    """Neo4j连接异常"""
    pass


class Neo4jQueryError(Exception):
    """Neo4j查询异常"""
    pass


class IndexCreationError(Exception):
    """索引创建异常"""
    pass


class NodeOperationError(Exception):
    """节点操作异常"""
    pass


class RelationshipOperationError(Exception):
    """关系操作异常"""
    pass


class GDSPluginError(Exception):
    """GDS插件异常"""
    pass


# ================== 主服务类 ==================

class KnowledgeGraphService:
    """
    Neo4j知识图谱服务
    
    提供异步的Neo4j数据库操作接口，支持：
    - 连接管理和健康检查
    - 索引创建和维护
    - 节点和关系的CRUD操作
    - 向量存储和相似度搜索
    - 图算法和数据分析
    """
    
    def __init__(self):
        """初始化知识图谱服务"""
        # Neo4j驱动配置
        self.driver: Optional[AsyncDriver] = None
        self.neo4j_config = settings.get_neo4j_config()
        
        # 服务状态跟踪
        self._is_initialized = False
        self._last_health_check = 0
        self._health_check_interval = 300  # 5分钟
        self._service_available = False
        self._gds_available = False
        
        # 查询缓存配置
        self._query_cache = {}
        self._cache_max_size = 1000
        self._cache_ttl = 3600  # 1小时
        
        # 批处理配置
        self._batch_size = 100
        self._max_batch_wait_time = 2.0  # 2秒
        
        # 索引配置
        self._required_indexes = {
            'Episode': ['id', 'created_at', 'processing_status'],
            'Entity': ['id', 'name', 'type', 'created_at'],
            'Statement': ['id', 'source_episode', 'created_at'],
        }
        
        # 约束配置
        self._required_constraints = {
            'Episode': ['id'],
            'Entity': ['id'],
            'Statement': ['id'],
        }
        
        logger.info("知识图谱服务初始化完成")
    
    def _serialize_for_neo4j(self, value: Any) -> Any:
        """将复杂数据类型序列化为Neo4j可接受的格式"""
        if value is None:
            return None
        elif isinstance(value, (str, int, float, bool)):
            return value
        elif isinstance(value, list):
            return [self._serialize_for_neo4j(item) for item in value]
        elif isinstance(value, dict):
            return json.dumps(value, ensure_ascii=False)
        else:
            return str(value)
    
    def _deserialize_from_neo4j(self, value: Any) -> Any:
        """将Neo4j中的数据反序列化为Python对象"""
        if isinstance(value, str):
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
        return value
    
    async def initialize(self) -> bool:
        """
        初始化Neo4j连接和数据库结构
        
        Returns:
            bool: 初始化是否成功
            
        Raises:
            Neo4jConnectionError: 连接初始化失败时
        """
        if self._is_initialized:
            logger.info("知识图谱服务已初始化，跳过重复初始化")
            return True
        
        try:
            logger.info("开始初始化Neo4j知识图谱服务...")
            
            # 1. 初始化Neo4j驱动
            await self._initialize_driver()
            
            # 2. 执行连接健康检查
            await self._perform_health_check()
            
            # 3. 创建数据库约束
            await self._create_constraints()
            
            # 4. 创建数据库索引
            await self._create_indexes()
            
            # 5. 验证GDS插件
            await self._verify_gds_plugin()
            
            self._is_initialized = True
            logger.info("✅ Neo4j知识图谱服务初始化成功")
            return True
        
        except Exception as e:
            logger.error(f"❌ Neo4j知识图谱服务初始化失败: {e}")
            await self.cleanup()
            raise Neo4jConnectionError(f"知识图谱服务初始化失败: {e}")
    
    async def _initialize_driver(self):
        """初始化Neo4j异步驱动"""
        try:
            logger.debug("初始化Neo4j异步驱动...")
            
            # 创建异步驱动实例
            self.driver = AsyncGraphDatabase.driver(
                self.neo4j_config["uri"],
                auth=self.neo4j_config["auth"],
                max_connection_pool_size=self.neo4j_config["max_connection_pool_size"],
                connection_timeout=self.neo4j_config["connection_timeout"],
                encrypted=False,  # 开发环境可关闭加密
                trust=neo4j.TRUST_ALL_CERTIFICATES,
            )
            
            logger.info(f"Neo4j驱动初始化完成 - URI: {self.neo4j_config['uri']}")
            
        except Exception as e:
            logger.error(f"Neo4j驱动初始化失败: {e}")
            raise
    
    async def _perform_health_check(self) -> bool:
        """
        执行Neo4j连接健康检查
        
        Returns:
            bool: 连接是否健康
            
        Raises:
            Neo4jConnectionError: 连接不可用时
        """
        current_time = time.time()
        
        # 检查是否需要进行健康检查
        if (current_time - self._last_health_check) < self._health_check_interval and self._service_available:
            return True
        
        try:
            logger.debug("执行Neo4j连接健康检查...")
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                # 执行简单查询测试连接
                result = await session.run("RETURN 1 AS health_check")
                record = await result.single()
                
                if record and record["health_check"] == 1:
                    # 检查数据库信息
                    db_info = await session.run("CALL dbms.components()")
                    components = await db_info.data()
                    
                    self._service_available = True
                    self._last_health_check = current_time
                    
                    logger.info("✅ Neo4j连接健康检查通过")
                    logger.debug(f"数据库组件信息: {components}")
                    return True
                else:
                    raise Neo4jConnectionError("健康检查查询返回异常结果")
        
        except Exception as e:
            self._service_available = False
            error_msg = f"Neo4j连接健康检查失败: {e}"
            logger.error(f"❌ {error_msg}")
            raise Neo4jConnectionError(error_msg)
    
    async def _create_constraints(self):
        """创建数据库约束"""
        try:
            logger.info("开始创建数据库约束...")
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                for node_type, constraint_fields in self._required_constraints.items():
                    for field in constraint_fields:
                        constraint_name = f"{node_type}_{field}_unique"
                        
                        # 检查约束是否已存在
                        check_query = "SHOW CONSTRAINTS WHERE name = $constraint_name"
                        result = await session.run(check_query, constraint_name=constraint_name)
                        existing = await result.data()
                        
                        if not existing:
                            # 创建唯一约束
                            create_query = f"""
                            CREATE CONSTRAINT {constraint_name} 
                            FOR (n:{node_type}) 
                            REQUIRE n.{field} IS UNIQUE
                            """
                            await session.run(create_query)
                            logger.debug(f"创建约束: {constraint_name}")
                        else:
                            logger.debug(f"约束已存在: {constraint_name}")
                
                logger.info("✅ 数据库约束创建完成")
        
        except Exception as e:
            logger.error(f"❌ 创建数据库约束失败: {e}")
            raise IndexCreationError(f"约束创建失败: {e}")
    
    async def _create_indexes(self):
        """创建数据库索引"""
        try:
            logger.info("开始创建数据库索引...")
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                for node_type, index_fields in self._required_indexes.items():
                    for field in index_fields:
                        index_name = f"{node_type}_{field}_index"
                        
                        # 检查索引是否已存在
                        check_query = "SHOW INDEXES WHERE name = $index_name"
                        result = await session.run(check_query, index_name=index_name)
                        existing = await result.data()
                        
                        if not existing:
                            try:
                                # 创建索引
                                create_query = f"""
                                CREATE INDEX {index_name} 
                                FOR (n:{node_type}) 
                                ON (n.{field})
                                """
                                await session.run(create_query)
                                logger.debug(f"创建索引: {index_name}")
                            except Exception as index_error:
                                # 检查是否是因为约束已存在而导致的错误
                                if "ConstraintAlreadyExists" in str(index_error):
                                    logger.debug(f"索引 {index_name} 已通过约束自动创建，跳过")
                                else:
                                    logger.warning(f"创建索引 {index_name} 失败: {index_error}")
                        else:
                            logger.debug(f"索引已存在: {index_name}")
                
                # 创建向量索引(如果支持)
                await self._create_vector_indexes()
                
                logger.info("✅ 数据库索引创建完成")
        
        except Exception as e:
            logger.error(f"❌ 创建数据库索引失败: {e}")
            raise IndexCreationError(f"索引创建失败: {e}")
    
    async def _create_vector_indexes(self):
        """创建向量索引（如果Neo4j支持向量搜索）"""
        try:
            # 为了简化，这里先跳过向量索引创建
            # 实际生产环境中可以根据Neo4j版本创建相应的向量索引
            logger.debug("向量索引创建已跳过（根据需要配置）")
            
        except Exception as e:
            logger.warning(f"向量索引创建失败: {e}")
    
    async def _verify_gds_plugin(self):
        """验证GDS(图数据科学)插件是否可用"""
        try:
            logger.info("验证GDS插件可用性...")
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                # 尝试调用GDS列表函数
                result = await session.run("CALL gds.list()")
                gds_data = await result.data()
                
                self._gds_available = True
                logger.info(f"✅ GDS插件验证通过，可用算法数量: {len(gds_data)}")
        
        except Exception as e:
            self._gds_available = False
            logger.warning(f"⚠️ GDS插件不可用: {e}")
            # GDS插件不可用不应影响核心功能
    
    async def is_service_healthy(self) -> bool:
        """
        检查服务健康状态（公共接口）
        
        Returns:
            bool: 服务是否健康
        """
        try:
            return await self._perform_health_check()
        except Neo4jConnectionError:
            return False
    
    # ================== Episode 节点操作 ==================
    
    async def create_episode(self, episode: Episode) -> str:
        """
        创建Episode节点
        
        Args:
            episode: Episode实例
            
        Returns:
            str: 创建的Episode ID
            
        Raises:
            NodeOperationError: 节点创建失败时
        """
        if not self._is_initialized:
            await self.initialize()
        
        try:
            logger.debug(f"创建Episode节点: {episode.id}")
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                # 构建Episode节点属性
                properties = {
                    "id": episode.id,
                    "content": episode.content,
                    "title": episode.title,
                    "summary": episode.summary,
                    "source": episode.source.value if hasattr(episode.source, 'value') else str(episode.source),
                    "session_id": episode.session_id,
                    "tags": self._serialize_for_neo4j(episode.tags),
                    "metadata": self._serialize_for_neo4j(episode.metadata),
                    "embedding": self._serialize_for_neo4j(episode.embedding),
                    "entities_count": episode.entities_count,
                    "statements_count": episode.statements_count,
                    "processing_status": episode.processing_status.value if hasattr(episode.processing_status, 'value') else str(episode.processing_status),
                    "processing_time_ms": episode.processing_time_ms,
                    "quality_score": episode.quality_score,
                    "created_at": episode.created_at.isoformat(),
                    "updated_at": episode.updated_at.isoformat() if episode.updated_at else None
                }
                
                # 创建Episode节点的Cypher查询
                create_query = """
                CREATE (e:Episode $properties)
                RETURN e.id AS episode_id
                """
                
                result = await session.run(create_query, properties=properties)
                record = await result.single()
                
                if record:
                    created_id = record["episode_id"]
                    logger.info(f"✅ Episode节点创建成功: {created_id}")
                    return created_id
                else:
                    raise NodeOperationError("Episode节点创建失败：未返回结果")
        
        except Exception as e:
            error_msg = f"Episode节点创建失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg)
    
    async def get_episode(self, episode_id: str) -> Optional[Dict[str, Any]]:
        """
        根据ID获取Episode节点
        
        Args:
            episode_id: Episode ID
            
        Returns:
            Optional[Dict[str, Any]]: Episode节点数据，不存在则返回None
            
        Raises:
            NodeOperationError: 查询失败时
        """
        if not self._is_initialized:
            await self.initialize()
        
        try:
            logger.debug(f"获取Episode节点: {episode_id}")
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                query = """
                MATCH (e:Episode {id: $episode_id})
                RETURN e
                """
                
                result = await session.run(query, episode_id=episode_id)
                record = await result.single()
                
                if record:
                    episode_data = dict(record["e"])
                    logger.debug(f"Episode节点获取成功: {episode_id}")
                    return episode_data
                else:
                    logger.debug(f"Episode节点不存在: {episode_id}")
                    return None
        
        except Exception as e:
            error_msg = f"获取Episode节点失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg)
    
    async def update_episode(self, episode_id: str, updates: Dict[str, Any]) -> bool:
        """
        更新Episode节点
        
        Args:
            episode_id: Episode ID
            updates: 更新的字段字典
            
        Returns:
            bool: 更新是否成功
            
        Raises:
            NodeOperationError: 更新失败时
        """
        if not self._is_initialized:
            await self.initialize()
        
        try:
            logger.debug(f"更新Episode节点: {episode_id}")
            
            # 添加更新时间
            updates["updated_at"] = datetime.now(timezone.utc).isoformat()
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                # 构建动态更新查询
                set_clauses = []
                for key in updates.keys():
                    set_clauses.append(f"e.{key} = ${key}")
                
                set_clause = ", ".join(set_clauses)
                
                query = f"""
                MATCH (e:Episode {{id: $episode_id}})
                SET {set_clause}
                RETURN e.id AS updated_id
                """
                
                params = {"episode_id": episode_id, **updates}
                result = await session.run(query, **params)
                record = await result.single()
                
                if record:
                    logger.info(f"✅ Episode节点更新成功: {episode_id}")
                    return True
                else:
                    logger.warning(f"Episode节点不存在，无法更新: {episode_id}")
                    return False
        
        except Exception as e:
            error_msg = f"更新Episode节点失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg)
    
    async def list_episodes(self, limit: int = 100, offset: int = 0, 
                          filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        列出Episode节点（支持分页和过滤）
        
        Args:
            limit: 返回数量限制
            offset: 偏移量
            filters: 过滤条件字典
            
        Returns:
            List[Dict[str, Any]]: Episode节点列表
            
        Raises:
            NodeOperationError: 查询失败时
        """
        if not self._is_initialized:
            await self.initialize()
        
        try:
            logger.debug(f"列出Episode节点 - limit: {limit}, offset: {offset}")
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                # 构建基础查询
                where_clauses = []
                params = {"limit": limit, "offset": offset}
                
                if filters:
                    for key, value in filters.items():
                        where_clauses.append(f"e.{key} = ${key}")
                        params[key] = value
                
                where_clause = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""
                
                query = f"""
                MATCH (e:Episode)
                {where_clause}
                RETURN e
                ORDER BY e.created_at DESC
                SKIP $offset
                LIMIT $limit
                """
                
                result = await session.run(query, **params)
                records = await result.data()
                
                episodes = [dict(record["e"]) for record in records]
                logger.debug(f"获取到 {len(episodes)} 个Episode节点")
                
                return episodes
        
        except Exception as e:
            error_msg = f"列出Episode节点失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg)
    
    # ================== Entity 节点操作 ==================
    
    async def create_entity(self, entity: Entity) -> str:
        """
        创建或更新Entity节点（支持去重逻辑）
        
        Args:
            entity: Entity实例
            
        Returns:
            str: 实体ID（新创建或已存在）
            
        Raises:
            NodeOperationError: 节点操作失败时
        """
        if not self._is_initialized:
            await self.initialize()
        
        try:
            logger.debug(f"创建Entity节点: {entity.name} ({entity.type})")
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                # 首先检查是否存在相同的实体（基于名称和类型去重）
                existing = await self._find_existing_entity(session, entity.name, entity.type)
                
                if existing:
                    # 实体已存在，更新信息并增加来源
                    entity_id = existing["id"]
                    await self._merge_entity_info(session, entity_id, entity)
                    logger.info(f"✅ Entity节点合并更新: {entity_id}")
                    return entity_id
                
                else:
                    # 创建新实体
                    properties = {
                        "id": entity.id,
                        "name": entity.name,
                        "type": entity.type,
                        "description": entity.description,
                        "properties": self._serialize_for_neo4j(entity.properties),
                        "embedding": self._serialize_for_neo4j(entity.embedding),
                        "confidence": entity.confidence,
                        "source_episodes": self._serialize_for_neo4j(entity.source_episodes),
                        "aliases": self._serialize_for_neo4j(entity.aliases),
                        "created_at": entity.created_at.isoformat(),
                        "updated_at": entity.updated_at.isoformat() if entity.updated_at else None
                    }
                    
                    create_query = """
                    CREATE (e:Entity $properties)
                    RETURN e.id AS entity_id
                    """
                    
                    result = await session.run(create_query, properties=properties)
                    record = await result.single()
                    
                    if record:
                        created_id = record["entity_id"]
                        logger.info(f"✅ Entity节点创建成功: {created_id}")
                        return created_id
                    else:
                        raise NodeOperationError("Entity节点创建失败：未返回结果")
        
        except Exception as e:
            error_msg = f"Entity节点操作失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg)
    
    async def _find_existing_entity(self, session: AsyncSession, name: str, 
                                  entity_type: str) -> Optional[Dict[str, Any]]:
        """查找已存在的实体"""
        query = """
        MATCH (e:Entity {name: $name, type: $type})
        RETURN e
        """
        result = await session.run(query, name=name, type=entity_type)
        record = await result.single()
        
        return dict(record["e"]) if record else None
    
    async def _merge_entity_info(self, session: AsyncSession, entity_id: str, new_entity: Entity):
        """合并实体信息"""
        query = """
        MATCH (e:Entity {id: $entity_id})
        SET e.confidence = CASE 
            WHEN $new_confidence > e.confidence THEN $new_confidence 
            ELSE e.confidence 
        END,
        e.source_episodes = e.source_episodes + $new_episodes,
        e.aliases = e.aliases + $new_aliases,
        e.updated_at = $updated_at,
        e.description = CASE 
            WHEN $new_description IS NOT NULL AND size($new_description) > size(e.description) 
            THEN $new_description 
            ELSE e.description 
        END
        RETURN e.id AS updated_id
        """
        
        params = {
            "entity_id": entity_id,
            "new_confidence": new_entity.confidence,
            "new_episodes": [ep for ep in new_entity.source_episodes if ep],
            "new_aliases": [alias for alias in new_entity.aliases if alias],
            "updated_at": datetime.now(timezone.utc).isoformat(),
            "new_description": new_entity.description
        }
        
        await session.run(query, **params)
    
    async def get_entity(self, entity_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取Entity节点"""
        if not self._is_initialized:
            await self.initialize()
        
        try:
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                query = """
                MATCH (e:Entity {id: $entity_id})
                RETURN e
                """
                
                result = await session.run(query, entity_id=entity_id)
                record = await result.single()
                
                return dict(record["e"]) if record else None
        
        except Exception as e:
            error_msg = f"获取Entity节点失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg)
    
    async def search_entities_by_type(self, entity_type: str, limit: int = 50) -> List[Dict[str, Any]]:
        """根据类型搜索实体"""
        if not self._is_initialized:
            await self.initialize()
        
        try:
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                query = """
                MATCH (e:Entity {type: $type})
                RETURN e
                ORDER BY e.confidence DESC
                LIMIT $limit
                """
                
                result = await session.run(query, type=entity_type, limit=limit)
                records = await result.data()
                
                return [dict(record["e"]) for record in records]
        
        except Exception as e:
            error_msg = f"搜索Entity节点失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg)
    
    # ================== Statement 节点操作 ==================
    
    async def create_statement(self, statement: Statement) -> str:
        """
        创建Statement节点
        
        Args:
            statement: Statement实例
            
        Returns:
            str: 创建的Statement ID
            
        Raises:
            NodeOperationError: 节点创建失败时
        """
        if not self._is_initialized:
            await self.initialize()
        
        try:
            logger.debug(f"创建Statement节点: {statement.id}")
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                properties = {
                    "id": statement.id,
                    "content": statement.content,
                    "subject": statement.subject,
                    "predicate": statement.predicate,
                    "object": statement.object,
                    "embedding": self._serialize_for_neo4j(statement.embedding),
                    "confidence": statement.confidence,
                    "source_episode": statement.source_episode,
                    "importance_score": statement.importance_score,
                    "verification_status": statement.verification_status,
                    "created_at": statement.created_at.isoformat(),
                    "updated_at": statement.updated_at.isoformat() if statement.updated_at else None
                }
                
                create_query = """
                CREATE (s:Statement $properties)
                RETURN s.id AS statement_id
                """
                
                result = await session.run(create_query, properties=properties)
                record = await result.single()
                
                if record:
                    created_id = record["statement_id"]
                    logger.info(f"✅ Statement节点创建成功: {created_id}")
                    return created_id
                else:
                    raise NodeOperationError("Statement节点创建失败：未返回结果")
        
        except Exception as e:
            error_msg = f"Statement节点创建失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg)
    
    async def get_statement(self, statement_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取Statement节点"""
        if not self._is_initialized:
            await self.initialize()
        
        try:
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                query = """
                MATCH (s:Statement {id: $statement_id})
                RETURN s
                """
                
                result = await session.run(query, statement_id=statement_id)
                record = await result.single()
                
                return dict(record["s"]) if record else None
        
        except Exception as e:
            error_msg = f"获取Statement节点失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg)
    
    async def get_statements_by_episode(self, episode_id: str) -> List[Dict[str, Any]]:
        """获取指定Episode的所有Statement"""
        if not self._is_initialized:
            await self.initialize()
        
        try:
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                query = """
                MATCH (s:Statement {source_episode: $episode_id})
                RETURN s
                ORDER BY s.importance_score DESC
                """
                
                result = await session.run(query, episode_id=episode_id)
                records = await result.data()
                
                return [dict(record["s"]) for record in records]
        
        except Exception as e:
            error_msg = f"获取Episode的Statement失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg)
    
    # ================== 关系操作 ==================
    
    async def create_relationship(self, source_id: str, target_id: str, 
                                relationship_type: RelationshipType,
                                properties: Optional[Dict[str, Any]] = None) -> str:
        """
        创建节点间的关系
        
        Args:
            source_id: 源节点ID
            target_id: 目标节点ID
            relationship_type: 关系类型
            properties: 关系属性
            
        Returns:
            str: 关系ID
            
        Raises:
            RelationshipOperationError: 关系创建失败时
        """
        if not self._is_initialized:
            await self.initialize()
        
        try:
            logger.debug(f"创建关系: {source_id} -> {target_id} ({relationship_type.value})")
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                rel_id = str(uuid.uuid4())
                rel_properties = properties or {}
                rel_properties.update({
                    "id": rel_id,
                    "created_at": datetime.now(timezone.utc).isoformat(),
                    "weight": rel_properties.get("weight", 1.0),
                    "confidence": rel_properties.get("confidence", 1.0)
                })
                
                # 使用MERGE确保关系不重复
                query = f"""
                MATCH (source {{id: $source_id}}), (target {{id: $target_id}})
                MERGE (source)-[r:{relationship_type.value}]->(target)
                ON CREATE SET r = $properties
                ON MATCH SET r.weight = r.weight + 0.1, r.updated_at = $updated_at
                RETURN r.id AS rel_id
                """
                
                params = {
                    "source_id": source_id,
                    "target_id": target_id,
                    "properties": rel_properties,
                    "updated_at": datetime.now(timezone.utc).isoformat()
                }
                
                result = await session.run(query, **params)
                record = await result.single()
                
                if record:
                    logger.info(f"✅ 关系创建/更新成功: {rel_id}")
                    return rel_properties["id"]
                else:
                    raise RelationshipOperationError("关系创建失败：源或目标节点不存在")
        
        except Exception as e:
            error_msg = f"创建关系失败: {e}"
            logger.error(error_msg)
            raise RelationshipOperationError(error_msg)
    
    # ================== 批量操作 ==================
    
    async def create_episode_with_knowledge(self, episode: Episode, 
                                         entities: List[Entity], 
                                         statements: List[Statement]) -> Dict[str, Any]:
        """
        批量创建Episode及其关联的实体和陈述
        
        Args:
            episode: Episode实例
            entities: 实体列表
            statements: 陈述列表
            
        Returns:
            Dict[str, Any]: 创建结果统计
            
        Raises:
            NodeOperationError: 批量创建失败时
        """
        if not self._is_initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            logger.info(f"开始批量创建知识图谱 - Episode: {episode.id}, 实体: {len(entities)}, 陈述: {len(statements)}")
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                tx = await session.begin_transaction()
                try:
                    # 1. 创建Episode节点
                    episode_id = await self._create_episode_in_tx(tx, episode)
                    
                    # 2. 创建Entity节点并建立关系
                    entity_ids = []
                    for entity in entities:
                        entity_id = await self._create_entity_in_tx(tx, entity)
                        entity_ids.append(entity_id)
                        
                        # 建立Episode->Entity的CONTAINS关系
                        await self._create_relationship_in_tx(
                            tx, episode_id, entity_id, RelationshipType.CONTAINS
                        )
                    
                    # 3. 创建Statement节点并建立关系
                    statement_ids = []
                    for statement in statements:
                        statement.source_episode = episode_id  # 确保关联正确的Episode
                        statement_id = await self._create_statement_in_tx(tx, statement)
                        statement_ids.append(statement_id)
                        
                        # 建立Episode->Statement的CONTAINS关系
                        await self._create_relationship_in_tx(
                            tx, episode_id, statement_id, RelationshipType.CONTAINS
                        )
                    
                    # 4. 建立实体间关系（基于Statement中的主谓宾）
                    await self._create_entity_relationships_from_statements(tx, statements)
                    
                    # 提交事务
                    await tx.commit()
                    
                except Exception as e:
                    # 回滚事务
                    await tx.rollback()
                    raise e
            
            processing_time = time.time() - start_time
            
            result = {
                "episode_id": episode_id,
                "entities_created": len(entity_ids),
                "statements_created": len(statement_ids),
                "processing_time_seconds": processing_time,
                "entity_ids": entity_ids,
                "statement_ids": statement_ids
            }
            
            logger.info(f"✅ 知识图谱批量创建成功 - 耗时: {processing_time:.2f}s")
            return result
        
        except Exception as e:
            error_msg = f"知识图谱批量创建失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg)
    
    async def _create_episode_in_tx(self, tx, episode: Episode) -> str:
        """在事务中创建Episode"""
        properties = {
            "id": episode.id,
            "content": episode.content,
            "title": episode.title,
            "summary": episode.summary,
            "source": episode.source.value if hasattr(episode.source, 'value') else str(episode.source),
            "session_id": episode.session_id,
            "tags": self._serialize_for_neo4j(episode.tags),
            "metadata": self._serialize_for_neo4j(episode.metadata),
            "embedding": self._serialize_for_neo4j(episode.embedding),
            "entities_count": len([]),  # 将在后续更新
            "statements_count": len([]),  # 将在后续更新
            "processing_status": episode.processing_status.value if hasattr(episode.processing_status, 'value') else str(episode.processing_status),
            "processing_time_ms": episode.processing_time_ms,
            "quality_score": episode.quality_score,
            "created_at": episode.created_at.isoformat()
        }
        
        query = """
        CREATE (e:Episode $properties)
        RETURN e.id AS episode_id
        """
        
        result = await tx.run(query, properties=properties)
        record = await result.single()
        return record["episode_id"]
    
    async def _create_entity_in_tx(self, tx, entity: Entity) -> str:
        """在事务中创建Entity（支持去重）"""
        # 检查是否已存在
        existing_query = """
        MATCH (e:Entity {name: $name, type: $type})
        RETURN e.id AS existing_id
        """
        existing_result = await tx.run(existing_query, name=entity.name, type=entity.type)
        existing_record = await existing_result.single()
        
        if existing_record:
            return existing_record["existing_id"]
        
        # 创建新实体
        properties = {
            "id": entity.id,
            "name": entity.name,
            "type": entity.type,
            "description": entity.description,
            "properties": self._serialize_for_neo4j(entity.properties),
            "embedding": self._serialize_for_neo4j(entity.embedding),
            "confidence": entity.confidence,
            "source_episodes": self._serialize_for_neo4j(entity.source_episodes),
            "aliases": self._serialize_for_neo4j(entity.aliases),
            "created_at": entity.created_at.isoformat()
        }
        
        query = """
        CREATE (e:Entity $properties)
        RETURN e.id AS entity_id
        """
        
        result = await tx.run(query, properties=properties)
        record = await result.single()
        return record["entity_id"]
    
    async def _create_statement_in_tx(self, tx, statement: Statement) -> str:
        """在事务中创建Statement"""
        properties = {
            "id": statement.id,
            "content": statement.content,
            "subject": statement.subject,
            "predicate": statement.predicate,
            "object": statement.object,
            "embedding": self._serialize_for_neo4j(statement.embedding),
            "confidence": statement.confidence,
            "source_episode": statement.source_episode,
            "importance_score": statement.importance_score,
            "verification_status": statement.verification_status,
            "created_at": statement.created_at.isoformat()
        }
        
        query = """
        CREATE (s:Statement $properties)
        RETURN s.id AS statement_id
        """
        
        result = await tx.run(query, properties=properties)
        record = await result.single()
        return record["statement_id"]
    
    async def _create_relationship_in_tx(self, tx, source_id: str, target_id: str, 
                                       relationship_type: RelationshipType, 
                                       properties: Optional[Dict[str, Any]] = None):
        """在事务中创建关系"""
        rel_properties = properties or {}
        rel_properties.update({
            "id": str(uuid.uuid4()),
            "created_at": datetime.now(timezone.utc).isoformat(),
            "weight": rel_properties.get("weight", 1.0)
        })
        
        query = f"""
        MATCH (source {{id: $source_id}}), (target {{id: $target_id}})
        MERGE (source)-[r:{relationship_type.value}]->(target)
        SET r = $properties
        """
        
        await tx.run(query, source_id=source_id, target_id=target_id, properties=rel_properties)
    
    async def _create_entity_relationships_from_statements(self, tx, statements: List[Statement]):
        """基于Statement建立实体间关系"""
        # 这里可以实现更复杂的实体关系推断逻辑
        # 目前先建立基础的相关关系
        for statement in statements:
            if statement.subject and statement.object:
                # 尝试匹配实体名称并建立关系
                await self._try_link_entities_by_names(tx, statement.subject, statement.object, statement.predicate)
    
    async def _try_link_entities_by_names(self, tx, subject_name: str, object_name: str, predicate: str):
        """尝试通过名称链接实体"""
        query = """
        MATCH (subject:Entity), (object:Entity)
        WHERE subject.name CONTAINS $subject_name AND object.name CONTAINS $object_name
        MERGE (subject)-[r:RELATED_TO]->(object)
        SET r.predicate = $predicate, 
            r.created_at = $created_at,
            r.weight = coalesce(r.weight, 0) + 0.1
        """
        
        await tx.run(query, 
                   subject_name=subject_name, 
                   object_name=object_name, 
                   predicate=predicate,
                   created_at=datetime.now(timezone.utc).isoformat())
    
    # ================== 向量相似度搜索 ==================
    
    async def similarity_search(self, 
                              query_embedding: List[float],
                              node_types: Optional[List[NodeType]] = None,
                              limit: int = 10,
                              threshold: float = 0.7,
                              include_metadata: bool = False) -> List[Dict[str, Any]]:
        """
        基于向量相似度的语义搜索
        
        Args:
            query_embedding: 查询向量（768维或其他维度）
            node_types: 搜索的节点类型过滤（Episode, Entity, Statement）
            limit: 返回结果数量限制
            threshold: 相似度阈值（0.0-1.0）
            include_metadata: 是否包含详细元数据
            
        Returns:
            List[Dict[str, Any]]: 相似度搜索结果列表，按相似度降序排列
            
        Raises:
            NodeOperationError: 搜索失败时
        """
        if not self._is_initialized:
            await self.initialize()
        
        try:
            start_time = time.time()
            logger.info(f"开始向量相似度搜索 - 向量维度: {len(query_embedding)}, 阈值: {threshold}")
            
            # 构建节点类型过滤条件
            node_filter = ""
            if node_types:
                type_labels = [f"n:{node_type.value}" for node_type in node_types]
                node_filter = f"WHERE ({' OR '.join(type_labels)})"
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                # 查询有向量的节点
                query = f"""
                MATCH (n)
                {node_filter}
                WHERE n.embedding IS NOT NULL
                RETURN n, labels(n)[0] AS node_type, n.embedding AS embedding
                """
                
                result = await session.run(query)
                records = await result.data()
                
                similar_nodes = []
                processed_count = 0
                
                for record in records:
                    node_data = dict(record["n"])
                    node_type = record["node_type"]
                    stored_embedding = record["embedding"]
                    
                    if stored_embedding and len(stored_embedding) == len(query_embedding):
                        try:
                            # 计算余弦相似度
                            similarity = self._calculate_cosine_similarity(query_embedding, stored_embedding)
                            
                            if similarity >= threshold:
                                search_result = {
                                    "id": node_data["id"],
                                    "node_type": node_type,
                                    "similarity_score": similarity,
                                    "title": node_data.get("title") or node_data.get("name") or node_data.get("content", "")[:50] + "...",
                                    "content": node_data.get("content", ""),
                                    "created_at": node_data.get("created_at"),
                                    "confidence": node_data.get("confidence", 1.0)
                                }
                                
                                if include_metadata:
                                    search_result["metadata"] = {
                                        "properties": node_data.get("properties", {}),
                                        "tags": node_data.get("tags", []),
                                        "source": node_data.get("source"),
                                        "processing_status": node_data.get("processing_status")
                                    }
                                
                                similar_nodes.append(search_result)
                        
                        except Exception as calc_error:
                            logger.warning(f"计算相似度失败: {calc_error}")
                            continue
                    
                    processed_count += 1
                
                # 按相似度排序并限制结果数量
                similar_nodes.sort(key=lambda x: x["similarity_score"], reverse=True)
                similar_nodes = similar_nodes[:limit]
                
                processing_time = time.time() - start_time
                
                logger.info(f"✅ 向量相似度搜索完成 - 处理: {processed_count}个节点, 返回: {len(similar_nodes)}个结果, 耗时: {processing_time:.2f}s")
                
                return similar_nodes
        
        except Exception as e:
            error_msg = f"向量相似度搜索失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg)
    
    async def hybrid_search(self,
                          query_text: str,
                          query_embedding: Optional[List[float]] = None,
                          node_types: Optional[List[NodeType]] = None,
                          limit: int = 10,
                          vector_weight: float = 0.6,
                          text_weight: float = 0.4,
                          similarity_threshold: float = 0.5,
                          include_metadata: bool = False) -> List[Dict[str, Any]]:
        """
        混合搜索：结合向量相似度和文本匹配
        
        Args:
            query_text: 查询文本
            query_embedding: 查询向量（可选，如果提供则启用向量搜索）
            node_types: 搜索的节点类型过滤
            limit: 返回结果数量限制
            vector_weight: 向量相似度权重（0.0-1.0）
            text_weight: 文本匹配权重（0.0-1.0）
            similarity_threshold: 最小相似度阈值
            include_metadata: 是否包含详细元数据
            
        Returns:
            List[Dict[str, Any]]: 混合搜索结果列表
            
        Raises:
            NodeOperationError: 搜索失败时
        """
        if not self._is_initialized:
            await self.initialize()
        
        try:
            start_time = time.time()
            logger.info(f"开始混合搜索 - 查询: '{query_text[:50]}...', 向量权重: {vector_weight}, 文本权重: {text_weight}")
            
            # 构建节点类型过滤
            node_filter = ""
            if node_types:
                type_labels = [f"n:{node_type.value}" for node_type in node_types]
                node_filter = f"WHERE ({' OR '.join(type_labels)})"
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                # 文本搜索查询
                text_query = f"""
                MATCH (n)
                {node_filter}
                WHERE toLower(n.content) CONTAINS toLower($query_text)
                   OR (n.name IS NOT NULL AND toLower(n.name) CONTAINS toLower($query_text))
                   OR (n.title IS NOT NULL AND toLower(n.title) CONTAINS toLower($query_text))
                   OR (n.description IS NOT NULL AND toLower(n.description) CONTAINS toLower($query_text))
                RETURN n, labels(n)[0] AS node_type, n.embedding AS embedding
                """
                
                result = await session.run(text_query, query_text=query_text)
                records = await result.data()
                
                hybrid_results = []
                processed_count = 0
                
                for record in records:
                    node_data = dict(record["n"])
                    node_type = record["node_type"]
                    stored_embedding = record["embedding"]
                    
                    # 计算文本匹配分数（基于关键词出现频率）
                    text_score = self._calculate_text_relevance_score(query_text, node_data)
                    
                    # 计算向量相似度分数
                    vector_score = 0.0
                    if query_embedding and stored_embedding and len(stored_embedding) == len(query_embedding):
                        vector_score = self._calculate_cosine_similarity(query_embedding, stored_embedding)
                    
                    # 计算混合分数
                    if query_embedding:
                        combined_score = (vector_score * vector_weight) + (text_score * text_weight)
                    else:
                        combined_score = text_score  # 纯文本搜索
                    
                    if combined_score >= similarity_threshold:
                        search_result = {
                            "id": node_data["id"],
                            "node_type": node_type,
                            "combined_score": combined_score,
                            "vector_score": vector_score,
                            "text_score": text_score,
                            "title": node_data.get("title") or node_data.get("name") or node_data.get("content", "")[:50] + "...",
                            "content": node_data.get("content", ""),
                            "created_at": node_data.get("created_at"),
                            "confidence": node_data.get("confidence", 1.0),
                            "highlight_snippets": self._extract_highlight_snippets(query_text, node_data.get("content", ""))
                        }
                        
                        if include_metadata:
                            search_result["metadata"] = {
                                "properties": node_data.get("properties", {}),
                                "tags": node_data.get("tags", []),
                                "source": node_data.get("source"),
                                "processing_status": node_data.get("processing_status")
                            }
                        
                        hybrid_results.append(search_result)
                    
                    processed_count += 1
                
                # 按综合分数排序并限制结果数量
                hybrid_results.sort(key=lambda x: x["combined_score"], reverse=True)
                hybrid_results = hybrid_results[:limit]
                
                processing_time = time.time() - start_time
                
                logger.info(f"✅ 混合搜索完成 - 处理: {processed_count}个节点, 返回: {len(hybrid_results)}个结果, 耗时: {processing_time:.2f}s")
                
                return hybrid_results
        
        except Exception as e:
            error_msg = f"混合搜索失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg)
    
    def _calculate_text_relevance_score(self, query_text: str, node_data: Dict[str, Any]) -> float:
        """
        计算文本相关性分数
        
        Args:
            query_text: 查询文本
            node_data: 节点数据
            
        Returns:
            float: 文本相关性分数 (0.0-1.0)
        """
        try:
            query_lower = query_text.lower()
            query_words = set(query_lower.split())
            
            # 合并所有文本字段
            text_fields = [
                node_data.get("content", ""),
                node_data.get("name", ""),
                node_data.get("title", ""),
                node_data.get("description", "")
            ]
            
            combined_text = " ".join([str(field) for field in text_fields if field]).lower()
            text_words = combined_text.split()
            
            if not query_words or not text_words:
                return 0.0
            
            # 计算匹配单词数量
            matches = sum(1 for word in query_words if word in combined_text)
            
            # 基础匹配分数
            base_score = matches / len(query_words)
            
            # TF-IDF风格的加权：短文档中的匹配更重要
            length_factor = min(1.0, 100.0 / max(1, len(text_words)))
            
            # 精确匹配加分
            exact_match_bonus = 0.2 if query_lower in combined_text else 0.0
            
            final_score = min(1.0, base_score * (1 + length_factor) + exact_match_bonus)
            
            return final_score
        
        except Exception:
            return 0.0
    
    def _extract_highlight_snippets(self, query_text: str, content: str, max_snippets: int = 3) -> List[str]:
        """
        提取高亮片段
        
        Args:
            query_text: 查询文本
            content: 内容文本
            max_snippets: 最大片段数量
            
        Returns:
            List[str]: 高亮片段列表
        """
        try:
            if not content or not query_text:
                return []
            
            query_words = query_text.lower().split()
            content_lower = content.lower()
            
            snippets = []
            snippet_length = 150  # 每个片段的长度
            
            for word in query_words:
                if word in content_lower:
                    # 找到第一个匹配位置
                    pos = content_lower.find(word)
                    if pos != -1:
                        # 提取前后文
                        start = max(0, pos - snippet_length // 2)
                        end = min(len(content), pos + snippet_length // 2)
                        snippet = content[start:end].strip()
                        
                        if snippet and snippet not in snippets:
                            snippets.append(snippet)
                            
                        if len(snippets) >= max_snippets:
                            break
            
            return snippets
        
        except Exception:
            return []
    
    # ================== 查询和搜索 ==================
    
    async def search_similar_episodes_by_embedding(self, query_embedding: List[float], 
                                                 limit: int = 10, 
                                                 threshold: float = 0.7) -> List[Dict[str, Any]]:
        """
        通过向量相似度搜索相关Episode（兼容性方法，推荐使用similarity_search）
        
        Args:
            query_embedding: 查询向量
            limit: 返回结果数量
            threshold: 相似度阈值
            
        Returns:
            List[Dict[str, Any]]: 相似Episode列表，包含相似度分数
        """
        logger.warning("search_similar_episodes_by_embedding 方法已废弃，请使用 similarity_search 方法")
        
        # 调用新的通用相似度搜索方法
        from models import NodeType
        results = await self.similarity_search(
            query_embedding=query_embedding,
            node_types=[NodeType.EPISODE],
            limit=limit,
            threshold=threshold,
            include_metadata=False
        )
        
        # 转换为旧格式以保持兼容性
        compatible_results = []
        for result in results:
            episode_data = {
                "id": result["id"],
                "title": result["title"],
                "content": result["content"],
                "created_at": result["created_at"],
                "confidence": result["confidence"],
                "similarity_score": result["similarity_score"]
            }
            compatible_results.append(episode_data)
        
        return compatible_results
    
    def _calculate_cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """
        计算两个向量的余弦相似度
        
        Args:
            vec1: 第一个向量
            vec2: 第二个向量
            
        Returns:
            float: 余弦相似度值 (-1.0 到 1.0)
        """
        try:
            if not vec1 or not vec2 or len(vec1) != len(vec2):
                return 0.0
            
            # 计算点积
            dot_product = sum(a * b for a, b in zip(vec1, vec2))
            
            # 计算向量的模长
            norm1 = sum(a * a for a in vec1) ** 0.5
            norm2 = sum(b * b for b in vec2) ** 0.5
            
            # 避免除零
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            
            # 确保结果在有效范围内
            return max(-1.0, min(1.0, similarity))
        
        except Exception as e:
            logger.warning(f"计算余弦相似度失败: {e}")
            return 0.0
    
    async def search_entities_by_name(self, name_pattern: str, 
                                    fuzzy: bool = True, 
                                    limit: int = 20) -> List[Dict[str, Any]]:
        """
        根据名称模式搜索实体
        
        Args:
            name_pattern: 名称模式
            fuzzy: 是否启用模糊匹配
            limit: 返回结果数量
            
        Returns:
            List[Dict[str, Any]]: 匹配的实体列表
        """
        if not self._is_initialized:
            await self.initialize()
        
        try:
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                if fuzzy:
                    # 模糊搜索：使用CONTAINS或正则表达式
                    query = """
                    MATCH (e:Entity)
                    WHERE toLower(e.name) CONTAINS toLower($pattern)
                       OR any(alias IN e.aliases WHERE toLower(alias) CONTAINS toLower($pattern))
                    RETURN e
                    ORDER BY e.confidence DESC
                    LIMIT $limit
                    """
                else:
                    # 精确匹配
                    query = """
                    MATCH (e:Entity)
                    WHERE e.name = $pattern
                       OR $pattern IN e.aliases
                    RETURN e
                    ORDER BY e.confidence DESC
                    LIMIT $limit
                    """
                
                result = await session.run(query, pattern=name_pattern, limit=limit)
                records = await result.data()
                
                return [dict(record["e"]) for record in records]
        
        except Exception as e:
            error_msg = f"实体名称搜索失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg)
    
    async def get_entity_relationships(self, entity_id: str, 
                                     relationship_types: Optional[List[str]] = None,
                                     direction: str = "both") -> List[Dict[str, Any]]:
        """
        获取实体的关系信息
        
        Args:
            entity_id: 实体ID
            relationship_types: 关系类型过滤列表
            direction: 关系方向 ("incoming", "outgoing", "both")
            
        Returns:
            List[Dict[str, Any]]: 关系信息列表
        """
        if not self._is_initialized:
            await self.initialize()
        
        try:
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                # 构建查询条件
                if direction == "outgoing":
                    match_pattern = "(e)-[r]->(related)"
                elif direction == "incoming":
                    match_pattern = "(related)-[r]->(e)"
                else:  # both
                    match_pattern = "(e)-[r]-(related)"
                
                # 构建关系类型过滤
                type_filter = ""
                if relationship_types:
                    types_str = "|".join(relationship_types)
                    type_filter = f":{types_str}"
                
                query = f"""
                MATCH {match_pattern.replace('[r]', f'[r{type_filter}]')}
                WHERE e.id = $entity_id
                RETURN r, related, type(r) AS relationship_type
                ORDER BY r.weight DESC, r.created_at DESC
                """
                
                result = await session.run(query, entity_id=entity_id)
                records = await result.data()
                
                relationships = []
                for record in records:
                    rel_data = {
                        "relationship": dict(record["r"]),
                        "related_entity": dict(record["related"]),
                        "relationship_type": record["relationship_type"]
                    }
                    relationships.append(rel_data)
                
                return relationships
        
        except Exception as e:
            error_msg = f"获取实体关系失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg)
    
    async def get_knowledge_path(self, start_entity_id: str, end_entity_id: str, 
                               max_depth: int = 3) -> Optional[List[Dict[str, Any]]]:
        """
        查找两个实体之间的知识路径
        
        Args:
            start_entity_id: 起始实体ID
            end_entity_id: 目标实体ID
            max_depth: 最大搜索深度
            
        Returns:
            Optional[List[Dict[str, Any]]]: 路径信息，如果没有找到则返回None
        """
        if not self._is_initialized:
            await self.initialize()
        
        try:
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                query = """
                MATCH path = shortestPath((start:Entity {id: $start_id})-[*1..{max_depth}]-(end:Entity {id: $end_id}))
                RETURN path, length(path) AS path_length
                ORDER BY path_length
                LIMIT 1
                """.format(max_depth=max_depth)
                
                result = await session.run(query, start_id=start_entity_id, end_id=end_entity_id)
                record = await result.single()
                
                if record:
                    path_info = {
                        "path_length": record["path_length"],
                        "path": record["path"],
                        "start_entity_id": start_entity_id,
                        "end_entity_id": end_entity_id
                    }
                    return [path_info]
                else:
                    return None
        
        except Exception as e:
            error_msg = f"查找知识路径失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg)
    
    async def get_recent_episodes(self, days: int = 7, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取最近的Episode
        
        Args:
            days: 天数范围
            limit: 返回数量限制
            
        Returns:
            List[Dict[str, Any]]: 最近Episode列表
        """
        if not self._is_initialized:
            await self.initialize()
        
        try:
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                query = """
                MATCH (e:Episode)
                WHERE datetime(e.created_at) > datetime() - duration({days: $days})
                RETURN e
                ORDER BY e.created_at DESC
                LIMIT $limit
                """
                
                result = await session.run(query, days=days, limit=limit)
                records = await result.data()
                
                return [dict(record["e"]) for record in records]
        
        except Exception as e:
            error_msg = f"获取最近Episode失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg)

    # ================== 图谱可视化数据生成 ==================
    
    async def get_graph_data(self, 
                           node_types: Optional[List[NodeType]] = None,
                           limit: int = 100,
                           include_metadata: bool = True,
                           layout_algorithm: str = "force-directed",
                           color_scheme: str = "default") -> Dict[str, Any]:
        """
        生成vis.js兼容的图谱数据
        
        Args:
            node_types: 要包含的节点类型列表
            limit: 节点数量限制
            include_metadata: 是否包含详细元数据
            layout_algorithm: 布局算法 (force-directed, hierarchical, circular)
            color_scheme: 色彩方案 (default, dark, colorful)
            
        Returns:
            Dict[str, Any]: vis.js兼容的图数据结构
            
        Raises:
            NodeOperationError: 数据获取失败时
        """
        if not self._is_initialized:
            await self.initialize()
        
        try:
            start_time = time.time()
            logger.info(f"开始生成图谱数据 - 限制: {limit}个节点, 布局: {layout_algorithm}")
            
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                # 构建节点类型过滤
                node_filter = ""
                if node_types:
                    type_labels = [f"n:{node_type.value}" for node_type in node_types]
                    node_filter = f"WHERE ({' OR '.join(type_labels)})"
                
                # 获取节点数据
                nodes_query = f"""
                MATCH (n)
                {node_filter}
                RETURN n, labels(n)[0] AS node_type, id(n) AS neo4j_id
                ORDER BY n.created_at DESC
                LIMIT $limit
                """
                
                result = await session.run(nodes_query, limit=limit)
                node_records = await result.data()
                
                # 获取节点ID列表用于查询边
                node_ids = [record["n"]["id"] for record in node_records]
                
                # 获取边数据（只包含选中节点间的边）
                edges_query = """
                MATCH (source)-[r]->(target)
                WHERE source.id IN $node_ids AND target.id IN $node_ids
                RETURN source.id AS source_id, target.id AS target_id, 
                       type(r) AS relationship_type, r, id(r) AS neo4j_rel_id
                """
                
                edges_result = await session.run(edges_query, node_ids=node_ids)
                edge_records = await edges_result.data()
                
                # 生成节点数据
                nodes = []
                color_map = self._get_color_scheme(color_scheme)
                
                for record in node_records:
                    node = record["n"]
                    # 安全地将neo4j节点转换为字典
                    node_data = dict(node.items()) if hasattr(node, 'items') else {}
                    node_type = record["node_type"]
                    
                    # 计算节点大小（基于重要性或置信度）
                    importance = node_data.get("importance_score", node_data.get("confidence", 0.5))
                    size = max(10, min(50, 10 + importance * 30))
                    
                    # 获取节点标签
                    label = (
                        node_data.get("title") or 
                        node_data.get("name") or 
                        node_data.get("content", "")[:30] + "..."
                    )
                    
                    vis_node = {
                        "id": node_data["id"],
                        "label": label,
                        "group": node_type,
                        "size": size,
                        "color": {
                            "background": color_map.get(node_type, color_map["default"]),
                            "border": "#2B5797",
                            "highlight": {
                                "background": "#FFA500",
                                "border": "#FF6600"
                            }
                        },
                        "font": {
                            "size": max(12, min(20, 12 + importance * 8)),
                            "color": "#343434"
                        },
                        "shape": self._get_node_shape(node_type)
                    }
                    
                    if include_metadata:
                        vis_node["title"] = self._generate_node_tooltip(node_data, node_type)
                        
                        # 安全地处理properties字段
                        properties = node_data.get("properties", {})
                        if isinstance(properties, str):
                            try:
                                properties = json.loads(properties) if properties else {}
                            except json.JSONDecodeError:
                                properties = {}
                        elif not isinstance(properties, dict):
                            properties = {}
                        
                        vis_node["metadata"] = {
                            "node_type": node_type,
                            "created_at": node_data.get("created_at"),
                            "confidence": node_data.get("confidence"),
                            "properties": properties
                        }
                    
                    nodes.append(vis_node)
                
                # 生成边数据
                edges = []
                
                for record in edge_records:
                    rel = record.get("r", {})
                    # 安全地将neo4j关系转换为字典
                    rel_data = dict(rel.items()) if hasattr(rel, 'items') else {}
                    relationship_type = record["relationship_type"]
                    
                    # 计算边宽度（基于权重）
                    weight = rel_data.get("weight", 1.0)
                    width = max(1, min(10, weight * 5))
                    
                    vis_edge = {
                        "id": rel_data.get("id", f"{record['source_id']}-{record['target_id']}"),
                        "from": record["source_id"],
                        "to": record["target_id"],
                        "label": relationship_type,
                        "width": width,
                        "color": {
                            "color": "#848484",
                            "highlight": "#FF6600"
                        },
                        "arrows": {
                            "to": {
                                "enabled": True,
                                "scaleFactor": 0.5
                            }
                        },
                        "font": {
                            "size": 10,
                            "color": "#343434"
                        }
                    }
                    
                    if include_metadata:
                        vis_edge["title"] = self._generate_edge_tooltip(rel_data, relationship_type)
                        vis_edge["metadata"] = {
                            "relationship_type": relationship_type,
                            "weight": weight,
                            "confidence": rel_data.get("confidence"),
                            "created_at": rel_data.get("created_at")
                        }
                    
                    edges.append(vis_edge)
                
                # 生成布局配置
                layout_config = self._get_layout_config(layout_algorithm)
                
                # 统计信息
                statistics = {
                    "total_nodes": len(nodes),
                    "total_edges": len(edges),
                    "node_types": {},
                    "relationship_types": {}
                }
                
                # 统计节点类型
                for node in nodes:
                    node_type = node["group"]
                    statistics["node_types"][node_type] = statistics["node_types"].get(node_type, 0) + 1
                
                # 统计关系类型
                for edge in edges:
                    rel_type = edge["label"]
                    statistics["relationship_types"][rel_type] = statistics["relationship_types"].get(rel_type, 0) + 1
                
                processing_time = time.time() - start_time
                
                graph_data = {
                    "nodes": nodes,
                    "edges": edges,
                    "layout": layout_config,
                    "statistics": statistics,
                    "metadata": {
                        "generated_at": datetime.now(timezone.utc).isoformat(),
                        "processing_time_seconds": processing_time,
                        "color_scheme": color_scheme,
                        "layout_algorithm": layout_algorithm,
                        "node_limit": limit,
                        "include_metadata": include_metadata
                    }
                }
                
                logger.info(f"✅ 图谱数据生成完成 - 节点: {len(nodes)}, 边: {len(edges)}, 耗时: {processing_time:.2f}s")
                
                return graph_data
        
        except Exception as e:
            error_msg = f"图谱数据生成失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg)

    def _get_color_scheme(self, color_scheme: str) -> Dict[str, str]:
        """获取颜色方案映射"""
        color_schemes = {
            "default": {
                "Episode": "#4285F4",     # 蓝色
                "Entity": "#34A853",      # 绿色  
                "Statement": "#FBBC04",   # 黄色
                "Concept": "#EA4335",     # 红色
                "Event": "#9AA0A6",       # 灰色
                "default": "#4285F4"
            },
            "dark": {
                "Episode": "#1A73E8",     # 深蓝色
                "Entity": "#137333",      # 深绿色
                "Statement": "#F29900",   # 橙色
                "Concept": "#C5221F",     # 深红色
                "Event": "#5F6368",       # 深灰色
                "default": "#1A73E8"
            },
            "colorful": {
                "Episode": "#FF6B6B",     # 珊瑚色
                "Entity": "#4ECDC4",      # 青蓝色
                "Statement": "#45B7D1",   # 天蓝色
                "Concept": "#96CEB4",     # 薄荷绿
                "Event": "#FECA57",       # 金黄色
                "default": "#FF6B6B"
            }
        }
        return color_schemes.get(color_scheme, color_schemes["default"])
    
    def _get_node_shape(self, node_type: str) -> str:
        """获取节点形状"""
        shape_mapping = {
            "Episode": "box",
            "Entity": "dot", 
            "Statement": "diamond",
            "Concept": "triangle",
            "Event": "star"
        }
        return shape_mapping.get(node_type, "dot")
    
    def _get_layout_config(self, layout_algorithm: str) -> Dict[str, Any]:
        """获取布局配置"""
        layout_configs = {
            "force-directed": {
                "randomSeed": 2,
                "improvedLayout": True,
                "clusterThreshold": 150,
                "levelSeparation": 150,
                "nodeDistance": 100,
                "springLength": 200,
                "springConstant": 0.04,
                "damping": 0.09,
                "avoidOverlap": 0.1
            },
            "hierarchical": {
                "enabled": True,
                "levelSeparation": 150,
                "nodeSpacing": 100,
                "treeSpacing": 200,
                "blockShifting": True,
                "edgeMinimization": True,
                "parentCentralization": True,
                "direction": "UD",
                "sortMethod": "directed"
            },
            "circular": {
                "randomSeed": 2,
                "improvedLayout": True,
                "hierarchical": {
                    "enabled": False
                },
                "stabilization": {
                    "enabled": True,
                    "iterations": 1000,
                    "updateInterval": 25
                }
            }
        }
        return layout_configs.get(layout_algorithm, layout_configs["force-directed"])
    
    def _generate_node_tooltip(self, node_data: Dict[str, Any], node_type: str) -> str:
        """生成节点提示信息"""
        tooltip_parts = [
            f"<b>{node_type}</b>",
            f"ID: {node_data.get('id', 'N/A')[:20]}...",
        ]
        
        if node_data.get("name"):
            tooltip_parts.append(f"名称: {node_data['name']}")
        
        if node_data.get("type"):
            tooltip_parts.append(f"类型: {node_data['type']}")
        
        if node_data.get("confidence"):
            tooltip_parts.append(f"置信度: {node_data['confidence']:.2f}")
        
        if node_data.get("created_at"):
            tooltip_parts.append(f"创建时间: {node_data['created_at'][:10]}")
        
        if node_data.get("content"):
            content_preview = node_data["content"][:100] + "..." if len(node_data["content"]) > 100 else node_data["content"]
            tooltip_parts.append(f"内容: {content_preview}")
        
        return "<br>".join(tooltip_parts)
    
    def _generate_edge_tooltip(self, rel_data: Dict[str, Any], relationship_type: str) -> str:
        """生成边提示信息"""
        tooltip_parts = [
            f"<b>关系: {relationship_type}</b>",
        ]
        
        if rel_data.get("weight"):
            tooltip_parts.append(f"权重: {rel_data['weight']:.2f}")
        
        if rel_data.get("confidence"):
            tooltip_parts.append(f"置信度: {rel_data['confidence']:.2f}")
        
        if rel_data.get("created_at"):
            tooltip_parts.append(f"创建时间: {rel_data['created_at'][:10]}")
        
        if rel_data.get("predicate"):
            tooltip_parts.append(f"谓语: {rel_data['predicate']}")
        
        return "<br>".join(tooltip_parts)

    async def get_graph_statistics(self) -> Dict[str, Any]:
        """获取图数据库统计信息"""
        if not self._is_initialized:
            await self.initialize()
        
        try:
            async with self.driver.session(database=self.neo4j_config["database"]) as session:
                stats_query = """
                MATCH (n)
                RETURN 
                    labels(n)[0] AS node_type,
                    count(n) AS count
                """
                
                result = await session.run(stats_query)
                node_stats = await result.data()
                
                # 获取关系统计
                rel_stats_query = """
                MATCH ()-[r]->()
                RETURN type(r) AS relationship_type, count(r) AS count
                """
                
                rel_result = await session.run(rel_stats_query)
                rel_stats = await rel_result.data()
                
                return {
                    "nodes": {record["node_type"]: record["count"] for record in node_stats},
                    "relationships": {record["relationship_type"]: record["count"] for record in rel_stats},
                    "total_nodes": sum(record["count"] for record in node_stats),
                    "total_relationships": sum(record["count"] for record in rel_stats),
                    "last_updated": datetime.now(timezone.utc).isoformat()
                }
        
        except Exception as e:
            error_msg = f"获取图统计信息失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg)
    
    async def cleanup(self):
        """清理资源和关闭连接"""
        logger.info("开始清理Neo4j服务资源...")
        
        try:
            if self.driver:
                await self.driver.close()
                self.driver = None
                logger.debug("Neo4j驱动连接已关闭")
            
            # 重置状态
            self._is_initialized = False
            self._service_available = False
            self._gds_available = False
            self._last_health_check = 0
            self._query_cache.clear()
            
            logger.info("✅ Neo4j服务资源清理完成")
        
        except Exception as e:
            logger.error(f"❌ Neo4j服务资源清理异常: {e}")
    
    @asynccontextmanager
    async def managed_service(self):
        """上下文管理器，自动管理服务生命周期"""
        try:
            await self.initialize()
            yield self
        finally:
            await self.cleanup()
    
    def get_service_info(self) -> Dict[str, Any]:
        """获取服务状态信息"""
        return {
            "service_name": "Neo4j知识图谱服务",
            "version": "v2.0",
            "is_initialized": self._is_initialized,
            "service_available": self._service_available,
            "gds_available": self._gds_available,
            "last_health_check": self._last_health_check,
            "neo4j_config": {
                "uri": self.neo4j_config["uri"],
                "database": self.neo4j_config["database"],
                "max_connection_pool_size": self.neo4j_config["max_connection_pool_size"],
                "connection_timeout": self.neo4j_config["connection_timeout"],
            },
            "cache_info": {
                "cache_size": len(self._query_cache),
                "max_cache_size": self._cache_max_size,
                "cache_ttl": self._cache_ttl
            }
        }


# ================== 全局服务实例 ==================

_knowledge_service_instance: Optional[KnowledgeGraphService] = None


def get_knowledge_service() -> KnowledgeGraphService:
    """获取知识图谱服务实例（单例模式）"""
    global _knowledge_service_instance
    if _knowledge_service_instance is None:
        _knowledge_service_instance = KnowledgeGraphService()
    return _knowledge_service_instance


# ================== 便捷函数接口 ==================

async def create_knowledge_episode(episode: Episode, entities: List[Entity], 
                                 statements: List[Statement]) -> Dict[str, Any]:
    """便捷函数：创建完整的知识Episode"""
    service = get_knowledge_service()
    return await service.create_episode_with_knowledge(episode, entities, statements)


async def get_graph_stats() -> Dict[str, Any]:
    """便捷函数：获取图数据库统计信息"""
    service = get_knowledge_service()
    return await service.get_graph_statistics()


if __name__ == "__main__":
    """知识图谱服务模块测试"""
    
    async def test_knowledge_service():
        """测试知识图谱服务功能"""
        print("🚀 开始测试Neo4j知识图谱服务...")
        
        try:
            # 获取服务实例
            service = get_knowledge_service()
            print(f"📊 服务信息: {service.get_service_info()}")
            
            # 使用上下文管理器测试
            async with service.managed_service():
                print("✅ Neo4j服务初始化成功")
                
                # 测试健康检查
                is_healthy = await service.is_service_healthy()
                print(f"🏥 服务健康状态: {'✅ 健康' if is_healthy else '❌ 异常'}")
                
                if is_healthy:
                    # 测试创建Episode
                    test_episode = Episode(
                        content="这是一个测试情节，用于验证Neo4j图数据库功能。",
                        title="测试情节",
                        source=ContentSource.MANUAL,
                        processing_status=ProcessingStatus.COMPLETED
                    )
                    
                    episode_id = await service.create_episode(test_episode)
                    print(f"✅ 测试Episode创建成功: {episode_id}")
                    
                    # 测试获取Episode
                    retrieved_episode = await service.get_episode(episode_id)
                    if retrieved_episode:
                        print(f"✅ Episode获取成功: {retrieved_episode['title']}")
                    
                    # 测试图统计信息
                    stats = await service.get_graph_statistics()
                    print(f"📊 图统计信息: {stats}")
                    
                    print("🎉 Neo4j知识图谱服务测试完成！")
                else:
                    print("⚠️ Neo4j服务不健康，跳过详细测试")
        
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 运行测试
    asyncio.run(test_knowledge_service())