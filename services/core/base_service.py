"""
智能记忆引擎 - 基础服务类

所有服务的基础抽象类，提供通用功能：
- 服务初始化和清理
- 健康检查机制
- 错误处理和日志记录
- 配置管理
- 异步上下文管理

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 14:06:34
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager
from datetime import datetime, timezone
from enum import Enum

class ServiceStatus(Enum):
    """服务状态枚举"""
    INITIALIZING = "initializing"
    READY = "ready"
    RUNNING = "running"
    ERROR = "error"
    STOPPED = "stopped"

class ServiceHealthCheck:
    """服务健康检查结果"""
    def __init__(
        self,
        service_name: str,
        status: ServiceStatus,
        message: str = "",
        details: Optional[Dict[str, Any]] = None,
        response_time: Optional[float] = None
    ):
        self.service_name = service_name
        self.status = status
        self.message = message
        self.details = details or {}
        self.response_time = response_time
        self.timestamp = datetime.now(timezone.utc)
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "service_name": self.service_name,
            "status": self.status.value,
            "message": self.message,
            "details": self.details,
            "response_time": self.response_time,
            "timestamp": self.timestamp.isoformat()
        }

    def is_healthy(self) -> bool:
        """检查服务是否健康"""
        return self.status in [ServiceStatus.READY, ServiceStatus.RUNNING]

class BaseService(ABC):
    """
    所有服务的基础抽象类

    提供通用功能：
    - 服务生命周期管理
    - 健康检查机制
    - 错误处理和日志记录
    - 配置管理
    - 性能监控
    """

    def __init__(
        self,
        service_name: str,
        config: Optional[Dict[str, Any]] = None,
        logger: Optional[logging.Logger] = None
    ):
        self.service_name = service_name
        self.config = config or {}
        self.logger = logger or logging.getLogger(f"smart_memory.{service_name}")

        # 服务状态
        self._status = ServiceStatus.INITIALIZING
        self._initialized = False
        self._error_count = 0
        self._last_error = None
        self._start_time = None
        self._initialization_time = None

        # 性能指标
        self._metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_response_time": 0.0,
            "last_response_time": 0.0
        }

        self.logger.info(f"初始化服务: {self.service_name}")

    @property
    def status(self) -> ServiceStatus:
        """获取服务当前状态"""
        return self._status

    @property
    def is_ready(self) -> bool:
        """检查服务是否已就绪"""
        return self._status in [ServiceStatus.READY, ServiceStatus.RUNNING]

    @property
    def uptime(self) -> Optional[float]:
        """获取服务运行时间（秒）"""
        if self._start_time:
            return time.time() - self._start_time
        return None

    @property
    def metrics(self) -> Dict[str, Any]:
        """获取服务性能指标"""
        return self._metrics.copy()

    async def initialize(self) -> None:
        """
        初始化服务

        子类应重写 _initialize_service 方法来实现具体的初始化逻辑
        """
        if self._initialized:
            self.logger.warning(f"服务 {self.service_name} 已经初始化")
            return

        try:
            self._status = ServiceStatus.INITIALIZING
            self.logger.info(f"开始初始化服务: {self.service_name}")

            start_time = time.time()
            await self._initialize_service()
            self._initialization_time = time.time() - start_time

            self._initialized = True
            self._status = ServiceStatus.READY
            self._start_time = time.time()

            self.logger.info(
                f"服务 {self.service_name} 初始化完成，耗时: {self._initialization_time:.2f}s"
            )

        except Exception as e:
            self._status = ServiceStatus.ERROR
            self._last_error = str(e)
            self._error_count += 1
            self.logger.error(f"服务 {self.service_name} 初始化失败: {e}", exc_info=True)
            raise

    async def cleanup(self) -> None:
        """
        清理服务资源

        子类应重写 _cleanup_service 方法来实现具体的清理逻辑
        """
        if not self._initialized:
            return

        try:
            self.logger.info(f"开始清理服务: {self.service_name}")
            await self._cleanup_service()

            self._initialized = False
            self._status = ServiceStatus.STOPPED

            self.logger.info(f"服务 {self.service_name} 清理完成")

        except Exception as e:
            self._last_error = str(e)
            self._error_count += 1
            self.logger.error(f"服务 {self.service_name} 清理失败: {e}", exc_info=True)
            raise

    async def health_check(self) -> ServiceHealthCheck:
        """
        执行健康检查

        子类应重写 _perform_health_check 方法来实现具体的健康检查逻辑
        """
        start_time = time.time()

        try:
            # 基础状态检查
            if not self._initialized:
                return ServiceHealthCheck(
                    service_name=self.service_name,
                    status=ServiceStatus.ERROR,
                    message="服务未初始化",
                    response_time=time.time() - start_time
                )

            # 执行具体的健康检查
            result = await self._perform_health_check()
            result.response_time = time.time() - start_time

            return result

        except Exception as e:
            self._last_error = str(e)
            self._error_count += 1
            self.logger.error(f"健康检查失败: {e}", exc_info=True)

            return ServiceHealthCheck(
                service_name=self.service_name,
                status=ServiceStatus.ERROR,
                message=f"健康检查异常: {str(e)}",
                response_time=time.time() - start_time
            )

    def update_metrics(self, success: bool, response_time: float) -> None:
        """更新服务性能指标"""
        self._metrics["total_requests"] += 1
        self._metrics["last_response_time"] = response_time

        if success:
            self._metrics["successful_requests"] += 1
        else:
            self._metrics["failed_requests"] += 1

        # 更新平均响应时间
        total_requests = self._metrics["total_requests"]
        current_avg = self._metrics["average_response_time"]
        self._metrics["average_response_time"] = (
            (current_avg * (total_requests - 1) + response_time) / total_requests
        )

    def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        return {
            "service_name": self.service_name,
            "status": self._status.value,
            "initialized": self._initialized,
            "uptime": self.uptime,
            "initialization_time": self._initialization_time,
            "error_count": self._error_count,
            "last_error": self._last_error,
            "metrics": self._metrics,
            "config_keys": list(self.config.keys())
        }

    @asynccontextmanager
    async def service_context(self):
        """服务异步上下文管理器"""
        try:
            if not self._initialized:
                await self.initialize()

            self._status = ServiceStatus.RUNNING
            yield self

        except Exception as e:
            self._status = ServiceStatus.ERROR
            self._last_error = str(e)
            self._error_count += 1
            raise
        finally:
            if self._status == ServiceStatus.RUNNING:
                self._status = ServiceStatus.READY

    # ========== 抽象方法：子类必须实现 ==========

    @abstractmethod
    async def _initialize_service(self) -> None:
        """
        初始化服务的具体实现
        子类必须重写此方法
        """
        pass

    @abstractmethod
    async def _cleanup_service(self) -> None:
        """
        清理服务的具体实现
        子类必须重写此方法
        """
        pass

    @abstractmethod
    async def _perform_health_check(self) -> ServiceHealthCheck:
        """
        执行健康检查的具体实现
        子类必须重写此方法
        """
        pass

    # ========== 可选重写的方法 ==========

    async def on_error(self, error: Exception) -> None:
        """
        错误处理回调
        子类可以重写此方法来处理特定的错误情况
        """
        self.logger.error(f"服务 {self.service_name} 发生错误: {error}", exc_info=True)

    async def on_status_change(self, old_status: ServiceStatus, new_status: ServiceStatus) -> None:
        """
        状态变更回调
        子类可以重写此方法来处理状态变更
        """
        self.logger.info(f"服务 {self.service_name} 状态变更: {old_status.value} -> {new_status.value}")

class AsyncServiceMixin:
    """异步服务混入类，提供通用的异步操作模式"""

    async def execute_with_retry(
        self,
        operation,
        max_retries: int = 3,
        delay: float = 1.0,
        backoff_factor: float = 2.0
    ):
        """
        带重试机制的异步操作执行

        Args:
            operation: 要执行的异步操作
            max_retries: 最大重试次数
            delay: 初始延迟时间
            backoff_factor: 退避因子
        """
        current_delay = delay

        for attempt in range(max_retries + 1):
            try:
                return await operation()
            except Exception as e:
                if attempt == max_retries:
                    raise

                if hasattr(self, 'logger'):
                    self.logger.warning(
                        f"操作失败，第 {attempt + 1} 次重试，{current_delay:.1f}s 后重试: {e}"
                    )

                await asyncio.sleep(current_delay)
                current_delay *= backoff_factor

    async def execute_with_timeout(
        self,
        operation,
        timeout: float = 30.0
    ):
        """
        带超时的异步操作执行

        Args:
            operation: 要执行的异步操作
            timeout: 超时时间（秒）
        """
        try:
            return await asyncio.wait_for(operation(), timeout=timeout)
        except asyncio.TimeoutError:
            raise Exception(f"操作超时 ({timeout}s)")
