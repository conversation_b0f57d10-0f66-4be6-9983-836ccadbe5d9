"""
智能记忆引擎 - AI服务协调器

统一管理所有AI相关服务的核心协调器：
- 集成embedding、llm、extraction三个服务模块
- 提供extract_knowledge统一接口  
- 支持LLM+jieba fallback机制
- 完整的依赖注入和服务生命周期管理
- 继承BaseService提供标准服务接口

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 15:33:51
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional

from ..core.base_service import BaseService, ServiceHealthCheck, ServiceStatus
from .embedding import get_bge_service, BGEEmbeddingService, BGEEmbeddingError
from .llm import get_openai_service, OpenAIService, LLMError, LLMConnectionError
from .extraction import (
    LLMEntityExtractor, JiebaFallbackExtractor as JiebaEntityExtractor, 
    LLMStatementExtractor, JiebaFallbackExtractor as JiebaStatementExtractor,
    EntityExtractionError, StatementExtractionError
)
from config import settings


class AIServiceOrchestrator(BaseService):
    """
    AI服务协调器
    
    核心功能：
    1. 统一管理embedding、LLM、extraction服务
    2. 提供extract_knowledge统一接口
    3. 智能fallback机制（LLM -> jieba）
    4. 依赖注入和服务编排
    5. 完整的生命周期管理
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, logger: Optional[logging.Logger] = None):
        super().__init__(
            service_name="ai_service_orchestrator",
            config=config or {},
            logger=logger or logging.getLogger("smart_memory.ai_orchestrator")
        )
        
        # 核心子服务
        self.embedding_service: Optional[BGEEmbeddingService] = None
        self.llm_service: Optional[OpenAIService] = None
        
        # 实体提取器
        self.llm_entity_extractor: Optional[LLMEntityExtractor] = None
        self.jieba_entity_extractor: Optional[JiebaEntityExtractor] = None
        
        # 陈述提取器  
        self.llm_statement_extractor: Optional[LLMStatementExtractor] = None
        self.jieba_statement_extractor: Optional[JiebaStatementExtractor] = None
        
        # 配置参数
        self.enable_llm_fallback = self.config.get("enable_llm_fallback", True)
        self.auto_generate_embeddings = self.config.get("auto_generate_embeddings", True)
        self.prefer_llm_extraction = self.config.get("prefer_llm_extraction", True)
        
        # 性能阈值
        self.llm_timeout_threshold = self.config.get("llm_timeout_threshold", 30.0)  # 秒
        self.embedding_batch_size = self.config.get("embedding_batch_size", 32)
        
        # 实体类型定义
        self.entity_types = [
            "Person",       # 人名
            "Organization", # 组织机构
            "Location",     # 地点
            "Concept",      # 概念
            "Event",        # 事件
            "Product",      # 产品
            "Time"          # 时间
        ]
    
    async def _initialize_service(self) -> None:
        """初始化AI服务协调器的所有子服务"""
        try:
            self.logger.info("开始初始化AI服务协调器...")
            
            # 1. 初始化embedding服务（必需）
            await self._initialize_embedding_service()
            
            # 2. 初始化LLM服务（可选，支持fallback）
            await self._initialize_llm_service()
            
            # 3. 初始化提取器服务
            await self._initialize_extractors()
            
            # 4. 验证服务依赖关系
            await self._validate_service_dependencies()
            
            self.logger.info("✅ AI服务协调器初始化完成")
            
        except Exception as e:
            self.logger.error(f"AI服务协调器初始化失败: {e}")
            raise
    
    async def _initialize_embedding_service(self) -> None:
        """初始化BGE-M3向量服务"""
        try:
            self.logger.info("初始化BGE-M3向量服务...")
            
            # 获取向量服务实例
            self.embedding_service = get_bge_service()
            
            # 检查服务可用性
            if not await self.embedding_service.initialize():
                raise Exception("BGE-M3向量服务初始化失败")
            
            # 验证服务健康状态
            if not await self.embedding_service.is_service_healthy():
                raise Exception("BGE-M3向量服务健康检查失败")
                
            self.logger.info("✅ BGE-M3向量服务初始化成功")
            
        except Exception as e:
            self.logger.error(f"BGE-M3向量服务初始化失败: {e}")
            raise
    
    async def _initialize_llm_service(self) -> None:
        """初始化LLM服务（支持fallback）"""
        try:
            self.logger.info("初始化LLM服务...")
            
            # 获取LLM服务实例
            self.llm_service = get_openai_service()
            
            # 检查API密钥配置
            openai_config = settings.get_openai_config()
            if not openai_config["api_key"]:
                self.logger.warning("OpenAI API密钥未配置，将使用jieba fallback模式")
                self.llm_service = None
                return
            
            # 初始化LLM服务
            await self.llm_service.initialize()
            
            # 验证服务健康状态
            health_check = await self.llm_service.health_check()
            if not health_check.is_healthy():
                self.logger.warning(f"LLM服务健康检查失败: {health_check.message}")
                if not self.enable_llm_fallback:
                    raise Exception("LLM服务不可用且未启用fallback")
                else:
                    self.logger.info("将使用jieba fallback模式")
                    self.llm_service = None
                    return
            
            self.logger.info(f"✅ LLM服务初始化成功 - 模型: {openai_config['model']}")
            
        except Exception as e:
            self.logger.error(f"LLM服务初始化失败: {e}")
            if not self.enable_llm_fallback:
                raise
            else:
                self.logger.warning("LLM服务初始化失败，继续使用jieba fallback模式")
                self.llm_service = None
    
    async def _initialize_extractors(self) -> None:
        """初始化所有提取器服务"""
        try:
            self.logger.info("初始化提取器服务...")
            
            # 初始化LLM提取器（如果LLM服务可用）
            if self.llm_service and self.llm_service.is_ready:
                try:
                    self.llm_entity_extractor = LLMEntityExtractor(
                        config={"llm_service": self.llm_service, "embedding_service": self.embedding_service}
                    )
                    await self.llm_entity_extractor.initialize()
                    
                    self.llm_statement_extractor = LLMStatementExtractor(
                        config={"llm_service": self.llm_service, "embedding_service": self.embedding_service}
                    )
                    await self.llm_statement_extractor.initialize()
                    
                    self.logger.info("✅ LLM提取器初始化成功")
                    
                except Exception as e:
                    self.logger.warning(f"LLM提取器初始化失败: {e}")
                    if not self.enable_llm_fallback:
                        raise
                    # 继续初始化jieba提取器作为fallback
            
            # 初始化jieba提取器（始终可用的fallback）
            try:
                self.jieba_entity_extractor = JiebaEntityExtractor(
                    config={"embedding_service": self.embedding_service}
                )
                await self.jieba_entity_extractor.initialize()
                
                self.jieba_statement_extractor = JiebaStatementExtractor(
                    config={"embedding_service": self.embedding_service}
                )
                await self.jieba_statement_extractor.initialize()
                
                self.logger.info("✅ Jieba提取器初始化成功")
                
            except Exception as e:
                self.logger.error(f"Jieba提取器初始化失败: {e}")
                raise
                
            self.logger.info("✅ 所有提取器服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"提取器服务初始化失败: {e}")
            raise
    
    async def _validate_service_dependencies(self) -> None:
        """验证服务依赖关系"""
        try:
            self.logger.info("验证服务依赖关系...")
            
            # 验证必需服务
            if not self.embedding_service:
                raise Exception("BGE-M3向量服务未初始化")
            
            if not (self.jieba_entity_extractor and self.jieba_statement_extractor):
                raise Exception("Jieba提取器未初始化")
            
            # 验证至少有一种提取方式可用
            has_llm_extractors = (self.llm_entity_extractor and self.llm_statement_extractor)
            has_jieba_extractors = (self.jieba_entity_extractor and self.jieba_statement_extractor)
            
            if not (has_llm_extractors or has_jieba_extractors):
                raise Exception("没有可用的提取器")
            
            # 记录可用的服务
            available_services = []
            if self.llm_service:
                available_services.append("LLM")
            if self.jieba_entity_extractor:
                available_services.append("Jieba")
            
            self.logger.info(f"✅ 可用服务: {', '.join(available_services)}")
            
        except Exception as e:
            self.logger.error(f"服务依赖验证失败: {e}")
            raise
    
    async def _cleanup_service(self) -> None:
        """清理所有子服务"""
        self.logger.info("开始清理AI服务协调器...")
        
        # 清理提取器
        extractors = [
            self.llm_entity_extractor,
            self.llm_statement_extractor,
            self.jieba_entity_extractor,
            self.jieba_statement_extractor
        ]
        
        for extractor in extractors:
            if extractor:
                try:
                    await extractor.cleanup()
                except Exception as e:
                    self.logger.warning(f"清理提取器失败: {e}")
        
        # 清理核心服务
        if self.llm_service:
            try:
                await self.llm_service.cleanup()
            except Exception as e:
                self.logger.warning(f"清理LLM服务失败: {e}")
        
        if self.embedding_service:
            try:
                await self.embedding_service.cleanup()
            except Exception as e:
                self.logger.warning(f"清理向量服务失败: {e}")
        
        # 重置所有服务引用
        self.embedding_service = None
        self.llm_service = None
        self.llm_entity_extractor = None
        self.llm_statement_extractor = None
        self.jieba_entity_extractor = None
        self.jieba_statement_extractor = None
        
        self.logger.info("✅ AI服务协调器清理完成")
    
    async def _perform_health_check(self) -> ServiceHealthCheck:
        """执行健康检查"""
        try:
            start_time = time.time()
            details = {}
            all_healthy = True
            messages = []
            
            # 检查BGE-M3向量服务
            if self.embedding_service:
                try:
                    is_healthy = await self.embedding_service.is_service_healthy()
                    details["embedding_service"] = {
                        "status": "healthy" if is_healthy else "unhealthy",
                        "service_info": self.embedding_service.get_service_info()
                    }
                    if not is_healthy:
                        all_healthy = False
                        messages.append("BGE-M3向量服务不健康")
                except Exception as e:
                    details["embedding_service"] = {"status": "error", "error": str(e)}
                    all_healthy = False
                    messages.append(f"BGE-M3向量服务检查失败: {e}")
            else:
                details["embedding_service"] = {"status": "not_initialized"}
                all_healthy = False
                messages.append("BGE-M3向量服务未初始化")
            
            # 检查LLM服务
            if self.llm_service:
                try:
                    health_check = await self.llm_service.health_check()
                    details["llm_service"] = {
                        "status": health_check.status.value,
                        "message": health_check.message,
                        "service_info": self.llm_service.get_service_info()
                    }
                    if not health_check.is_healthy():
                        messages.append(f"LLM服务不健康: {health_check.message}")
                        # LLM服务不健康不影响整体状态（有fallback）
                except Exception as e:
                    details["llm_service"] = {"status": "error", "error": str(e)}
                    messages.append(f"LLM服务检查失败: {e}")
            else:
                details["llm_service"] = {
                    "status": "not_configured", 
                    "fallback": "jieba_available"
                }
                messages.append("LLM服务未配置，使用jieba fallback")
            
            # 检查提取器状态
            extractors_status = {
                "llm_entity_extractor": self.llm_entity_extractor is not None,
                "llm_statement_extractor": self.llm_statement_extractor is not None,
                "jieba_entity_extractor": self.jieba_entity_extractor is not None,
                "jieba_statement_extractor": self.jieba_statement_extractor is not None
            }
            details["extractors"] = extractors_status
            
            # 至少要有jieba提取器可用
            if not (extractors_status["jieba_entity_extractor"] and extractors_status["jieba_statement_extractor"]):
                all_healthy = False
                messages.append("Jieba提取器不可用")
            
            # 确定整体状态
            if all_healthy:
                status = ServiceStatus.READY
                message = "所有AI服务运行正常"
            else:
                # 如果有jieba fallback，降级但仍可用
                if extractors_status["jieba_entity_extractor"] and extractors_status["jieba_statement_extractor"]:
                    status = ServiceStatus.READY  # 降级但可用
                    message = f"服务降级运行: {'; '.join(messages)}"
                else:
                    status = ServiceStatus.ERROR
                    message = f"服务不可用: {'; '.join(messages)}"
            
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=status,
                message=message,
                details=details,
                response_time=time.time() - start_time
            )
            
        except Exception as e:
            self.logger.error(f"AI服务协调器健康检查失败: {e}")
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=ServiceStatus.ERROR,
                message=f"健康检查异常: {str(e)}",
                response_time=time.time() - start_time
            )
    
    # ================== 核心业务接口 ==================
    
    async def extract_knowledge(self, content: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        统一知识提取接口
        
        核心工作流程：
        1. 生成内容向量
        2. 提取实体（LLM优先，jieba fallback）
        3. 提取陈述（LLM优先，jieba fallback）
        4. 生成知识结构
        
        Args:
            content: 输入文本内容
            context: 上下文信息
            
        Returns:
            Dict[str, Any]: 完整的知识提取结果
            
        Raises:
            Exception: 知识提取失败时
        """
        if not content or not content.strip():
            raise ValueError("内容不能为空")
        
        if not self.is_ready:
            await self.initialize()
        
        start_time = time.time()
        self.logger.info(f"开始知识提取，内容长度: {len(content)}")
        
        try:
            # 1. 生成内容向量
            self.logger.debug("步骤1: 生成内容向量")
            content_embedding = await self._generate_content_embedding(content)
            
            # 2. 提取实体
            self.logger.debug("步骤2: 提取实体")
            entities = await self._extract_entities_with_fallback(content)
            
            # 3. 提取陈述
            self.logger.debug("步骤3: 提取陈述")
            statements = await self._extract_statements_with_fallback(content, entities)
            
            # 4. 计算置信度统计
            entity_confidences = [e.get("confidence", 0.5) for e in entities]
            statement_confidences = [s.get("confidence", 0.5) for s in statements]
            all_confidences = entity_confidences + statement_confidences
            
            # 5. 构建处理统计信息
            processing_time = time.time() - start_time
            processing_stats = {
                "processing_time_seconds": processing_time,
                "entities_extracted": len(entities),
                "statements_extracted": len(statements),
                "avg_confidence": sum(all_confidences) / len(all_confidences) if all_confidences else 0.0,
                "content_length": len(content),
                "extraction_method": self._determine_extraction_method(),
                "embedding_dimension": len(content_embedding) if content_embedding else 0
            }
            
            # 6. 构建完整结果
            result = {
                "content_embedding": content_embedding,
                "entities": entities,
                "statements": statements,
                "confidence_scores": all_confidences,
                "processing_stats": processing_stats,
                "context": context or {}
            }
            
            # 更新性能指标
            self.update_metrics(True, processing_time)
            
            self.logger.info(
                f"知识提取完成 - 实体: {len(entities)}, 陈述: {len(statements)}, "
                f"方法: {processing_stats['extraction_method']}, 耗时: {processing_time:.2f}s"
            )
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.update_metrics(False, processing_time)
            
            error_msg = f"知识提取失败: {e}"
            self.logger.error(error_msg)
            raise Exception(error_msg)
    
    async def _generate_content_embedding(self, content: str) -> Optional[List[float]]:
        """生成内容向量"""
        try:
            if not self.embedding_service:
                self.logger.warning("向量服务不可用，跳过向量生成")
                return None
                
            embedding = await self.embedding_service.get_embedding(content)
            self.logger.debug(f"内容向量生成成功 - 维度: {len(embedding)}")
            return embedding
            
        except Exception as e:
            self.logger.warning(f"内容向量生成失败: {e}")
            return None
    
    async def _extract_entities_with_fallback(self, content: str) -> List[Dict[str, Any]]:
        """实体提取（支持fallback）"""
        entities = []
        
        # 优先尝试LLM提取
        if self.prefer_llm_extraction and self.llm_entity_extractor:
            try:
                self.logger.debug("使用LLM进行实体提取")
                entities = await self.llm_entity_extractor.extract_entities(content)
                self.logger.info(f"✅ LLM实体提取成功 - 提取到 {len(entities)} 个实体")
                return entities
                
            except Exception as e:
                self.logger.warning(f"⚠️ LLM实体提取失败: {e}")
                if not self.enable_llm_fallback:
                    raise EntityExtractionError(f"LLM实体提取失败: {e}")
                
        # Fallback到jieba提取
        if self.jieba_entity_extractor:
            try:
                self.logger.debug("使用jieba fallback进行实体提取")
                entities = await self.jieba_entity_extractor.extract_entities(content)
                self.logger.info(f"✅ Jieba实体提取成功 - 提取到 {len(entities)} 个实体")
                return entities
                
            except Exception as e:
                self.logger.error(f"❌ Jieba实体提取失败: {e}")
                raise EntityExtractionError(f"所有实体提取方法都失败: {e}")
        
        self.logger.warning("没有可用的实体提取器")
        return []
    
    async def _extract_statements_with_fallback(self, content: str, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """陈述提取（支持fallback）"""
        statements = []
        
        # 优先尝试LLM提取
        if self.prefer_llm_extraction and self.llm_statement_extractor:
            try:
                self.logger.debug("使用LLM进行陈述提取")
                statements = await self.llm_statement_extractor.extract_statements(content, entities)
                self.logger.info(f"✅ LLM陈述提取成功 - 提取到 {len(statements)} 个陈述")
                return statements
                
            except Exception as e:
                self.logger.warning(f"⚠️ LLM陈述提取失败: {e}")
                if not self.enable_llm_fallback:
                    raise StatementExtractionError(f"LLM陈述提取失败: {e}")
        
        # Fallback到jieba提取
        if self.jieba_statement_extractor:
            try:
                self.logger.debug("使用jieba fallback进行陈述提取")
                statements = await self.jieba_statement_extractor.extract_statements(content, entities)
                self.logger.info(f"✅ Jieba陈述提取成功 - 提取到 {len(statements)} 个陈述")
                return statements
                
            except Exception as e:
                self.logger.error(f"❌ Jieba陈述提取失败: {e}")
                raise StatementExtractionError(f"所有陈述提取方法都失败: {e}")
        
        self.logger.warning("没有可用的陈述提取器")
        return []
    
    def _determine_extraction_method(self) -> str:
        """确定当前使用的提取方法"""
        if self.llm_entity_extractor and self.llm_statement_extractor:
            return "llm_primary"
        elif self.jieba_entity_extractor and self.jieba_statement_extractor:
            return "jieba_fallback"
        else:
            return "mixed_mode"
    
    # ================== 便捷接口 ==================
    
    async def get_embedding(self, text: str) -> Optional[List[float]]:
        """获取文本向量（便捷接口）"""
        if not self.embedding_service:
            return None
        return await self.embedding_service.get_embedding(text)
    
    async def get_batch_embeddings(self, texts: List[str]) -> List[List[float]]:
        """批量获取文本向量（便捷接口）"""
        if not self.embedding_service:
            return []
        return await self.embedding_service.get_batch_embeddings(texts)
    
    async def extract_entities_only(self, content: str) -> List[Dict[str, Any]]:
        """仅提取实体（便捷接口）"""
        if not self.is_ready:
            await self.initialize()
        return await self._extract_entities_with_fallback(content)
    
    async def extract_statements_only(self, content: str, entities: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """仅提取陈述（便捷接口）"""
        if not self.is_ready:
            await self.initialize()
        if entities is None:
            entities = await self._extract_entities_with_fallback(content)
        return await self._extract_statements_with_fallback(content, entities)
    
    # ================== 服务状态查询 ==================
    
    def is_llm_available(self) -> bool:
        """检查LLM服务是否可用"""
        return self.llm_service is not None and self.llm_service.is_ready
    
    def is_embedding_available(self) -> bool:
        """检查向量服务是否可用"""
        return self.embedding_service is not None
    
    def get_available_extractors(self) -> Dict[str, bool]:
        """获取可用的提取器状态"""
        return {
            "llm_entity_extractor": self.llm_entity_extractor is not None,
            "llm_statement_extractor": self.llm_statement_extractor is not None,
            "jieba_entity_extractor": self.jieba_entity_extractor is not None,
            "jieba_statement_extractor": self.jieba_statement_extractor is not None
        }


# ================== 全局服务实例 ==================

_ai_orchestrator_instance: Optional[AIServiceOrchestrator] = None


def get_ai_orchestrator() -> AIServiceOrchestrator:
    """
    获取AI服务协调器实例（单例模式）
    
    Returns:
        AIServiceOrchestrator: AI服务协调器实例
    """
    global _ai_orchestrator_instance
    if _ai_orchestrator_instance is None:
        _ai_orchestrator_instance = AIServiceOrchestrator()
    return _ai_orchestrator_instance


# ================== 便捷函数接口 ==================

async def extract_knowledge_from_text(content: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    便捷函数：从文本中提取完整知识结构
    
    Args:
        content: 输入文本内容
        context: 上下文信息
    
    Returns:
        Dict[str, Any]: 完整的知识提取结果
    """
    orchestrator = get_ai_orchestrator()
    return await orchestrator.extract_knowledge(content, context)


async def get_text_embedding(text: str) -> Optional[List[float]]:
    """
    便捷函数：获取文本向量
    
    Args:
        text: 输入文本
    
    Returns:
        Optional[List[float]]: 文本向量，失败时返回None
    """
    orchestrator = get_ai_orchestrator()
    return await orchestrator.get_embedding(text)


async def extract_entities_from_text(content: str) -> List[Dict[str, Any]]:
    """
    便捷函数：从文本中提取实体
    
    Args:
        content: 输入文本内容
    
    Returns:
        List[Dict[str, Any]]: 提取的实体列表
    """
    orchestrator = get_ai_orchestrator()
    return await orchestrator.extract_entities_only(content)


if __name__ == "__main__":
    """AI服务协调器测试"""
    
    async def test_ai_orchestrator():
        """测试AI服务协调器功能"""
        print("🚀 开始测试AI服务协调器...")
        
        try:
            # 获取协调器实例
            orchestrator = get_ai_orchestrator()
            
            # 使用上下文管理器测试
            async with orchestrator.service_context():
                print("✅ AI服务协调器初始化成功")
                
                # 测试健康检查
                health_check = await orchestrator.health_check()
                print(f"🏥 服务健康状态: {health_check.status.value}")
                print(f"📝 健康检查详情: {health_check.message}")
                
                # 测试向量生成
                test_text = "这是一个测试文本，用于验证AI服务协调器的向量生成功能。"
                embedding = await orchestrator.get_embedding(test_text)
                if embedding:
                    print(f"✅ 向量生成成功 - 维度: {len(embedding)}")
                else:
                    print("⚠️ 向量生成失败或不可用")
                
                # 测试知识提取
                test_content = """
                苹果公司是一家位于美国加利福尼亚州库比蒂诺的科技公司，由史蒂夫·乔布斯、史蒂夫·沃兹尼亚克和罗纳德·韦恩于1976年创立。
                公司最著名的产品包括iPhone、iPad和MacBook等，这些产品在全球科技市场具有重要影响力。
                2007年发布的第一代iPhone彻底改变了智能手机行业，开启了移动互联网时代。
                """
                
                knowledge = await orchestrator.extract_knowledge(test_content.strip())
                print(f"✅ 知识提取成功:")
                print(f"   - 实体数量: {knowledge['processing_stats']['entities_extracted']}")
                print(f"   - 陈述数量: {knowledge['processing_stats']['statements_extracted']}")
                print(f"   - 提取方法: {knowledge['processing_stats']['extraction_method']}")
                print(f"   - 处理耗时: {knowledge['processing_stats']['processing_time_seconds']:.2f}s")
                
                # 显示提取的实体
                for i, entity in enumerate(knowledge['entities'][:5]):  # 仅显示前5个
                    print(f"   实体{i+1}: [{entity['type']}] {entity['name']} (置信度: {entity['confidence']:.2f})")
                
                print("🎉 AI服务协调器测试完成！")
        
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 运行测试
    asyncio.run(test_ai_orchestrator())