"""
智能记忆引擎 - LLM服务抽象基类

定义所有LLM服务的统一接口和基础功能：
- 模型调用的统一接口
- 重试机制和错误处理
- 响应格式化和解析
- 配置管理和健康检查

支持的功能：
- 实体提取
- 知识陈述提取
- 文本分析和处理

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 14:53:21
"""

import asyncio
import json
import logging
import re
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from enum import Enum

from services.core.base_service import BaseService, ServiceHealthCheck, ServiceStatus


class LLMModelType(Enum):
    """LLM模型类型"""
    OPENAI = "openai"
    GEMINI = "gemini" 
    CUSTOM = "custom"


@dataclass
class LLMConfig:
    """LLM服务配置"""
    model: str
    api_key: str
    base_url: Optional[str] = None
    timeout: float = 60.0
    temperature: float = 0.7
    max_tokens: int = 4000
    max_retries: int = 3
    retry_delay: float = 1.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "model": self.model,
            "api_key": "***" if self.api_key else None,  # 隐藏密钥
            "base_url": self.base_url,
            "timeout": self.timeout,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "max_retries": self.max_retries,
            "retry_delay": self.retry_delay
        }


@dataclass
class LLMResponse:
    """LLM响应数据结构"""
    content: str
    model: str
    usage: Dict[str, int]
    response_time: float
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "content": self.content,
            "model": self.model,
            "usage": self.usage,
            "response_time": self.response_time,
            "metadata": self.metadata or {}
        }


class LLMError(Exception):
    """LLM服务异常基类"""
    pass


class LLMConnectionError(LLMError):
    """LLM连接异常"""
    pass


class LLMRateLimitError(LLMError):
    """LLM请求频率限制异常"""
    pass


class LLMContentError(LLMError):
    """LLM内容处理异常"""
    pass


class BaseLLMService(BaseService, ABC):
    """
    LLM服务抽象基类
    
    提供统一的LLM服务接口，包括：
    - 模型调用的标准接口
    - 重试机制和错误处理
    - 响应解析和验证
    - 性能监控和健康检查
    """
    
    def __init__(
        self,
        service_name: str,
        config: LLMConfig,
        logger: Optional[logging.Logger] = None
    ):
        super().__init__(service_name, config.to_dict(), logger)
        self.llm_config = config
        self.model_type = self._detect_model_type()
        
        # LLM特有的指标
        self._llm_metrics = {
            "total_tokens": 0,
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "average_tokens_per_request": 0.0,
            "rate_limit_errors": 0,
            "content_errors": 0
        }
        
        self.logger.info(f"初始化LLM服务: {service_name} (模型: {config.model})")
    
    @property
    def llm_metrics(self) -> Dict[str, Any]:
        """获取LLM特有指标"""
        return self._llm_metrics.copy()
    
    def _detect_model_type(self) -> LLMModelType:
        """根据配置检测模型类型"""
        model_name = self.llm_config.model.lower()
        
        if "gpt" in model_name or "openai" in model_name:
            return LLMModelType.OPENAI
        elif "gemini" in model_name or "google" in model_name:
            return LLMModelType.GEMINI
        else:
            return LLMModelType.CUSTOM
    
    async def _initialize_service(self) -> None:
        """初始化LLM服务的具体实现"""
        try:
            await self._initialize_llm_client()
            self.logger.info(f"LLM客户端初始化完成 - 模型: {self.llm_config.model}")
        except Exception as e:
            self.logger.error(f"LLM客户端初始化失败: {e}")
            raise LLMConnectionError(f"LLM服务初始化失败: {e}")
    
    async def _cleanup_service(self) -> None:
        """清理LLM服务资源"""
        try:
            await self._cleanup_llm_client()
            self.logger.info("LLM客户端资源清理完成")
        except Exception as e:
            self.logger.error(f"LLM客户端清理失败: {e}")
            raise
    
    async def _perform_health_check(self) -> ServiceHealthCheck:
        """执行LLM服务健康检查"""
        try:
            start_time = time.time()
            
            # 使用简单的测试请求检查服务可用性
            test_prompt = "Hello, please respond with 'OK' to confirm service availability."
            response = await self._call_llm_simple(test_prompt, max_tokens=10)
            
            response_time = time.time() - start_time
            
            if response and response.content:
                return ServiceHealthCheck(
                    service_name=self.service_name,
                    status=ServiceStatus.READY,
                    message="LLM服务正常",
                    details={
                        "model": self.llm_config.model,
                        "test_response": response.content[:50],
                        "response_time": response_time
                    },
                    response_time=response_time
                )
            else:
                return ServiceHealthCheck(
                    service_name=self.service_name,
                    status=ServiceStatus.ERROR,
                    message="LLM服务返回空响应",
                    response_time=response_time
                )
                
        except Exception as e:
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=ServiceStatus.ERROR,
                message=f"LLM健康检查失败: {str(e)}",
                response_time=time.time() - start_time if 'start_time' in locals() else None
            )
    
    async def call_llm(
        self,
        messages: List[Dict[str, str]],
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        response_format: Optional[str] = None
    ) -> LLMResponse:
        """
        调用LLM模型的通用接口
        
        Args:
            messages: 消息列表 [{"role": "user/system", "content": "..."}]
            temperature: 生成温度 (0.0-1.0)
            max_tokens: 最大token数
            response_format: 响应格式 ("json_object" 等)
            
        Returns:
            LLMResponse: 标准化的LLM响应
            
        Raises:
            LLMError: LLM调用相关异常
        """
        start_time = time.time()
        success = False
        
        try:
            # 使用配置的默认值
            temperature = temperature or self.llm_config.temperature
            max_tokens = max_tokens or self.llm_config.max_tokens
            
            # 带重试的LLM调用
            response = await self._call_llm_with_retry(
                messages, temperature, max_tokens, response_format
            )
            
            success = True
            
            # 更新指标
            self._update_llm_metrics(response, success=True)
            
            response_time = time.time() - start_time
            self.update_metrics(success=True, response_time=response_time)
            
            return response
            
        except Exception as e:
            response_time = time.time() - start_time
            self.update_metrics(success=False, response_time=response_time)
            self._update_llm_metrics(None, success=False, error=e)
            
            # 根据异常类型抛出相应的异常
            if "rate limit" in str(e).lower():
                raise LLMRateLimitError(f"请求频率限制: {e}")
            elif "content" in str(e).lower() or "format" in str(e).lower():
                raise LLMContentError(f"内容处理错误: {e}")
            else:
                raise LLMConnectionError(f"LLM调用失败: {e}")
    
    async def _call_llm_simple(
        self,
        prompt: str,
        temperature: float = 0.1,
        max_tokens: int = 100
    ) -> Optional[LLMResponse]:
        """
        简单的LLM调用（用于健康检查等）
        
        Args:
            prompt: 简单提示文本
            temperature: 生成温度
            max_tokens: 最大token数
            
        Returns:
            Optional[LLMResponse]: LLM响应或None
        """
        messages = [{"role": "user", "content": prompt}]
        try:
            return await self.call_llm(
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
        except Exception as e:
            self.logger.warning(f"简单LLM调用失败: {e}")
            return None
    
    async def _call_llm_with_retry(
        self,
        messages: List[Dict[str, str]],
        temperature: float,
        max_tokens: int,
        response_format: Optional[str] = None
    ) -> LLMResponse:
        """
        带重试机制的LLM调用
        
        Args:
            messages: 消息列表
            temperature: 生成温度
            max_tokens: 最大token数
            response_format: 响应格式
            
        Returns:
            LLMResponse: LLM响应
            
        Raises:
            LLMError: 重试失败后的异常
        """
        retry_delay = self.llm_config.retry_delay
        
        for attempt in range(self.llm_config.max_retries + 1):
            try:
                if attempt > 0:
                    self.logger.debug(f"LLM重试第 {attempt} 次，延迟 {retry_delay:.1f}s")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                
                return await self._call_llm_api(
                    messages, temperature, max_tokens, response_format
                )
                
            except Exception as e:
                if attempt == self.llm_config.max_retries:
                    self.logger.error(f"LLM调用失败，已重试 {self.llm_config.max_retries} 次: {e}")
                    raise
                else:
                    self.logger.warning(f"LLM调用失败 - 第 {attempt + 1} 次尝试: {e}")
    
    def _update_llm_metrics(
        self, 
        response: Optional[LLMResponse], 
        success: bool,
        error: Optional[Exception] = None
    ) -> None:
        """更新LLM特有指标"""
        if success and response:
            # 更新token使用统计
            usage = response.usage
            self._llm_metrics["total_tokens"] += usage.get("total_tokens", 0)
            self._llm_metrics["prompt_tokens"] += usage.get("prompt_tokens", 0)
            self._llm_metrics["completion_tokens"] += usage.get("completion_tokens", 0)
            
            # 更新平均每请求token数
            total_requests = self.metrics["successful_requests"]
            if total_requests > 0:
                self._llm_metrics["average_tokens_per_request"] = (
                    self._llm_metrics["total_tokens"] / total_requests
                )
        
        # 更新错误统计
        if error:
            error_str = str(error).lower()
            if "rate limit" in error_str:
                self._llm_metrics["rate_limit_errors"] += 1
            elif "content" in error_str or "format" in error_str:
                self._llm_metrics["content_errors"] += 1
    
    def clean_json_response(self, response_text: str) -> str:
        """
        清理和修复JSON响应文本
        
        Args:
            response_text: 原始响应文本
            
        Returns:
            str: 清理后的JSON文本
        """
        # 移除markdown代码块标记
        response_text = re.sub(r'```json\s*', '', response_text)
        response_text = re.sub(r'```\s*$', '', response_text)
        
        # 移除前后空白字符
        response_text = response_text.strip()
        
        # 尝试提取JSON部分
        json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
        if json_match:
            response_text = json_match.group(0)
        
        return response_text
    
    def parse_json_response(self, response_text: str) -> Dict[str, Any]:
        """
        解析JSON响应
        
        Args:
            response_text: 响应文本
            
        Returns:
            Dict[str, Any]: 解析后的数据
            
        Raises:
            LLMContentError: JSON解析失败时
        """
        try:
            return json.loads(response_text)
        except json.JSONDecodeError as e:
            # 尝试修复常见的JSON格式问题
            cleaned_text = self.clean_json_response(response_text)
            try:
                return json.loads(cleaned_text)
            except json.JSONDecodeError:
                raise LLMContentError(f"JSON解析失败: {e}")
    
    def get_service_info(self) -> Dict[str, Any]:
        """获取LLM服务信息"""
        base_info = super().get_service_info()
        
        llm_info = {
            "model_type": self.model_type.value,
            "model_name": self.llm_config.model,
            "llm_config": {
                "temperature": self.llm_config.temperature,
                "max_tokens": self.llm_config.max_tokens,
                "timeout": self.llm_config.timeout,
                "base_url": self.llm_config.base_url
            },
            "llm_metrics": self._llm_metrics
        }
        
        base_info.update(llm_info)
        return base_info
    
    # ========== 抽象方法：子类必须实现 ==========
    
    @abstractmethod
    async def _initialize_llm_client(self) -> None:
        """
        初始化具体的LLM客户端
        子类必须实现此方法
        """
        pass
    
    @abstractmethod
    async def _cleanup_llm_client(self) -> None:
        """
        清理具体的LLM客户端资源
        子类必须实现此方法
        """
        pass
    
    @abstractmethod
    async def _call_llm_api(
        self,
        messages: List[Dict[str, str]],
        temperature: float,
        max_tokens: int,
        response_format: Optional[str] = None
    ) -> LLMResponse:
        """
        调用具体的LLM API
        子类必须实现此方法
        """
        pass


# ========== 实体和陈述提取的通用提示词模板 ==========

class LLMPromptTemplates:
    """LLM提示词模板集合"""
    
    @staticmethod
    def get_entity_extraction_system_prompt() -> str:
        """获取实体提取系统提示词"""
        return """你是一个专业的实体提取专家，擅长从中文文本中准确识别和提取各种类型的实体。

实体类型定义：
- Person（人名）: 真实人物、虚构人物、历史人物等
- Organization（组织机构）: 公司、政府机构、学校、团体等
- Location（地点）: 国家、城市、地址、地标等
- Concept（概念）: 抽象概念、理论、方法、技术等
- Event（事件）: 历史事件、会议、活动、项目等
- Product（产品）: 具体产品、服务、品牌等
- Time（时间）: 具体时间点、时间段、历史时期等

提取要求：
1. 准确识别实体边界，避免过度提取
2. 为每个实体提供简洁而准确的描述
3. 根据上下文判断实体的真正含义
4. 避免提取过于通用或无意义的词汇
5. 优先提取具有重要信息价值的实体

输出格式：
必须返回标准JSON格式，包含entities数组，每个实体包含：
- type: 实体类型（必须是上述7种类型之一）
- name: 实体名称（从原文中提取的准确文本）
- description: 实体描述（简洁说明实体的含义或重要性）
- confidence: 置信度（0.0-1.0之间的浮点数）

示例输出：
{
  "entities": [
    {
      "type": "Person",
      "name": "张三",
      "description": "文中提到的主要人物",
      "confidence": 0.9
    }
  ]
}"""
    
    @staticmethod
    def get_entity_extraction_user_prompt(content: str, max_length: int = 3000) -> str:
        """
        获取实体提取用户提示词
        
        Args:
            content: 要提取实体的文本内容
            max_length: 最大内容长度
            
        Returns:
            str: 用户提示词
        """
        # 限制内容长度
        if len(content) > max_length:
            content = content[:max_length] + "..."
        
        return f"""请从以下中文文本中提取所有重要的实体信息：

{content}

请严格按照JSON格式返回提取结果，确保格式正确且包含所有必需字段。"""
    
    @staticmethod
    def get_statement_extraction_prompt(content: str, entities: List[str]) -> str:
        """
        获取知识陈述提取提示词
        
        Args:
            content: 文本内容
            entities: 已识别的实体列表
            
        Returns:
            str: 知识陈述提取提示词
        """
        entity_list = ', '.join(entities) if entities else '无'
        
        return f"""请从以下文本中提取关键的知识陈述（事实），返回JSON格式：

文本: {content}

已识别实体: {entity_list}

请提取重要的事实陈述，每个陈述应该包含：
- subject: 主语（优先使用已识别的实体）
- predicate: 谓语动词或关系
- object: 宾语
- fact: 完整的事实描述
- confidence: 置信度(0.0-1.0)

返回格式：
{{
  "statements": [
    {{
      "subject": "主语",
      "predicate": "关系/动作",
      "object": "宾语",
      "fact": "完整事实描述",
      "confidence": 0.9
    }}
  ]
}}

只返回JSON，不要其他解释。"""