"""
智能记忆引擎 - OpenAI/Gemini LLM服务实现

提供OpenAI兼容API的统一接口，支持：
- OpenAI GPT模型
- Google Gemini模型（通过兼容API）
- 其他OpenAI兼容的LLM服务

核心功能：
- 异步API调用和连接管理
- 自定义base_url支持（如Gemini API代理）
- 重试机制和错误处理
- 响应解析和格式化
- 实体提取和知识陈述提取

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 14:53:21
"""

import asyncio
import time
from typing import Dict, Any, List, Optional

from openai import AsyncOpenAI

from .llm_base import (
    BaseLLMService, LLMConfig, LLMResponse, 
    LLMError, LLMConnectionError, LLMContentError,
    LLMPromptTemplates
)
from config import settings


class OpenAIService(BaseLLMService):
    """
    OpenAI/Gemini LLM服务实现
    
    支持以下功能：
    - OpenAI GPT系列模型
    - Google Gemini模型（通过兼容API）
    - 自定义base_url的LLM服务
    - 完整的错误处理和重试机制
    - 实体提取和知识陈述提取
    """
    
    def __init__(self, config: Optional[LLMConfig] = None):
        # 使用配置或从settings创建默认配置
        if config is None:
            config = self._create_default_config()
        
        super().__init__("openai_llm_service", config)
        
        # OpenAI客户端
        self._client: Optional[AsyncOpenAI] = None
        
        # 实体类型定义
        self.entity_types = [
            "Person",      # 人名
            "Organization", # 组织机构
            "Location",    # 地点
            "Concept",     # 概念
            "Event",       # 事件
            "Product",     # 产品
            "Time"         # 时间
        ]
    
    @classmethod
    def _create_default_config(cls) -> LLMConfig:
        """从settings创建默认配置"""
        openai_config = settings.get_openai_config()
        
        return LLMConfig(
            model=openai_config["model"],
            api_key=openai_config["api_key"],
            base_url=openai_config.get("base_url"),
            timeout=openai_config["timeout"],
            temperature=openai_config["temperature"],
            max_tokens=openai_config["max_tokens"],
            max_retries=3,
            retry_delay=1.0
        )
    
    async def _initialize_llm_client(self) -> None:
        """初始化OpenAI客户端"""
        if not self.llm_config.api_key:
            raise LLMConnectionError("OpenAI API密钥未配置")
        
        # 构建客户端参数
        client_params = {
            "api_key": self.llm_config.api_key,
            "timeout": self.llm_config.timeout,
        }
        
        # 添加自定义base_url支持（如Gemini API代理）
        if self.llm_config.base_url:
            client_params["base_url"] = self.llm_config.base_url
            self.logger.info(f"使用自定义API端点: {self.llm_config.base_url}")
        
        self._client = AsyncOpenAI(**client_params)
        
        self.logger.info(f"OpenAI客户端初始化完成 - 模型: {self.llm_config.model}")
    
    async def _cleanup_llm_client(self) -> None:
        """清理OpenAI客户端资源"""
        if self._client:
            await self._client.close()
            self._client = None
            self.logger.debug("OpenAI客户端连接已关闭")
    
    async def _call_llm_api(
        self,
        messages: List[Dict[str, str]],
        temperature: float,
        max_tokens: int,
        response_format: Optional[str] = None
    ) -> LLMResponse:
        """
        调用OpenAI API (使用流式调用避免超时)
        
        Args:
            messages: 消息列表
            temperature: 生成温度
            max_tokens: 最大token数
            response_format: 响应格式
            
        Returns:
            LLMResponse: 标准化响应
            
        Raises:
            LLMConnectionError: API连接异常
            LLMContentError: 内容处理异常
        """
        if not self._client:
            raise LLMConnectionError("OpenAI客户端未初始化")
        
        try:
            start_time = time.time()
            
            # 构建API调用参数
            api_params = {
                "model": self.llm_config.model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": True  # 启用流式调用
            }
            
            # 添加响应格式（如果支持）
            if response_format == "json_object":
                api_params["response_format"] = {"type": "json_object"}
            
            self.logger.debug(f"调用OpenAI API (流式) - 模型: {self.llm_config.model}")
            
            # 使用流式API调用
            stream = await self._client.chat.completions.create(**api_params)
            
            # 收集流式响应
            content_chunks = []
            finish_reason = None
            usage_info = None
            
            async for chunk in stream:
                if chunk.choices:
                    choice = chunk.choices[0]
                    if choice.delta.content:
                        content_chunks.append(choice.delta.content)
                    if choice.finish_reason:
                        finish_reason = choice.finish_reason
                
                # 获取使用统计（通常在最后一个chunk中）
                if hasattr(chunk, 'usage') and chunk.usage:
                    usage_info = chunk.usage
            
            response_time = time.time() - start_time
            
            # 合并内容
            content = ''.join(content_chunks)
            if not content:
                raise LLMContentError("OpenAI API返回空内容")
            
            # 构建使用统计
            usage = {
                "prompt_tokens": usage_info.prompt_tokens if usage_info else 0,
                "completion_tokens": usage_info.completion_tokens if usage_info else 0,
                "total_tokens": usage_info.total_tokens if usage_info else 0
            }
            
            self.logger.debug(
                f"OpenAI API流式调用成功 - Token使用: {usage['total_tokens']}, 耗时: {response_time:.2f}s"
            )
            
            return LLMResponse(
                content=content,
                model=self.llm_config.model,
                usage=usage,
                response_time=response_time,
                metadata={
                    "finish_reason": finish_reason or "stop",
                    "api_version": "openai_compatible_stream",
                    "chunks_received": len(content_chunks)
                }
            )
            
        except Exception as e:
            error_msg = f"OpenAI API流式调用失败: {e}"
            self.logger.error(error_msg)
            
            # 根据错误类型抛出相应异常
            error_str = str(e).lower()
            if "rate limit" in error_str or "quota" in error_str:
                raise LLMConnectionError(f"API请求频率限制: {e}")
            elif "timeout" in error_str:
                raise LLMConnectionError(f"API请求超时: {e}")
            elif "authentication" in error_str or "api_key" in error_str:
                raise LLMConnectionError(f"API认证失败: {e}")
            else:
                raise LLMConnectionError(error_msg)
    
    # ========== 高级功能：实体和知识提取 ==========
    
    async def extract_entities(self, content: str) -> List[Dict[str, Any]]:
        """
        从文本中提取实体
        
        Args:
            content: 输入文本内容
            
        Returns:
            List[Dict[str, Any]]: 提取的实体列表
            
        Raises:
            LLMError: 实体提取失败
        """
        if not content or not content.strip():
            raise ValueError("输入内容不能为空")
        
        if len(content.strip()) < 10:
            self.logger.warning("输入内容过短，可能影响实体提取效果")
        
        try:
            self.logger.debug(f"开始实体提取 - 内容长度: {len(content)}")
            
            # 构建提示词
            system_prompt = LLMPromptTemplates.get_entity_extraction_system_prompt()
            user_prompt = LLMPromptTemplates.get_entity_extraction_user_prompt(content)
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # 调用LLM进行实体提取
            response = await self.call_llm(
                messages=messages,
                temperature=0.3,  # 较低温度确保一致性
                response_format="json_object"
            )
            
            # 解析JSON响应
            response_data = self.parse_json_response(response.content)
            
            # 验证响应结构
            if "entities" not in response_data:
                raise LLMContentError("响应缺少entities字段")
            
            entities = response_data["entities"]
            if not isinstance(entities, list):
                raise LLMContentError("entities字段不是数组")
            
            # 验证和清理实体数据
            clean_entities = self._validate_and_clean_entities(entities)
            
            self.logger.info(f"实体提取完成 - 提取到 {len(clean_entities)} 个实体")
            return clean_entities
            
        except LLMError:
            raise
        except Exception as e:
            error_msg = f"实体提取失败: {e}"
            self.logger.error(error_msg)
            raise LLMContentError(error_msg)
    
    async def extract_statements(
        self, 
        content: str, 
        entities: Optional[List[Dict[str, Any]]] = None
    ) -> List[Dict[str, Any]]:
        """
        提取知识陈述
        
        Args:
            content: 文本内容
            entities: 已提取的实体列表（可选）
            
        Returns:
            List[Dict[str, Any]]: 知识陈述列表
            
        Raises:
            LLMError: 陈述提取失败
        """
        if not content or not content.strip():
            raise ValueError("输入内容不能为空")
        
        try:
            self.logger.debug(f"开始陈述提取 - 内容长度: {len(content)}")
            
            # 准备实体名称列表
            entity_names = []
            if entities:
                entity_names = [e.get("name", "") for e in entities if e.get("name")]
            
            # 构建提示词
            prompt = LLMPromptTemplates.get_statement_extraction_prompt(content, entity_names)
            
            messages = [{"role": "user", "content": prompt}]
            
            # 调用LLM进行陈述提取
            response = await self.call_llm(
                messages=messages,
                temperature=0.3,
                response_format="json_object"
            )
            
            # 解析JSON响应
            response_data = self.parse_json_response(response.content)
            
            # 验证响应结构
            if "statements" not in response_data:
                raise LLMContentError("响应缺少statements字段")
            
            statements = response_data["statements"]
            if not isinstance(statements, list):
                raise LLMContentError("statements字段不是数组")
            
            # 验证和清理陈述数据
            clean_statements = self._validate_and_clean_statements(statements)
            
            self.logger.info(f"陈述提取完成 - 提取到 {len(clean_statements)} 个陈述")
            return clean_statements
            
        except LLMError:
            raise
        except Exception as e:
            error_msg = f"陈述提取失败: {e}"
            self.logger.error(error_msg)
            raise LLMContentError(error_msg)
    
    async def extract_knowledge(
        self, 
        content: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        综合提取知识结构（实体 + 陈述）
        
        Args:
            content: 输入文本内容
            context: 上下文信息（可选）
            
        Returns:
            Dict[str, Any]: 包含实体和陈述的完整知识结构
        """
        if not content or not content.strip():
            raise ValueError("内容不能为空")
        
        start_time = time.time()
        self.logger.info(f"开始知识提取，内容长度: {len(content)}")
        
        try:
            # 1. 提取实体
            self.logger.debug("步骤1: 提取实体")
            entities = await self.extract_entities(content)
            
            # 2. 提取知识陈述
            self.logger.debug("步骤2: 提取知识陈述")
            statements = await self.extract_statements(content, entities)
            
            # 3. 计算置信度统计
            entity_confidences = [e.get("confidence", 0.5) for e in entities]
            statement_confidences = [s.get("confidence", 0.5) for s in statements]
            all_confidences = entity_confidences + statement_confidences
            
            # 4. 构建处理统计
            processing_time = time.time() - start_time
            processing_stats = {
                "processing_time_seconds": processing_time,
                "entities_extracted": len(entities),
                "statements_extracted": len(statements),
                "avg_confidence": sum(all_confidences) / len(all_confidences) if all_confidences else 0.0,
                "content_length": len(content),
                "extraction_method": "llm_extraction"
            }
            
            result = {
                "entities": entities,
                "statements": statements,
                "confidence_scores": all_confidences,
                "processing_stats": processing_stats
            }
            
            self.logger.info(
                f"知识提取完成 - 实体: {len(entities)}, 陈述: {len(statements)}, "
                f"耗时: {processing_time:.2f}s"
            )
            
            return result
            
        except Exception as e:
            error_msg = f"知识提取失败: {e}"
            self.logger.error(error_msg)
            raise LLMError(error_msg)
    
    def _validate_and_clean_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        验证和清理提取的实体数据
        
        Args:
            entities: 原始实体列表
            
        Returns:
            List[Dict[str, Any]]: 验证和清理后的实体列表
        """
        valid_entities = []
        
        for i, entity in enumerate(entities):
            try:
                # 验证必需字段
                if not isinstance(entity, dict):
                    self.logger.warning(f"第 {i+1} 个实体不是对象，跳过")
                    continue
                
                # 验证实体类型
                entity_type = entity.get("type", "").strip()
                if entity_type not in self.entity_types:
                    self.logger.warning(f"无效的实体类型: {entity_type}，跳过该实体")
                    continue
                
                # 验证实体名称
                entity_name = entity.get("name", "").strip()
                if not entity_name or len(entity_name) < 2:
                    self.logger.warning(f"实体名称无效或过短: '{entity_name}'，跳过")
                    continue
                
                # 设置默认值
                description = entity.get("description", "").strip()
                if not description:
                    description = f"{entity_type}类型的实体"
                
                confidence = entity.get("confidence", 0.8)
                if not isinstance(confidence, (int, float)) or not (0.0 <= confidence <= 1.0):
                    confidence = 0.8
                
                # 构建清理后的实体
                clean_entity = {
                    "type": entity_type,
                    "name": entity_name,
                    "description": description,
                    "confidence": float(confidence),
                    "source": "llm_extraction",
                    "embedding": None  # 将在后续步骤中生成
                }
                
                valid_entities.append(clean_entity)
                
            except Exception as e:
                self.logger.warning(f"验证第 {i+1} 个实体时出错: {e}，跳过该实体")
                continue
        
        self.logger.debug(f"实体验证完成 - 原始: {len(entities)}, 有效: {len(valid_entities)}")
        return valid_entities
    
    def _validate_and_clean_statements(self, statements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        验证和清理提取的陈述数据
        
        Args:
            statements: 原始陈述列表
            
        Returns:
            List[Dict[str, Any]]: 验证和清理后的陈述列表
        """
        valid_statements = []
        
        for i, statement in enumerate(statements):
            try:
                # 验证必需字段
                if not isinstance(statement, dict):
                    self.logger.warning(f"第 {i+1} 个陈述不是对象，跳过")
                    continue
                
                # 验证主要字段
                subject = statement.get("subject", "").strip()
                predicate = statement.get("predicate", "").strip()
                obj = statement.get("object", "").strip()
                fact = statement.get("fact", "").strip()
                
                if not all([subject, predicate, obj]):
                    self.logger.warning(f"陈述缺少必要字段 (主语/谓语/宾语)，跳过")
                    continue
                
                # 使用fact作为完整描述，如果没有则组合三元组
                if not fact:
                    fact = f"{subject}{predicate}{obj}"
                
                # 验证置信度
                confidence = statement.get("confidence", 0.8)
                if not isinstance(confidence, (int, float)) or not (0.0 <= confidence <= 1.0):
                    confidence = 0.8
                
                # 构建清理后的陈述
                clean_statement = {
                    "subject": subject,
                    "predicate": predicate,
                    "object": obj,
                    "fact": fact,
                    "confidence": float(confidence),
                    "source": "llm_extraction",
                    "embedding": None  # 将在后续步骤中生成
                }
                
                valid_statements.append(clean_statement)
                
            except Exception as e:
                self.logger.warning(f"验证第 {i+1} 个陈述时出错: {e}，跳过该陈述")
                continue
        
        self.logger.debug(f"陈述验证完成 - 原始: {len(statements)}, 有效: {len(valid_statements)}")
        return valid_statements


# ========== 全局服务实例管理 ==========

_openai_service_instance: Optional[OpenAIService] = None


def get_openai_service() -> OpenAIService:
    """
    获取OpenAI服务实例（单例模式）
    
    Returns:
        OpenAIService: OpenAI服务实例
    """
    global _openai_service_instance
    if _openai_service_instance is None:
        _openai_service_instance = OpenAIService()
    return _openai_service_instance


# ========== 便捷函数接口 ==========

async def extract_entities_with_llm(content: str) -> List[Dict[str, Any]]:
    """
    便捷函数：使用LLM提取实体
    
    Args:
        content: 输入文本内容
    
    Returns:
        List[Dict[str, Any]]: 提取的实体列表
    """
    service = get_openai_service()
    async with service.service_context():
        return await service.extract_entities(content)


async def extract_statements_with_llm(
    content: str, 
    entities: Optional[List[Dict[str, Any]]] = None
) -> List[Dict[str, Any]]:
    """
    便捷函数：使用LLM提取知识陈述
    
    Args:
        content: 输入文本内容
        entities: 已提取的实体列表
    
    Returns:
        List[Dict[str, Any]]: 提取的知识陈述列表
    """
    service = get_openai_service()
    async with service.service_context():
        return await service.extract_statements(content, entities)


async def extract_knowledge_with_llm(
    content: str, 
    context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    便捷函数：使用LLM提取完整知识结构
    
    Args:
        content: 输入文本内容
        context: 上下文信息
    
    Returns:
        Dict[str, Any]: 完整的知识提取结果
    """
    service = get_openai_service()
    async with service.service_context():
        return await service.extract_knowledge(content, context)


if __name__ == "__main__":
    """OpenAI服务模块测试"""
    
    async def test_openai_service():
        """测试OpenAI服务功能"""
        print("🚀 开始测试OpenAI LLM服务...")
        
        try:
            # 获取服务实例
            service = get_openai_service()
            print(f"📊 服务信息: {service.get_service_info()}")
            
            # 使用上下文管理器测试
            async with service.service_context():
                print("✅ OpenAI服务初始化成功")
                
                # 测试健康检查
                health = await service.health_check()
                print(f"🏥 健康检查: {health.to_dict()}")
                
                # 测试基本LLM调用
                test_messages = [
                    {"role": "user", "content": "请回答'测试成功'来确认服务可用。"}
                ]
                
                response = await service.call_llm(messages=test_messages, max_tokens=50)
                print(f"✅ LLM调用成功: {response.content}")
                print(f"📊 Token使用: {response.usage}")
                
                # 测试实体提取功能
                print("\\n🧠 测试实体提取功能...")
                test_content = """
                苹果公司是一家位于美国加利福尼亚州库比蒂诺的科技公司，由史蒂夫·乔布斯、史蒂夫·沃兹尼亚克和罗纳德·韦恩于1976年创立。
                公司最著名的产品包括iPhone、iPad和MacBook等，这些产品在全球科技市场具有重要影响力。
                2007年发布的第一代iPhone彻底改变了智能手机行业，开启了移动互联网时代。
                """
                
                entities = await service.extract_entities(test_content.strip())
                print(f"✅ 实体提取成功 - 发现 {len(entities)} 个实体:")
                
                for i, entity in enumerate(entities):
                    print(f"  {i+1}. [{entity['type']}] {entity['name']}")
                    print(f"     描述: {entity['description']}")
                    print(f"     置信度: {entity['confidence']:.2f}")
                
                # 测试知识陈述提取
                print("\\n📝 测试知识陈述提取...")
                statements = await service.extract_statements(test_content.strip(), entities)
                print(f"✅ 陈述提取成功 - 发现 {len(statements)} 个陈述:")
                
                for i, stmt in enumerate(statements):
                    print(f"  {i+1}. {stmt['subject']} → {stmt['predicate']} → {stmt['object']}")
                    print(f"     事实: {stmt['fact']}")
                    print(f"     置信度: {stmt['confidence']:.2f}")
                
                # 测试综合知识提取
                print("\\n🔬 测试综合知识提取...")
                knowledge = await service.extract_knowledge(test_content.strip())
                stats = knowledge['processing_stats']
                
                print(f"✅ 综合知识提取完成:")
                print(f"  - 实体数量: {stats['entities_extracted']}")
                print(f"  - 陈述数量: {stats['statements_extracted']}")
                print(f"  - 平均置信度: {stats['avg_confidence']:.2f}")
                print(f"  - 处理时间: {stats['processing_time_seconds']:.2f}s")
                
                # 显示服务指标
                print("\\n📈 服务性能指标:")
                metrics = service.metrics
                llm_metrics = service.llm_metrics
                print(f"  - 总请求数: {metrics['total_requests']}")
                print(f"  - 成功请求数: {metrics['successful_requests']}")
                print(f"  - 平均响应时间: {metrics['average_response_time']:.2f}s")
                print(f"  - 总Token数: {llm_metrics['total_tokens']}")
                print(f"  - 平均每请求Token: {llm_metrics['average_tokens_per_request']:.1f}")
            
            print("🎉 OpenAI LLM服务测试完成！")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 运行测试
    asyncio.run(test_openai_service())