"""
智能记忆引擎 MVP v2.0 - BGE-M3 向量服务

专门处理BGE-M3向量模型的服务实现，继承自BaseEmbeddingService。
提供高效的文本向量化、批量处理、健康监控等功能。

BGE-M3特点：
- 支持多语言文本嵌入
- 1024维向量表示
- 高效的批量处理能力
- 优秀的语义相似性表现

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 14:06:34
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional

import httpx
from config import settings
from .embedding_base import BaseEmbeddingService, BaseEmbeddingError, EmbeddingServiceHealthError

# 配置日志记录器
logger = logging.getLogger("smart_memory.bge_service")


class BGEEmbeddingError(BaseEmbeddingError):
    """BGE-M3 Embedding 服务特定异常"""
    pass


class BGEEmbeddingService(BaseEmbeddingService):
    """
    BGE-M3 Embedding 向量服务实现
    
    核心功能：
    1. BGE-M3文本向量化服务（单个/批量）
    2. 服务健康状态监控
    3. 自动重试和错误恢复
    4. 连接池管理和资源清理
    5. 高性能批量处理优化
    """

    def __init__(self):
        """初始化BGE-M3向量服务"""
        super().__init__("BGE-M3-EmbeddingService")
        
        # BGE-M3 特定配置
        self.embedding_client: Optional[httpx.AsyncClient] = None
        self.embedding_config = settings.get_embedding_config()
        
        # 重试配置
        self.max_retries = self.embedding_config["retry_times"]
        self.retry_delays = [1, 2, 4, 8, 16]  # 指数退避延迟序列
        
        logger.info("BGE-M3向量服务初始化完成")

    async def initialize(self) -> bool:
        """
        初始化BGE-M3向量服务连接
        
        Returns:
            bool: 初始化是否成功
            
        Raises:
            EmbeddingServiceHealthError: 当服务初始化失败时
        """
        if self._is_initialized:
            logger.info("BGE-M3服务已初始化，跳过重复初始化")
            return True

        try:
            logger.info("开始初始化BGE-M3向量服务连接...")

            # 初始化BGE-M3 HTTP客户端
            await self._initialize_http_client()

            # 执行服务健康检查
            await self._perform_health_check()

            self._is_initialized = True
            logger.info("✅ BGE-M3向量服务初始化成功")
            return True

        except Exception as e:
            logger.error(f"❌ BGE-M3向量服务初始化失败: {e}")
            await self.cleanup()
            raise EmbeddingServiceHealthError(f"BGE-M3向量服务初始化失败: {e}")

    async def _initialize_http_client(self):
        """初始化BGE-M3 HTTP客户端"""
        try:
            # 创建异步HTTP客户端，配置连接池和超时
            timeout_config = httpx.Timeout(
                connect=10.0,  # 连接超时
                read=self.embedding_config["timeout"],  # 读取超时
                write=10.0,  # 写入超时
                pool=5.0   # 连接池获取超时
            )

            limits = httpx.Limits(
                max_keepalive_connections=10,  # 最大保持连接数
                max_connections=20,           # 最大连接数
                keepalive_expiry=30.0        # 连接保持时间
            )

            self.embedding_client = httpx.AsyncClient(
                timeout=timeout_config,
                limits=limits,
                follow_redirects=True,
                verify=False  # 开发环境可关闭SSL验证
            )

            logger.info(f"BGE-M3 HTTP客户端初始化完成 - 服务地址: {self.embedding_config['service_url']}")

        except Exception as e:
            logger.error(f"BGE-M3 HTTP客户端初始化失败: {e}")
            raise

    async def get_embedding(self, text: str) -> List[float]:
        """
        生成单个文本的向量表示
        
        Args:
            text: 输入文本
            
        Returns:
            List[float]: 1024维向量列表
            
        Raises:
            BGEEmbeddingError: 当向量生成失败时
            ValueError: 当输入参数无效时
        """
        # 验证输入
        cleaned_text = self._validate_text_input(text)
        
        if not self._is_initialized:
            await self.initialize()

        start_time = self._record_request_start()

        try:
            logger.debug(f"生成单个文本向量 - 文本长度: {len(cleaned_text)}")

            # 调用批量接口处理单个文本
            embeddings = await self.get_batch_embeddings([cleaned_text])

            if embeddings and len(embeddings) > 0:
                result = embeddings[0]
                logger.debug(f"向量生成成功 - 维度: {len(result)}")
                self._record_request_success(start_time)
                return result
            else:
                raise BGEEmbeddingError("向量生成返回空结果")

        except BGEEmbeddingError:
            self._record_request_failure(start_time)
            raise
        except Exception as e:
            self._record_request_failure(start_time)
            error_msg = f"单个文本向量生成失败: {e}"
            logger.error(error_msg)
            raise BGEEmbeddingError(error_msg)

    async def get_batch_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        批量生成文本向量表示
        
        Args:
            texts: 文本列表
            
        Returns:
            List[List[float]]: 向量列表，每个向量包含1024个浮点数
            
        Raises:
            BGEEmbeddingError: 当批量向量生成失败时
            ValueError: 当输入参数无效时
        """
        # 验证输入
        cleaned_texts = self._validate_texts_input(texts)
        
        if not self._is_initialized:
            await self.initialize()

        # 检查批量大小限制
        batch_size = self.embedding_config["batch_size"]
        if len(cleaned_texts) > batch_size:
            logger.warning(f"文本数量({len(cleaned_texts)})超过批量限制({batch_size})，将分批处理")
            return await self._process_large_batch(cleaned_texts, batch_size)

        start_time = self._record_request_start()

        try:
            logger.debug(f"批量生成文本向量 - 文本数量: {len(cleaned_texts)}")

            # 调用BGE-M3 API
            result = await self._call_embedding_api(cleaned_texts)

            if not result:
                raise BGEEmbeddingError("批量向量生成返回空结果")

            # 验证返回结果
            self._validate_embedding_results(result, cleaned_texts)

            logger.debug(f"批量向量生成成功 - 向量数量: {len(result)}, 向量维度: {len(result[0])}")
            self._record_request_success(start_time)
            return result

        except BGEEmbeddingError:
            self._record_request_failure(start_time)
            raise
        except Exception as e:
            self._record_request_failure(start_time)
            error_msg = f"批量文本向量生成失败: {e}"
            logger.error(error_msg)
            raise BGEEmbeddingError(error_msg)

    def _validate_embedding_results(self, results: List[List[float]], input_texts: List[str]):
        """
        验证向量生成结果的正确性
        
        Args:
            results: API返回的向量结果
            input_texts: 输入文本列表
            
        Raises:
            BGEEmbeddingError: 验证失败时
        """
        # 验证返回结果数量
        if len(results) != len(input_texts):
            raise BGEEmbeddingError(f"返回向量数量({len(results)})与输入文本数量({len(input_texts)})不匹配")

        # 验证向量维度
        expected_dim = self.embedding_config["dimension"]
        for i, embedding in enumerate(results):
            if not isinstance(embedding, list):
                raise BGEEmbeddingError(f"第{i+1}个向量格式错误，应为列表类型")
            
            if len(embedding) != expected_dim:
                raise BGEEmbeddingError(f"第{i+1}个向量维度({len(embedding)})不匹配预期维度({expected_dim})")
            
            # 验证向量数值有效性
            if not all(isinstance(x, (int, float)) for x in embedding):
                raise BGEEmbeddingError(f"第{i+1}个向量包含非数值元素")

    async def _process_large_batch(self, texts: List[str], batch_size: int) -> List[List[float]]:
        """
        处理超大批量文本的向量生成
        
        Args:
            texts: 文本列表
            batch_size: 批量大小
            
        Returns:
            List[List[float]]: 完整的向量列表
        """
        logger.info(f"开始分批处理大量文本 - 总数: {len(texts)}, 批量大小: {batch_size}")

        all_embeddings = []
        batch_count = (len(texts) + batch_size - 1) // batch_size

        for i in range(0, len(texts), batch_size):
            batch_num = i // batch_size + 1
            batch_texts = texts[i:i + batch_size]

            logger.debug(f"处理第 {batch_num}/{batch_count} 批 - 文本数量: {len(batch_texts)}")

            try:
                batch_embeddings = await self._call_embedding_api(batch_texts)
                all_embeddings.extend(batch_embeddings)

                # 批次间短暂延迟，避免服务过载
                if i + batch_size < len(texts):
                    await asyncio.sleep(0.1)

            except Exception as e:
                logger.error(f"第 {batch_num} 批处理失败: {e}")
                raise

        logger.info(f"大批量文本处理完成 - 总向量数量: {len(all_embeddings)}")
        return all_embeddings

    async def _call_embedding_api(self, texts: List[str], is_health_check: bool = False) -> List[List[float]]:
        """
        调用BGE-M3 Embedding API
        
        Args:
            texts: 文本列表
            is_health_check: 是否为健康检查调用
            
        Returns:
            List[List[float]]: 向量列表
            
        Raises:
            BGEEmbeddingError: API调用失败时
        """
        if not self.embedding_client:
            raise BGEEmbeddingError("BGE-M3客户端未初始化")

        url = f"{self.embedding_config['service_url']}/api/v1/embed/documents"
        payload = {
            "texts": texts,
            "normalize": True
        }

        last_exception = None

        # 实现重试机制
        for attempt in range(self.max_retries + 1):
            try:
                if attempt > 0:
                    delay = self.retry_delays[min(attempt - 1, len(self.retry_delays) - 1)]
                    logger.warning(f"BGE-M3 API重试第 {attempt} 次，延迟 {delay} 秒...")
                    await asyncio.sleep(delay)

                # 发送HTTP请求
                response = await self.embedding_client.post(
                    url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )

                # 检查HTTP状态码
                response.raise_for_status()

                # 解析响应数据
                data = response.json()

                # 验证响应格式 (BGE-M3格式)
                if "embeddings" not in data:
                    raise BGEEmbeddingError("API响应缺少embeddings字段")

                embeddings = data["embeddings"]
                if not isinstance(embeddings, list):
                    raise BGEEmbeddingError("API响应embeddings格式错误")

                if not is_health_check:
                    logger.debug(f"BGE-M3 API调用成功 - 响应时间: {response.elapsed.total_seconds():.3f}s")

                return embeddings

            except httpx.HTTPStatusError as e:
                last_exception = e
                error_msg = f"HTTP错误 {e.response.status_code}: {e.response.text}"
                logger.error(f"BGE-M3 API调用失败 - {error_msg}")

                # 某些HTTP错误不需要重试
                if e.response.status_code in [400, 401, 403, 404]:
                    break

            except httpx.TimeoutException as e:
                last_exception = e
                logger.error(f"BGE-M3 API超时 - 第 {attempt + 1} 次尝试")

            except httpx.ConnectError as e:
                last_exception = e
                logger.error(f"BGE-M3 API连接失败 - 第 {attempt + 1} 次尝试: {e}")

            except Exception as e:
                last_exception = e
                logger.error(f"BGE-M3 API调用异常 - 第 {attempt + 1} 次尝试: {e}")

        # 所有重试都失败
        error_msg = f"BGE-M3 API调用失败，已重试 {self.max_retries} 次: {last_exception}"
        logger.error(error_msg)
        raise BGEEmbeddingError(error_msg)

    async def _perform_health_check(self) -> bool:
        """
        执行BGE-M3服务健康检查
        
        Returns:
            bool: 服务是否健康
            
        Raises:
            EmbeddingServiceHealthError: 当服务不可用时
        """
        try:
            logger.debug("执行BGE-M3服务健康检查...")

            # 使用简单的文本进行健康检查
            health_check_text = "健康检查测试文本"

            # 调用embedding接口进行健康检查
            response = await self._call_embedding_api([health_check_text], is_health_check=True)

            if response and len(response) > 0 and len(response[0]) > 0:
                logger.debug("✅ BGE-M3服务健康检查通过")
                return True
            else:
                raise EmbeddingServiceHealthError("健康检查返回无效响应")

        except Exception as e:
            error_msg = f"BGE-M3服务健康检查失败: {e}"
            logger.error(f"❌ {error_msg}")
            raise EmbeddingServiceHealthError(error_msg)

    async def cleanup(self):
        """清理BGE-M3服务资源和关闭连接"""
        logger.info("开始清理BGE-M3向量服务资源...")

        try:
            # 关闭HTTP客户端
            if self.embedding_client:
                await self.embedding_client.aclose()
                self.embedding_client = None
                logger.debug("BGE-M3 HTTP客户端连接已关闭")

            # 重置状态
            self._is_initialized = False
            self._service_available = False
            self._last_health_check = 0

            logger.info("✅ BGE-M3向量服务资源清理完成")

        except Exception as e:
            logger.error(f"❌ BGE-M3向量服务资源清理异常: {e}")

    def get_service_config(self) -> Dict[str, Any]:
        """
        获取BGE-M3服务配置信息
        
        Returns:
            Dict[str, Any]: 服务配置详情
        """
        return {
            "service_type": "BGE-M3-Embedding",
            "service_url": self.embedding_config["service_url"],
            "timeout": self.embedding_config["timeout"],
            "retry_times": self.embedding_config["retry_times"],
            "dimension": self.embedding_config["dimension"],
            "batch_size": self.embedding_config["batch_size"],
            "max_retries": self.max_retries,
            "retry_delays": self.retry_delays,
            "health_check_interval": self._health_check_interval
        }

    def get_embedding_dimension(self) -> int:
        """
        获取向量维度
        
        Returns:
            int: 向量维度数
        """
        return self.embedding_config["dimension"]

    def get_max_batch_size(self) -> int:
        """
        获取最大批量处理大小
        
        Returns:
            int: 最大批量大小
        """
        return self.embedding_config["batch_size"]

    def get_api_endpoint(self) -> str:
        """
        获取API端点URL
        
        Returns:
            str: API端点地址
        """
        return f"{self.embedding_config['service_url']}/api/v1/embed/documents"

    def __str__(self) -> str:
        """字符串表示"""
        return (f"BGE-M3EmbeddingService("
                f"url={self.embedding_config['service_url']}, "
                f"dim={self.embedding_config['dimension']}, "
                f"initialized={self._is_initialized}, "
                f"available={self._service_available})")


# ================== 便捷函数和实例管理 ==================

# 全局BGE-M3服务实例（单例模式）
_bge_service_instance: Optional[BGEEmbeddingService] = None


def get_bge_service() -> BGEEmbeddingService:
    """
    获取BGE-M3向量服务实例（单例模式）
    
    Returns:
        BGEEmbeddingService: BGE-M3向量服务实例
    """
    global _bge_service_instance
    if _bge_service_instance is None:
        _bge_service_instance = BGEEmbeddingService()
    return _bge_service_instance


async def get_bge_embedding(text: str) -> List[float]:
    """
    便捷函数：使用BGE-M3生成单个文本的向量表示
    
    Args:
        text: 输入文本
        
    Returns:
        List[float]: 向量表示
    """
    service = get_bge_service()
    return await service.get_embedding(text)


async def get_bge_batch_embeddings(texts: List[str]) -> List[List[float]]:
    """
    便捷函数：使用BGE-M3批量生成文本向量表示
    
    Args:
        texts: 文本列表
        
    Returns:
        List[List[float]]: 向量列表
    """
    service = get_bge_service()
    return await service.get_batch_embeddings(texts)


if __name__ == "__main__":
    """BGE-M3向量服务模块测试"""

    async def test_bge_service():
        """测试BGE-M3向量服务功能"""
        print("🚀 开始测试BGE-M3向量服务模块...")

        try:
            # 获取服务实例
            service = get_bge_service()
            print(f"📊 服务信息: {service.get_service_info()}")

            # 使用上下文管理器测试
            async with service.managed_service():
                print("✅ BGE-M3服务初始化成功")

                # 测试单个文本向量生成
                test_text = "这是一个测试文本，用于验证BGE-M3向量生成功能。"
                print(f"🧪 测试文本: {test_text}")

                embedding = await service.get_embedding(test_text)
                print(f"✅ 单个向量生成成功 - 维度: {len(embedding)}")
                print(f"📋 向量前5维: {embedding[:5]}")

                # 测试批量向量生成
                test_texts = [
                    "第一个测试文本",
                    "第二个测试文本",
                    "第三个测试文本"
                ]
                print(f"🧪 批量测试文本数量: {len(test_texts)}")

                batch_embeddings = await service.get_batch_embeddings(test_texts)
                print(f"✅ 批量向量生成成功 - 向量数量: {len(batch_embeddings)}")

                for i, emb in enumerate(batch_embeddings):
                    print(f"📋 第{i+1}个向量维度: {len(emb)}, 前3维: {emb[:3]}")

                # 测试服务健康检查
                health_status = await service.check_health()
                print(f"🏥 服务健康状态: {health_status}")

                # 测试性能统计
                perf_stats = service.get_performance_stats()
                print(f"📈 性能统计: {perf_stats}")

            print("🎉 BGE-M3向量服务模块测试完成！")

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()

    # 运行测试
    asyncio.run(test_bge_service())