"""
智能记忆引擎 MVP v2.0 - 向量服务抽象基类

定义向量服务的标准接口，支持多种向量模型的统一管理。
提供健康检查、批量处理、错误恢复等核心功能的抽象定义。

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 14:06:34
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from contextlib import asynccontextmanager

# 配置日志记录器
logger = logging.getLogger("smart_memory.embedding_base")


class BaseEmbeddingError(Exception):
    """向量服务基础异常"""
    pass


class EmbeddingServiceHealthError(Exception):
    """向量服务健康检查异常"""
    pass


class BaseEmbeddingService(ABC):
    """
    向量服务抽象基类
    
    定义所有向量服务的标准接口和基础功能，包括：
    1. 单个文本和批量文本向量生成
    2. 服务健康状态监控和检查
    3. 自动重试和错误恢复机制
    4. 连接管理和资源清理
    5. 性能监控和统计
    """

    def __init__(self, service_name: str = "EmbeddingService"):
        """
        初始化向量服务基类
        
        Args:
            service_name: 服务名称标识
        """
        self.service_name = service_name
        
        # 服务状态跟踪
        self._is_initialized = False
        self._last_health_check = 0
        self._health_check_interval = 300  # 5分钟
        self._service_available = False
        
        # 性能统计
        self._total_requests = 0
        self._successful_requests = 0
        self._failed_requests = 0
        self._total_processing_time = 0.0
        
        logger.info(f"向量服务 {self.service_name} 基类初始化完成")

    # ================== 抽象方法定义 ==================
    
    @abstractmethod
    async def initialize(self) -> bool:
        """
        初始化向量服务连接
        
        Returns:
            bool: 初始化是否成功
            
        Raises:
            EmbeddingServiceHealthError: 当服务初始化失败时
        """
        pass

    @abstractmethod
    async def get_embedding(self, text: str) -> List[float]:
        """
        生成单个文本的向量表示
        
        Args:
            text: 输入文本
            
        Returns:
            List[float]: 向量表示
            
        Raises:
            BaseEmbeddingError: 当向量生成失败时
            ValueError: 当输入参数无效时
        """
        pass

    @abstractmethod
    async def get_batch_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        批量生成文本向量表示
        
        Args:
            texts: 文本列表
            
        Returns:
            List[List[float]]: 向量列表
            
        Raises:
            BaseEmbeddingError: 当批量向量生成失败时
            ValueError: 当输入参数无效时
        """
        pass

    @abstractmethod
    async def _perform_health_check(self) -> bool:
        """
        执行服务健康检查的具体实现
        
        Returns:
            bool: 服务是否健康
            
        Raises:
            EmbeddingServiceHealthError: 当服务不可用时
        """
        pass

    @abstractmethod
    async def cleanup(self):
        """清理资源和关闭连接的具体实现"""
        pass

    @abstractmethod
    def get_service_config(self) -> Dict[str, Any]:
        """
        获取服务配置信息
        
        Returns:
            Dict[str, Any]: 服务配置详情
        """
        pass

    # ================== 通用实现方法 ==================

    async def is_service_healthy(self) -> bool:
        """
        检查服务是否健康（公共接口）
        
        Returns:
            bool: 服务健康状态
        """
        current_time = time.time()
        
        # 检查是否需要进行健康检查（避免频繁检查）
        if (current_time - self._last_health_check) < self._health_check_interval and self._service_available:
            return True
        
        try:
            result = await self._perform_health_check()
            self._last_health_check = current_time
            self._service_available = result
            return result
        except EmbeddingServiceHealthError:
            self._service_available = False
            return False
        except Exception as e:
            logger.error(f"健康检查异常: {e}")
            self._service_available = False
            return False

    def _validate_text_input(self, text: str) -> str:
        """
        验证并清理文本输入
        
        Args:
            text: 原始文本
            
        Returns:
            str: 清理后的文本
            
        Raises:
            ValueError: 输入无效时
        """
        if not text:
            raise ValueError("输入文本不能为空")
        
        cleaned_text = text.strip()
        if not cleaned_text:
            raise ValueError("输入文本不能为空白字符")
            
        return cleaned_text

    def _validate_texts_input(self, texts: List[str]) -> List[str]:
        """
        验证并清理批量文本输入
        
        Args:
            texts: 原始文本列表
            
        Returns:
            List[str]: 清理后的文本列表
            
        Raises:
            ValueError: 输入无效时
        """
        if not texts:
            raise ValueError("文本列表不能为空")
        
        if not isinstance(texts, list):
            raise ValueError("输入必须是文本列表")
        
        cleaned_texts = []
        for i, text in enumerate(texts):
            try:
                cleaned_text = self._validate_text_input(text)
                cleaned_texts.append(cleaned_text)
            except ValueError as e:
                raise ValueError(f"第{i+1}个文本输入无效: {e}")
        
        return cleaned_texts

    def _record_request_start(self) -> float:
        """记录请求开始时间"""
        self._total_requests += 1
        return time.time()

    def _record_request_success(self, start_time: float):
        """记录成功请求"""
        self._successful_requests += 1
        self._total_processing_time += time.time() - start_time

    def _record_request_failure(self, start_time: float):
        """记录失败请求"""
        self._failed_requests += 1
        self._total_processing_time += time.time() - start_time

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            Dict[str, Any]: 性能统计数据
        """
        success_rate = 0.0
        avg_processing_time = 0.0
        
        if self._total_requests > 0:
            success_rate = self._successful_requests / self._total_requests
            avg_processing_time = self._total_processing_time / self._total_requests
        
        return {
            "service_name": self.service_name,
            "total_requests": self._total_requests,
            "successful_requests": self._successful_requests,
            "failed_requests": self._failed_requests,
            "success_rate": success_rate,
            "average_processing_time_seconds": avg_processing_time,
            "total_processing_time_seconds": self._total_processing_time,
            "is_initialized": self._is_initialized,
            "is_available": self._service_available,
            "last_health_check": self._last_health_check
        }

    def get_service_info(self) -> Dict[str, Any]:
        """
        获取服务完整信息
        
        Returns:
            Dict[str, Any]: 服务详细信息
        """
        return {
            "service_info": {
                "name": self.service_name,
                "version": "v2.0",
                "is_initialized": self._is_initialized,
                "is_available": self._service_available,
                "last_health_check": self._last_health_check,
                "health_check_interval": self._health_check_interval
            },
            "performance_stats": self.get_performance_stats(),
            "service_config": self.get_service_config()
        }

    async def check_health(self) -> Dict[str, Any]:
        """
        检查服务健康状态并返回详细信息
        
        Returns:
            Dict[str, Any]: 健康状态信息
        """
        health_status = {
            "service": self.service_name,
            "timestamp": time.time(),
            "status": "unknown",
            "details": {}
        }
        
        try:
            is_healthy = await self.is_service_healthy()
            
            health_status["status"] = "healthy" if is_healthy else "unhealthy"
            health_status["details"] = {
                "is_initialized": self._is_initialized,
                "is_available": self._service_available,
                "last_health_check": self._last_health_check,
                "performance_stats": self.get_performance_stats(),
                "service_config": self.get_service_config()
            }
            
        except Exception as e:
            health_status["status"] = "error"
            health_status["details"]["error"] = str(e)
            logger.error(f"健康检查失败: {e}")
        
        return health_status

    @asynccontextmanager
    async def managed_service(self):
        """
        上下文管理器，自动管理服务生命周期
        
        使用示例:
            async with embedding_service.managed_service():
                embedding = await embedding_service.get_embedding("测试文本")
        """
        try:
            if not self._is_initialized:
                await self.initialize()
            yield self
        finally:
            await self.cleanup()

    def reset_performance_stats(self):
        """重置性能统计数据"""
        self._total_requests = 0
        self._successful_requests = 0
        self._failed_requests = 0
        self._total_processing_time = 0.0
        logger.info(f"已重置 {self.service_name} 性能统计数据")

    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.service_name}(initialized={self._is_initialized}, available={self._service_available})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"{self.__class__.__name__}("
                f"service_name='{self.service_name}', "
                f"initialized={self._is_initialized}, "
                f"available={self._service_available}, "
                f"total_requests={self._total_requests})")