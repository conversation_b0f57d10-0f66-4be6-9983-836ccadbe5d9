"""
智能记忆引擎 - LLM陈述提取器

基于大语言模型的智能知识陈述提取服务：
- 使用OpenAI/Gemini API进行陈述提取
- 支持三元组(主语-谓语-宾语)结构化提取
- 智能提示词构建和响应解析
- 完整的错误处理和重试机制
- 自动向量生成集成

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 15:10:14
"""

import json
import time
import logging
from typing import List, Dict, Any, Optional

from .extraction_base import BaseExtractionService, StatementExtractionError
from ..llm import LLMError, LLMConnectionError, LLMContentError


class LLMStatementExtractor(BaseExtractionService):
    """
    基于LLM的陈述提取器

    使用大语言模型进行智能知识陈述提取：
    1. 构建优化的三元组提取提示词
    2. 调用LLM API进行知识陈述识别
    3. 解析和验证返回结果
    4. 集成向量生成服务
    5. 提供完整的错误处理
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None, logger: Optional[logging.Logger] = None):
        super().__init__(
            service_name="llm_statement_extractor",
            config=config,
            logger=logger or logging.getLogger("smart_memory.llm_statement_extractor")
        )

        # LLM提取特定配置
        self.max_statements_per_request = 30  # 单次请求最大陈述数量
        self.temperature = 0.1  # 低温度确保稳定性
        self.max_retries = 3  # 最大重试次数
        
        # 陈述质量控制
        self.min_statement_length = 5  # 最短陈述长度
        self.max_statement_length = 200  # 最长陈述长度

    async def _initialize_extractor(self) -> None:
        """初始化LLM陈述提取器"""
        if not self.llm_service or not self.llm_service.is_ready:
            raise StatementExtractionError("LLM服务未初始化或不可用")

        self.logger.info("LLM陈述提取器初始化完成")

    async def _cleanup_extractor(self) -> None:
        """清理LLM陈述提取器"""
        self.logger.info("LLM陈述提取器清理完成")

    async def extract_entities(self, content: str) -> List[Dict[str, Any]]:
        """
        LLM陈述提取器不实现实体提取
        这个功能由专门的实体提取器实现
        """
        self.logger.info("LLM陈述提取器不支持实体提取，请使用专门的实体提取器")
        return []

    async def extract_statements(self, content: str, entities: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        使用LLM提取知识陈述

        Args:
            content: 输入文本内容
            entities: 已提取的实体列表（可选，用于提高提取准确性）

        Returns:
            List[Dict[str, Any]]: 提取的陈述列表

        Raises:
            StatementExtractionError: 陈述提取失败时
        """
        if not content or not content.strip():
            raise ValueError("输入内容不能为空")

        if len(content.strip()) < 20:
            self.logger.warning("输入内容过短，可能影响陈述提取效果")

        try:
            start_time = time.time()
            self.logger.debug(f"开始LLM陈述提取 - 内容长度: {len(content)}")

            # 调用LLM服务进行陈述提取
            statements = await self._call_llm_extract_statements(content, entities)

            # 验证和清理陈述数据
            valid_statements = self.validate_and_clean_statements(statements)

            # 为陈述生成向量
            if valid_statements:
                valid_statements = await self.add_embeddings_to_statements(valid_statements)

            # 更新性能指标
            processing_time = time.time() - start_time
            self.update_metrics(True, processing_time)

            self.logger.info(
                f"LLM陈述提取完成 - 提取到 {len(valid_statements)} 个陈述，耗时: {processing_time:.2f}s"
            )
            return valid_statements

        except StatementExtractionError:
            # 更新错误指标
            processing_time = time.time() - start_time if 'start_time' in locals() else 0
            self.update_metrics(False, processing_time)
            raise
        except Exception as e:
            # 更新错误指标
            processing_time = time.time() - start_time if 'start_time' in locals() else 0
            self.update_metrics(False, processing_time)
            error_msg = f"LLM陈述提取失败: {e}"
            self.logger.error(error_msg)
            raise StatementExtractionError(error_msg)

    async def _call_llm_extract_statements(self, content: str, entities: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        调用LLM服务进行陈述提取

        Args:
            content: 输入文本内容
            entities: 已提取的实体列表（可选）

        Returns:
            List[Dict[str, Any]]: LLM返回的陈述列表

        Raises:
            StatementExtractionError: LLM调用失败时
        """
        if not self.llm_service or not self.llm_service.is_ready:
            raise StatementExtractionError("LLM服务不可用")

        try:
            # 构建提示词
            prompt = self._build_statement_extraction_prompt(content, entities)

            # 调用LLM服务
            response = await self.llm_service.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                temperature=self.temperature,
                max_retries=self.max_retries
            )

            # 解析响应
            statements = self._parse_statement_response(response)

            self.logger.debug(f"LLM陈述提取原始结果: {len(statements)} 个陈述")
            return statements

        except (LLMError, LLMConnectionError, LLMContentError) as e:
            error_msg = f"LLM陈述提取调用失败: {e}"
            self.logger.error(error_msg)
            raise StatementExtractionError(error_msg)
        except Exception as e:
            error_msg = f"LLM陈述提取处理异常: {e}"
            self.logger.error(error_msg)
            raise StatementExtractionError(error_msg)

    def _build_statement_extraction_prompt(self, content: str, entities: Optional[List[Dict[str, Any]]] = None) -> str:
        """
        构建陈述提取的提示词

        Args:
            content: 输入文本内容
            entities: 已提取的实体列表（可选）

        Returns:
            str: 构建的提示词
        """
        # 构建实体信息
        entity_info = ""
        if entities:
            entity_names = [entity.get("name", "") for entity in entities if entity.get("name")]
            if entity_names:
                entity_info = f"\n已识别的实体: {', '.join(entity_names)}"

        prompt = f"""请从以下中文文本中提取重要的知识陈述，返回JSON格式的结果。

文本内容：
{content}
{entity_info}

提取要求：
1. 识别文本中的重要事实和关系
2. 将每个事实表示为三元组结构：主语-谓语-宾语
3. 主语和宾语优先使用已识别的实体
4. 提供完整的事实描述
5. 评估陈述的可信度 (0.0-1.0)
6. 避免过于泛化或常识性的陈述
7. 确保陈述具有明确的信息价值

返回JSON格式：
{{
  "statements": [
    {{
      "subject": "主语",
      "predicate": "谓语/关系",
      "object": "宾语",
      "fact": "完整事实描述",
      "confidence": 0.9
    }}
  ]
}}

只返回JSON格式的结果，不要包含其他解释文本。"""

        return prompt

    def _parse_statement_response(self, response: str) -> List[Dict[str, Any]]:
        """
        解析LLM返回的陈述提取结果

        Args:
            response: LLM返回的原始响应

        Returns:
            List[Dict[str, Any]]: 解析后的陈述列表

        Raises:
            StatementExtractionError: 解析失败时
        """
        try:
            # 清理JSON响应
            clean_response = self._clean_json_response(response)

            # 解析JSON
            result = json.loads(clean_response)

            # 提取陈述列表
            statements = result.get("statements", [])

            if not isinstance(statements, list):
                raise StatementExtractionError("LLM返回格式错误：statements应为列表")

            # 为每个陈述添加源信息
            for statement in statements:
                statement["source"] = "llm_extraction"

            self.logger.debug(f"成功解析 {len(statements)} 个陈述")
            return statements

        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            self.logger.error(f"原始响应: {response[:200]}...")
            raise StatementExtractionError(f"LLM响应JSON解析失败: {e}")
        except Exception as e:
            self.logger.error(f"陈述响应解析异常: {e}")
            raise StatementExtractionError(f"陈述响应解析失败: {e}")

    def _clean_json_response(self, response: str) -> str:
        """
        清理LLM返回的JSON响应

        Args:
            response: 原始响应文本

        Returns:
            str: 清理后的JSON文本
        """
        # 去除前后空白
        clean_text = response.strip()

        # 移除可能的markdown代码块标记
        if clean_text.startswith('```json'):
            clean_text = clean_text[7:]
        elif clean_text.startswith('```'):
            clean_text = clean_text[3:]

        if clean_text.endswith('```'):
            clean_text = clean_text[:-3]

        # 去除多余空白
        clean_text = clean_text.strip()

        # 查找JSON对象的开始和结束
        start_idx = clean_text.find('{')
        end_idx = clean_text.rfind('}')

        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            clean_text = clean_text[start_idx:end_idx+1]

        return clean_text

    def validate_and_clean_statements(self, statements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        验证和清理陈述数据（覆盖基类方法以添加特定验证）

        Args:
            statements: 原始陈述列表

        Returns:
            List[Dict[str, Any]]: 清理后的陈述列表
        """
        # 调用基类验证
        valid_statements = super().validate_and_clean_statements(statements)

        # 添加特定的长度和质量验证
        filtered_statements = []
        for statement in valid_statements:
            fact = statement.get("fact", "")
            
            # 长度验证
            if len(fact) < self.min_statement_length:
                self.logger.debug(f"陈述过短，跳过: {fact}")
                continue
                
            if len(fact) > self.max_statement_length:
                self.logger.debug(f"陈述过长，截断: {fact[:50]}...")
                statement["fact"] = fact[:self.max_statement_length]
                
            # 质量验证：避免过于简单的陈述
            if self._is_trivial_statement(statement):
                self.logger.debug(f"陈述过于简单，跳过: {fact}")
                continue
                
            filtered_statements.append(statement)

        self.logger.debug(
            f"陈述质量过滤完成 - 输入: {len(valid_statements)}, 输出: {len(filtered_statements)}"
        )
        return filtered_statements

    def _is_trivial_statement(self, statement: Dict[str, Any]) -> bool:
        """
        判断陈述是否过于简单或常识性

        Args:
            statement: 陈述字典

        Returns:
            bool: 如果是简单陈述返回True
        """
        fact = statement.get("fact", "").lower()
        subject = statement.get("subject", "").lower()
        predicate = statement.get("predicate", "").lower()
        obj = statement.get("object", "").lower()

        # 过滤常见的简单关系
        trivial_patterns = [
            "是一个", "是一种", "属于", "包含", "有", "存在",
            "位于", "在", "的", "和", "与", "或"
        ]

        # 检查谓语是否过于简单
        if any(pattern in predicate for pattern in trivial_patterns):
            if len(subject) < 3 or len(obj) < 3:
                return True

        # 检查整个事实是否过于简单
        if len(fact.replace(" ", "")) < 8:
            return True

        return False

    def get_extraction_stats(self) -> Dict[str, Any]:
        """获取提取器性能统计"""
        base_stats = self.get_service_info()

        llm_stats = {}
        if self.llm_service:
            llm_stats = self.llm_service.get_service_info()

        return {
            **base_stats,
            "extractor_type": "llm_statement",
            "max_statements_per_request": self.max_statements_per_request,
            "temperature": self.temperature,
            "max_retries": self.max_retries,
            "min_statement_length": self.min_statement_length,
            "max_statement_length": self.max_statement_length,
            "llm_service": llm_stats
        }