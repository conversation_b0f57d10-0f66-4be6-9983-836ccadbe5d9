"""
智能记忆引擎 - LLM实体提取器

基于大语言模型的智能实体识别和抽取服务：
- 使用OpenAI/Gemini API进行实体提取
- 支持多种实体类型识别
- 智能提示词构建和响应解析
- 完整的错误处理和重试机制
- 自动向量生成集成

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 15:10:14
"""

import json
import re
import logging
from typing import List, Dict, Any, Optional

from .extraction_base import BaseExtractionService, EntityExtractionError
from ..llm import LLMError, LLMConnectionError, LLMContentError


class LLMEntityExtractor(BaseExtractionService):
    """
    基于LLM的实体提取器
    
    使用大语言模型进行智能实体识别：
    1. 构建优化的提示词模板
    2. 调用LLM API进行实体识别  
    3. 解析和验证返回结果
    4. 集成向量生成服务
    5. 提供完整的错误处理
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, logger: Optional[logging.Logger] = None):
        super().__init__(
            service_name="llm_entity_extractor",
            config=config,
            logger=logger or logging.getLogger("smart_memory.llm_entity_extractor")
        )
        
        # LLM提取特定配置
        self.max_entities_per_request = 50  # 单次请求最大实体数量
        self.temperature = 0.1  # 低温度确保稳定性
        self.max_retries = 3  # 最大重试次数
        
    async def _initialize_extractor(self) -> None:
        """初始化LLM实体提取器"""
        if not self.llm_service or not self.llm_service.is_ready:
            raise EntityExtractionError("LLM服务未初始化或不可用")
            
        self.logger.info("LLM实体提取器初始化完成")
        
    async def _cleanup_extractor(self) -> None:
        """清理LLM实体提取器"""
        self.logger.info("LLM实体提取器清理完成")
        
    async def extract_entities(self, content: str) -> List[Dict[str, Any]]:
        """
        使用LLM提取实体
        
        Args:
            content: 输入文本内容
            
        Returns:
            List[Dict[str, Any]]: 提取的实体列表
            
        Raises:
            EntityExtractionError: 实体提取失败时
        """
        if not content or not content.strip():
            raise ValueError("输入内容不能为空")
            
        if len(content.strip()) < 10:
            self.logger.warning("输入内容过短，可能影响实体提取效果")
            
        try:
            start_time = time.time()
            self.logger.debug(f"开始LLM实体提取 - 内容长度: {len(content)}")
            
            # 调用LLM服务进行实体提取
            entities = await self._call_llm_extract_entities(content)
            
            # 验证和清理实体数据
            valid_entities = self.validate_and_clean_entities(entities)
            
            # 为实体生成向量
            if valid_entities:
                valid_entities = await self.add_embeddings_to_entities(valid_entities)
            
            # 更新性能指标
            processing_time = time.time() - start_time
            self.update_metrics(True, processing_time)
            
            self.logger.info(f"LLM实体提取完成 - 提取到 {len(valid_entities)} 个实体，耗时: {processing_time:.2f}s")
            return valid_entities
            
        except EntityExtractionError:
            # 更新错误指标
            processing_time = time.time() - start_time if 'start_time' in locals() else 0
            self.update_metrics(False, processing_time)
            raise
        except Exception as e:
            # 更新错误指标
            processing_time = time.time() - start_time if 'start_time' in locals() else 0
            self.update_metrics(False, processing_time)
            error_msg = f"LLM实体提取失败: {e}"
            self.logger.error(error_msg)
            raise EntityExtractionError(error_msg)
    
    async def extract_statements(self, content: str, entities: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        LLM实体提取器不实现陈述提取
        这个功能由专门的陈述提取器实现
        """
        self.logger.info("LLM实体提取器不支持陈述提取，请使用专门的陈述提取器")
        return []
    
    async def _call_llm_extract_entities(self, content: str) -> List[Dict[str, Any]]:
        """
        调用LLM服务进行实体提取
        
        Args:
            content: 输入文本内容
            
        Returns:
            List[Dict[str, Any]]: LLM返回的实体列表
            
        Raises:
            EntityExtractionError: LLM调用失败时
        """
        if not self.llm_service or not self.llm_service.is_ready:
            raise EntityExtractionError("LLM服务不可用")
            
        try:
            # 构建提示词
            prompt = self._build_entity_extraction_prompt(content)
            
            # 调用LLM服务
            response = await self.llm_service.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                temperature=self.temperature,
                max_retries=self.max_retries
            )
            
            # 解析响应
            entities = self._parse_entity_response(response)
            
            self.logger.debug(f"LLM实体提取原始结果: {len(entities)} 个实体")
            return entities
            
        except (LLMError, LLMConnectionError, LLMContentError) as e:
            error_msg = f"LLM实体提取调用失败: {e}"
            self.logger.error(error_msg)
            raise EntityExtractionError(error_msg)
        except Exception as e:
            error_msg = f"LLM实体提取处理异常: {e}"
            self.logger.error(error_msg)
            raise EntityExtractionError(error_msg)
    
    def _build_entity_extraction_prompt(self, content: str) -> str:
        """
        构建实体提取的提示词
        
        Args:
            content: 输入文本内容
            
        Returns:
            str: 构建的提示词
        """
        entity_types_desc = "\n".join([f"- {et}: {self._get_entity_type_description(et)}" for et in self.entity_types])
        
        prompt = f"""请从以下中文文本中提取实体信息，返回JSON格式的结果。

文本内容：
{content}

请识别以下类型的实体：
{entity_types_desc}

提取要求：
1. 准确识别实体类型和名称
2. 为每个实体提供简洁的描述
3. 评估实体识别的置信度 (0.0-1.0)
4. 避免提取过于泛化的概念
5. 确保实体名称具有明确的指代性

返回JSON格式：
{{
  "entities": [
    {{
      "name": "实体名称",
      "type": "实体类型",
      "description": "简要描述",
      "confidence": 0.9
    }}
  ]
}}

只返回JSON格式的结果，不要包含其他解释文本。"""
        
        return prompt
    
    def _get_entity_type_description(self, entity_type: str) -> str:
        """获取实体类型的描述"""
        descriptions = {
            "Person": "人物姓名，包括真实人物、虚拟角色等",
            "Organization": "组织机构，包括公司、政府部门、学校、NGO等",
            "Location": "地理位置，包括国家、城市、地标、建筑物等",
            "Concept": "抽象概念，包括理论、技术、思想、方法论等",
            "Event": "具体事件，包括历史事件、会议、活动等",
            "Product": "具体产品，包括商品、软件、服务、作品等",
            "Time": "时间表达，包括日期、时期、时间段等"
        }
        return descriptions.get(entity_type, "未知类型的实体")
    
    def _parse_entity_response(self, response: str) -> List[Dict[str, Any]]:
        """
        解析LLM返回的实体提取结果
        
        Args:
            response: LLM返回的原始响应
            
        Returns:
            List[Dict[str, Any]]: 解析后的实体列表
            
        Raises:
            EntityExtractionError: 解析失败时
        """
        try:
            # 清理JSON响应
            clean_response = self._clean_json_response(response)
            
            # 解析JSON
            result = json.loads(clean_response)
            
            # 提取实体列表
            entities = result.get("entities", [])
            
            if not isinstance(entities, list):
                raise EntityExtractionError("LLM返回格式错误：entities应为列表")
                
            # 为每个实体添加源信息
            for entity in entities:
                entity["source"] = "llm_extraction"
                
            self.logger.debug(f"成功解析 {len(entities)} 个实体")
            return entities
            
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            self.logger.error(f"原始响应: {response[:200]}...")
            raise EntityExtractionError(f"LLM响应JSON解析失败: {e}")
        except Exception as e:
            self.logger.error(f"实体响应解析异常: {e}")
            raise EntityExtractionError(f"实体响应解析失败: {e}")
    
    def _clean_json_response(self, response: str) -> str:
        """
        清理LLM返回的JSON响应
        
        Args:
            response: 原始响应文本
            
        Returns:
            str: 清理后的JSON文本
        """
        # 去除前后空白
        clean_text = response.strip()
        
        # 移除可能的markdown代码块标记
        if clean_text.startswith('```json'):
            clean_text = clean_text[7:]
        elif clean_text.startswith('```'):
            clean_text = clean_text[3:]
            
        if clean_text.endswith('```'):
            clean_text = clean_text[:-3]
        
        # 去除多余空白
        clean_text = clean_text.strip()
        
        # 查找JSON对象的开始和结束
        start_idx = clean_text.find('{')
        end_idx = clean_text.rfind('}')
        
        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            clean_text = clean_text[start_idx:end_idx+1]
        
        return clean_text
    
    def get_extraction_stats(self) -> Dict[str, Any]:
        """获取提取器性能统计"""
        base_stats = self.get_service_info()
        
        llm_stats = {}
        if self.llm_service:
            llm_stats = self.llm_service.get_service_info()
        
        return {
            **base_stats,
            "extractor_type": "llm",
            "max_entities_per_request": self.max_entities_per_request,
            "temperature": self.temperature,
            "max_retries": self.max_retries,
            "llm_service": llm_stats
        }


# 导入时间模块
import time