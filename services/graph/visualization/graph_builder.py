"""
智能记忆引擎 - 图谱可视化构建器

专门负责知识图谱可视化数据生成的服务：
- 从knowledge_service.py提取get_graph_data功能
- 生成vis.js兼容的图数据结构
- 提供颜色方案、布局配置、提示生成等辅助方法
- 继承BaseService提供标准服务接口
- 支持多种布局算法和颜色方案

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 15:33:51
"""

import json
import logging
import time
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone

from ...core.base_service import BaseService, ServiceHealthCheck, ServiceStatus
from ...graph.connection.neo4j_driver import Neo4jDriverService as Neo4jConnectionManager
from models import NodeType, RelationshipType

# 配置日志记录器
logger = logging.getLogger("smart_memory.graph_visualization")


class GraphVisualizationError(Exception):
    """图谱可视化异常"""
    pass


class GraphVisualizationBuilder(BaseService):
    """
    图谱可视化构建器
    
    核心功能：
    1. 从Neo4j数据库获取图数据
    2. 生成vis.js兼容的节点和边数据
    3. 提供多种颜色方案和布局配置
    4. 生成交互式提示信息
    5. 支持过滤和聚合功能
    """
    
    def __init__(self, 
                 connection_manager: Optional[Neo4jConnectionManager] = None,
                 config: Optional[Dict[str, Any]] = None, 
                 logger: Optional[logging.Logger] = None):
        super().__init__(
            service_name="graph_visualization_builder",
            config=config or {},
            logger=logger or logging.getLogger("smart_memory.graph_visualization")
        )
        
        # Neo4j连接管理器
        self.connection_manager = connection_manager
        self._should_cleanup_connection = connection_manager is None
        
        # 可视化配置
        self.default_node_limit = self.config.get("default_node_limit", 100)
        self.max_node_limit = self.config.get("max_node_limit", 500)
        self.default_color_scheme = self.config.get("default_color_scheme", "default")
        self.default_layout = self.config.get("default_layout", "force-directed")
        
        # 缓存配置
        self.enable_caching = self.config.get("enable_caching", True)
        self.cache_ttl = self.config.get("cache_ttl", 300)  # 5分钟
        self._graph_cache: Dict[str, Dict[str, Any]] = {}
        
        # 性能配置
        self.enable_clustering = self.config.get("enable_clustering", True)
        self.cluster_threshold = self.config.get("cluster_threshold", 50)
        
    async def _initialize_service(self) -> None:
        """初始化图谱可视化构建器"""
        try:
            self.logger.info("开始初始化图谱可视化构建器...")
            
            # 如果没有提供连接管理器，则从配置创建驱动
            if self.connection_manager is None:
                from neo4j import AsyncGraphDatabase
                from config import settings
                neo4j_config = settings.get_neo4j_config()
                self.connection_manager = AsyncGraphDatabase.driver(
                    neo4j_config["uri"],
                    auth=neo4j_config["auth"]
                )
                self._should_cleanup_connection = True
            
            # 验证数据库连接
            from config import settings
            database_name = settings.get_neo4j_config()["database"]
            async with self.connection_manager.session(database=database_name) as session:
                result = await session.run("RETURN 1 AS health_check")
                record = await result.single()
                if not record or record["health_check"] != 1:
                    raise Exception("Neo4j数据库连接测试失败")
            
            self.logger.info("✅ 图谱可视化构建器初始化完成")
            
        except Exception as e:
            self.logger.error(f"图谱可视化构建器初始化失败: {e}")
            raise
    
    async def _cleanup_service(self) -> None:
        """清理图谱可视化构建器"""
        self.logger.info("开始清理图谱可视化构建器...")
        
        # 清理缓存
        self._graph_cache.clear()
        
        # 如果连接管理器是我们创建的，则清理它
        if self._should_cleanup_connection and self.connection_manager:
            try:
                await self.connection_manager.close()
            except Exception as e:
                self.logger.warning(f"清理连接管理器失败: {e}")
        
        self.connection_manager = None
        self.logger.info("✅ 图谱可视化构建器清理完成")
    
    async def _perform_health_check(self) -> ServiceHealthCheck:
        """执行健康检查"""
        try:
            start_time = time.time()
            details = {}
            
            # 检查连接管理器状态
            if self.connection_manager:
                try:
                    # 测试连接健康性
                    from config import settings
                    database_name = settings.get_neo4j_config()["database"]
                    async with self.connection_manager.session(database=database_name) as session:
                        result = await session.run("RETURN 1 AS health_check")
                        record = await result.single()
                        is_healthy = record and record["health_check"] == 1
                    
                    details["neo4j_connection"] = {
                        "status": "healthy" if is_healthy else "unhealthy",
                        "connection_type": "AsyncBoltDriver"
                    }
                    
                    if is_healthy:
                        status = ServiceStatus.READY
                        message = "图谱可视化构建器运行正常"
                    else:
                        status = ServiceStatus.ERROR
                        message = "Neo4j连接测试失败"
                except Exception as e:
                    status = ServiceStatus.ERROR
                    message = f"Neo4j连接检查异常: {e}"
                    details["neo4j_connection"] = {"status": "error", "error": str(e)}
            else:
                status = ServiceStatus.ERROR
                message = "连接管理器未初始化"
                details["neo4j_connection"] = {"status": "not_initialized"}
            
            # 添加缓存状态
            details["cache"] = {
                "enabled": self.enable_caching,
                "cached_graphs": len(self._graph_cache),
                "cache_ttl": self.cache_ttl
            }
            
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=status,
                message=message,
                details=details,
                response_time=time.time() - start_time
            )
            
        except Exception as e:
            self.logger.error(f"图谱可视化构建器健康检查失败: {e}")
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=ServiceStatus.ERROR,
                message=f"健康检查异常: {str(e)}",
                response_time=time.time() - start_time
            )
    
    # ================== 核心可视化功能 ==================
    
    async def build_graph_data(self,
                              node_types: Optional[List[NodeType]] = None,
                              limit: int = None,
                              include_metadata: bool = True,
                              layout_algorithm: str = None,
                              color_scheme: str = None,
                              enable_clustering: Optional[bool] = None,
                              filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        构建图谱可视化数据
        
        Args:
            node_types: 要包含的节点类型列表
            limit: 节点数量限制
            include_metadata: 是否包含详细元数据
            layout_algorithm: 布局算法 (force-directed, hierarchical, circular)
            color_scheme: 色彩方案 (default, dark, colorful, custom)
            enable_clustering: 是否启用聚类
            filters: 过滤条件
            
        Returns:
            Dict[str, Any]: vis.js兼容的图数据结构
            
        Raises:
            GraphVisualizationError: 数据构建失败时
        """
        if not self.is_ready:
            await self.initialize()
        
        # 应用默认值
        limit = min(limit or self.default_node_limit, self.max_node_limit)
        layout_algorithm = layout_algorithm or self.default_layout
        color_scheme = color_scheme or self.default_color_scheme
        enable_clustering = enable_clustering if enable_clustering is not None else self.enable_clustering
        
        # 检查缓存
        cache_key = self._generate_cache_key(node_types, limit, layout_algorithm, color_scheme, filters)
        if self.enable_caching and cache_key in self._graph_cache:
            cached_data = self._graph_cache[cache_key]
            if time.time() - cached_data["timestamp"] < self.cache_ttl:
                self.logger.debug(f"使用缓存的图数据: {cache_key}")
                return cached_data["data"]
        
        try:
            start_time = time.time()
            self.logger.info(f"开始构建图谱数据 - 限制: {limit}个节点, 布局: {layout_algorithm}, 配色: {color_scheme}")
            
            # 获取原始数据
            raw_nodes, raw_edges = await self._fetch_raw_graph_data(node_types, limit, filters)
            
            # 生成vis.js节点数据
            vis_nodes = await self._build_vis_nodes(raw_nodes, color_scheme, include_metadata)
            
            # 生成vis.js边数据
            vis_edges = await self._build_vis_edges(raw_edges, include_metadata)
            
            # 应用聚类（如果启用）
            if enable_clustering and len(vis_nodes) > self.cluster_threshold:
                vis_nodes, vis_edges = await self._apply_clustering(vis_nodes, vis_edges)
            
            # 生成布局配置
            layout_config = self._get_layout_config(layout_algorithm)
            
            # 生成统计信息
            statistics = self._generate_statistics(vis_nodes, vis_edges)
            
            # 构建完整的图数据
            graph_data = {
                "nodes": vis_nodes,
                "edges": vis_edges,
                "layout": layout_config,
                "statistics": statistics,
                "metadata": {
                    "generated_at": datetime.now(timezone.utc).isoformat(),
                    "processing_time_seconds": time.time() - start_time,
                    "color_scheme": color_scheme,
                    "layout_algorithm": layout_algorithm,
                    "node_limit": limit,
                    "include_metadata": include_metadata,
                    "clustering_enabled": enable_clustering,
                    "filter_applied": filters is not None
                }
            }
            
            # 缓存结果
            if self.enable_caching:
                self._graph_cache[cache_key] = {
                    "data": graph_data,
                    "timestamp": time.time()
                }
                
                # 清理过期缓存
                await self._cleanup_expired_cache()
            
            processing_time = time.time() - start_time
            self.logger.info(f"✅ 图谱数据构建完成 - 节点: {len(vis_nodes)}, 边: {len(vis_edges)}, 耗时: {processing_time:.2f}s")
            
            return graph_data
            
        except Exception as e:
            error_msg = f"图谱数据构建失败: {e}"
            self.logger.error(error_msg)
            raise GraphVisualizationError(error_msg)
    
    async def _fetch_raw_graph_data(self, 
                                   node_types: Optional[List[NodeType]], 
                                   limit: int,
                                   filters: Optional[Dict[str, Any]]) -> tuple:
        """从Neo4j获取原始图数据"""
        try:
            from config import settings
            database_name = settings.get_neo4j_config()["database"]
            async with self.connection_manager.session(database=database_name) as session:
                # 构建节点类型过滤条件
                node_filter = ""
                if node_types:
                    type_labels = [f"n:{node_type.value}" for node_type in node_types]
                    node_filter = f"WHERE ({' OR '.join(type_labels)})"
                
                # 添加自定义过滤条件
                additional_filters = []
                params = {"limit": limit}
                
                if filters:
                    for key, value in filters.items():
                        if key == "created_after":
                            additional_filters.append("n.created_at > $created_after")
                            params["created_after"] = value
                        elif key == "confidence_threshold":
                            additional_filters.append("n.confidence >= $confidence_threshold")
                            params["confidence_threshold"] = value
                        elif key == "tags_contain":
                            additional_filters.append("ANY(tag IN n.tags WHERE tag CONTAINS $tag_filter)")
                            params["tag_filter"] = value
                
                if additional_filters:
                    if node_filter:
                        node_filter += f" AND {' AND '.join(additional_filters)}"
                    else:
                        node_filter = f"WHERE {' AND '.join(additional_filters)}"
                
                # 获取节点数据
                nodes_query = f"""
                MATCH (n)
                {node_filter}
                RETURN n, labels(n)[0] AS node_type, id(n) AS neo4j_id
                ORDER BY n.created_at DESC
                LIMIT $limit
                """
                
                result = await session.run(nodes_query, **params)
                node_records = await result.data()
                
                # 获取节点ID列表用于查询边
                node_ids = [record["n"]["id"] for record in node_records if record["n"].get("id")]
                
                if not node_ids:
                    return [], []
                
                # 获取边数据（只包含选中节点间的边）
                edges_query = """
                MATCH (source)-[r]->(target)
                WHERE source.id IN $node_ids AND target.id IN $node_ids
                RETURN source.id AS source_id, target.id AS target_id, 
                       type(r) AS relationship_type, r, id(r) AS neo4j_rel_id
                """
                
                edges_result = await session.run(edges_query, node_ids=node_ids)
                edge_records = await edges_result.data()
                
                self.logger.debug(f"获取原始数据 - 节点: {len(node_records)}, 边: {len(edge_records)}")
                return node_records, edge_records
                
        except Exception as e:
            self.logger.error(f"获取原始图数据失败: {e}")
            raise
    
    async def _build_vis_nodes(self, 
                              node_records: List[Dict[str, Any]], 
                              color_scheme: str,
                              include_metadata: bool) -> List[Dict[str, Any]]:
        """构建vis.js节点数据"""
        vis_nodes = []
        color_map = self._get_color_scheme(color_scheme)
        
        for record in node_records:
            try:
                node = record["n"]
                node_data = dict(node.items()) if hasattr(node, 'items') else node
                node_type = record["node_type"]
                
                # 计算节点大小（基于重要性或置信度）
                importance = node_data.get("importance_score", node_data.get("confidence", 0.5))
                size = max(10, min(50, 10 + importance * 30))
                
                # 获取节点标签
                label = self._generate_node_label(node_data, node_type)
                
                # 构建vis.js节点
                vis_node = {
                    "id": node_data["id"],
                    "label": label,
                    "group": node_type,
                    "size": size,
                    "color": {
                        "background": color_map.get(node_type, color_map["default"]),
                        "border": "#2B5797",
                        "highlight": {
                            "background": "#FFA500",
                            "border": "#FF6600"
                        }
                    },
                    "font": {
                        "size": max(12, min(20, 12 + importance * 8)),
                        "color": "#343434"
                    },
                    "shape": self._get_node_shape(node_type),
                    "physics": True
                }
                
                # 添加详细元数据
                if include_metadata:
                    vis_node["title"] = self._generate_node_tooltip(node_data, node_type)
                    vis_node["metadata"] = self._extract_node_metadata(node_data, node_type)
                
                vis_nodes.append(vis_node)
                
            except Exception as e:
                self.logger.warning(f"构建节点 {record.get('neo4j_id', 'unknown')} 失败: {e}")
                continue
        
        self.logger.debug(f"构建vis.js节点完成 - 数量: {len(vis_nodes)}")
        return vis_nodes
    
    async def _build_vis_edges(self, 
                              edge_records: List[Dict[str, Any]], 
                              include_metadata: bool) -> List[Dict[str, Any]]:
        """构建vis.js边数据"""
        vis_edges = []
        
        for record in edge_records:
            try:
                rel = record.get("r", {})
                rel_data = dict(rel.items()) if hasattr(rel, 'items') else rel
                relationship_type = record["relationship_type"]
                
                # 计算边宽度（基于权重）
                weight = rel_data.get("weight", 1.0)
                width = max(1, min(10, weight * 5))
                
                # 构建vis.js边
                vis_edge = {
                    "id": rel_data.get("id", f"{record['source_id']}-{record['target_id']}-{len(vis_edges)}"),
                    "from": record["source_id"],
                    "to": record["target_id"],
                    "label": self._format_relationship_label(relationship_type),
                    "width": width,
                    "color": {
                        "color": "#848484",
                        "highlight": "#FF6600"
                    },
                    "arrows": {
                        "to": {
                            "enabled": True,
                            "scaleFactor": 0.8
                        }
                    },
                    "font": {
                        "size": 10,
                        "color": "#343434",
                        "background": "rgba(255,255,255,0.7)",
                        "strokeWidth": 1,
                        "strokeColor": "#ffffff"
                    },
                    "smooth": {
                        "type": "continuous"
                    }
                }
                
                # 添加详细元数据
                if include_metadata:
                    vis_edge["title"] = self._generate_edge_tooltip(rel_data, relationship_type)
                    vis_edge["metadata"] = self._extract_edge_metadata(rel_data, relationship_type)
                
                vis_edges.append(vis_edge)
                
            except Exception as e:
                self.logger.warning(f"构建边失败: {e}")
                continue
        
        self.logger.debug(f"构建vis.js边完成 - 数量: {len(vis_edges)}")
        return vis_edges
    
    # ================== 辅助方法 ==================
    
    def _generate_node_label(self, node_data: Dict[str, Any], node_type: str) -> str:
        """生成节点标签"""
        # 优先级：title > name > content片段
        label = (
            node_data.get("title") or 
            node_data.get("name") or 
            str(node_data.get("content", ""))[:30]
        )
        
        # 如果标签为空，使用节点类型
        if not label.strip():
            label = f"{node_type}节点"
        
        # 限制标签长度
        if len(label) > 50:
            label = label[:47] + "..."
        
        return label
    
    def _format_relationship_label(self, relationship_type: str) -> str:
        """格式化关系标签"""
        # 关系类型的中文映射
        type_mapping = {
            "CONTAINS": "包含",
            "RELATED_TO": "相关",
            "DERIVED_FROM": "派生",
            "OCCURS_IN": "发生于",
            "MENTIONS": "提及",
            "PART_OF": "部分",
            "SIMILAR_TO": "相似"
        }
        return type_mapping.get(relationship_type, relationship_type)
    
    def _get_color_scheme(self, color_scheme: str) -> Dict[str, str]:
        """获取颜色方案映射"""
        color_schemes = {
            "default": {
                "Episode": "#4285F4",     # 蓝色
                "Entity": "#34A853",      # 绿色  
                "Statement": "#FBBC04",   # 黄色
                "Concept": "#EA4335",     # 红色
                "Event": "#9AA0A6",       # 灰色
                "Person": "#FF6B9D",      # 粉色
                "Organization": "#4ECDC4", # 青色
                "Location": "#FFE66D",    # 浅黄
                "Product": "#A8E6CF",     # 浅绿
                "Time": "#D4A5FF",        # 紫色
                "default": "#4285F4"
            },
            "dark": {
                "Episode": "#1A73E8",     # 深蓝色
                "Entity": "#137333",      # 深绿色
                "Statement": "#F29900",   # 橙色
                "Concept": "#C5221F",     # 深红色
                "Event": "#5F6368",       # 深灰色
                "Person": "#E91E63",      # 深粉色
                "Organization": "#00695C", # 深青色
                "Location": "#F57F17",    # 深黄色
                "Product": "#388E3C",     # 深绿
                "Time": "#7B1FA2",        # 深紫色
                "default": "#1A73E8"
            },
            "colorful": {
                "Episode": "#FF6B6B",     # 珊瑚色
                "Entity": "#4ECDC4",      # 青蓝色
                "Statement": "#45B7D1",   # 天蓝色
                "Concept": "#96CEB4",     # 薄荷绿
                "Event": "#FECA57",       # 金黄色
                "Person": "#FF9FF3",      # 樱花粉
                "Organization": "#54A0FF", # 蔚蓝色
                "Location": "#5F27CD",    # 深紫色
                "Product": "#00D2D3",     # 青绿色
                "Time": "#FF9F43",        # 橙黄色
                "default": "#FF6B6B"
            }
        }
        return color_schemes.get(color_scheme, color_schemes["default"])
    
    def _get_node_shape(self, node_type: str) -> str:
        """获取节点形状"""
        shape_mapping = {
            "Episode": "box",
            "Entity": "dot", 
            "Statement": "diamond",
            "Concept": "triangle",
            "Event": "star",
            "Person": "circularImage",
            "Organization": "square",
            "Location": "hexagon",
            "Product": "ellipse",
            "Time": "database"
        }
        return shape_mapping.get(node_type, "dot")
    
    def _get_layout_config(self, layout_algorithm: str) -> Dict[str, Any]:
        """获取布局配置"""
        layout_configs = {
            "force-directed": {
                "randomSeed": 2,
                "improvedLayout": True,
                "clusterThreshold": 150,
                "hierarchical": {
                    "enabled": False
                },
                "physics": {
                    "enabled": True,
                    "barnesHut": {
                        "gravitationalConstant": -2000,
                        "centralGravity": 0.3,
                        "springLength": 200,
                        "springConstant": 0.04,
                        "damping": 0.09,
                        "avoidOverlap": 0.1
                    },
                    "maxVelocity": 50,
                    "minVelocity": 0.75,
                    "timestep": 0.5,
                    "stabilization": {
                        "enabled": True,
                        "iterations": 1000,
                        "updateInterval": 25
                    }
                }
            },
            "hierarchical": {
                "randomSeed": 2,
                "hierarchical": {
                    "enabled": True,
                    "levelSeparation": 150,
                    "nodeSpacing": 100,
                    "treeSpacing": 200,
                    "blockShifting": True,
                    "edgeMinimization": True,
                    "parentCentralization": True,
                    "direction": "UD",
                    "sortMethod": "directed"
                },
                "physics": {
                    "enabled": False
                }
            },
            "circular": {
                "randomSeed": 2,
                "hierarchical": {
                    "enabled": False
                },
                "physics": {
                    "enabled": True,
                    "stabilization": {
                        "enabled": True,
                        "iterations": 1000,
                        "updateInterval": 25
                    }
                }
            }
        }
        return layout_configs.get(layout_algorithm, layout_configs["force-directed"])
    
    def _generate_node_tooltip(self, node_data: Dict[str, Any], node_type: str) -> str:
        """生成节点提示信息"""
        tooltip_parts = [
            f"<div><b>{node_type}</b></div>",
            f"<div>ID: {node_data.get('id', 'N/A')[:20]}{'...' if len(str(node_data.get('id', ''))) > 20 else ''}</div>",
        ]
        
        if node_data.get("name"):
            tooltip_parts.append(f"<div>名称: {node_data['name']}</div>")
        
        if node_data.get("type"):
            tooltip_parts.append(f"<div>类型: {node_data['type']}</div>")
        
        if node_data.get("confidence"):
            tooltip_parts.append(f"<div>置信度: {node_data['confidence']:.2f}</div>")
        
        if node_data.get("created_at"):
            tooltip_parts.append(f"<div>创建时间: {node_data['created_at'][:10]}</div>")
        
        if node_data.get("content"):
            content_preview = node_data["content"][:100] + "..." if len(node_data["content"]) > 100 else node_data["content"]
            tooltip_parts.append(f"<div>内容: {content_preview}</div>")
        
        return "".join(tooltip_parts)
    
    def _generate_edge_tooltip(self, rel_data: Dict[str, Any], relationship_type: str) -> str:
        """生成边提示信息"""
        tooltip_parts = [
            f"<div><b>关系: {self._format_relationship_label(relationship_type)}</b></div>",
        ]
        
        if rel_data.get("weight"):
            tooltip_parts.append(f"<div>权重: {rel_data['weight']:.2f}</div>")
        
        if rel_data.get("confidence"):
            tooltip_parts.append(f"<div>置信度: {rel_data['confidence']:.2f}</div>")
        
        if rel_data.get("created_at"):
            tooltip_parts.append(f"<div>创建时间: {rel_data['created_at'][:10]}</div>")
        
        if rel_data.get("predicate"):
            tooltip_parts.append(f"<div>谓语: {rel_data['predicate']}</div>")
        
        return "".join(tooltip_parts)
    
    def _extract_node_metadata(self, node_data: Dict[str, Any], node_type: str) -> Dict[str, Any]:
        """提取节点元数据"""
        metadata = {
            "node_type": node_type,
            "created_at": node_data.get("created_at"),
            "confidence": node_data.get("confidence"),
            "importance_score": node_data.get("importance_score")
        }
        
        # 安全地处理properties字段
        properties = node_data.get("properties", {})
        if isinstance(properties, str):
            try:
                properties = json.loads(properties) if properties else {}
            except json.JSONDecodeError:
                properties = {}
        elif not isinstance(properties, dict):
            properties = {}
        
        metadata["properties"] = properties
        
        # 添加其他有用字段
        for key in ["tags", "source", "processing_status", "aliases", "source_episodes"]:
            if key in node_data:
                metadata[key] = node_data[key]
        
        return metadata
    
    def _extract_edge_metadata(self, rel_data: Dict[str, Any], relationship_type: str) -> Dict[str, Any]:
        """提取边元数据"""
        return {
            "relationship_type": relationship_type,
            "weight": rel_data.get("weight"),
            "confidence": rel_data.get("confidence"),
            "created_at": rel_data.get("created_at"),
            "predicate": rel_data.get("predicate"),
            "properties": rel_data.get("properties", {})
        }
    
    def _generate_statistics(self, nodes: List[Dict[str, Any]], edges: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成统计信息"""
        statistics = {
            "total_nodes": len(nodes),
            "total_edges": len(edges),
            "node_types": {},
            "relationship_types": {},
            "average_node_size": 0,
            "average_edge_width": 0
        }
        
        # 统计节点类型
        node_sizes = []
        for node in nodes:
            node_type = node["group"]
            statistics["node_types"][node_type] = statistics["node_types"].get(node_type, 0) + 1
            node_sizes.append(node.get("size", 10))
        
        # 统计关系类型
        edge_widths = []
        for edge in edges:
            rel_type = edge["label"]
            statistics["relationship_types"][rel_type] = statistics["relationship_types"].get(rel_type, 0) + 1
            edge_widths.append(edge.get("width", 1))
        
        # 计算平均值
        if node_sizes:
            statistics["average_node_size"] = sum(node_sizes) / len(node_sizes)
        if edge_widths:
            statistics["average_edge_width"] = sum(edge_widths) / len(edge_widths)
        
        return statistics
    
    async def _apply_clustering(self, nodes: List[Dict[str, Any]], edges: List[Dict[str, Any]]) -> tuple:
        """应用节点聚类（简单实现）"""
        # 这里可以实现更复杂的聚类算法
        # 当前仅按节点类型进行基础聚类
        try:
            from collections import defaultdict
            
            clusters = defaultdict(list)
            for node in nodes:
                cluster_key = node["group"]
                clusters[cluster_key].append(node)
            
            # 为每个聚类设置cluster_id
            for cluster_id, cluster_nodes in clusters.items():
                for node in cluster_nodes:
                    node["cluster_id"] = cluster_id
            
            self.logger.debug(f"应用聚类 - 聚类数量: {len(clusters)}")
            return nodes, edges
            
        except Exception as e:
            self.logger.warning(f"聚类应用失败: {e}")
            return nodes, edges
    
    # ================== 缓存管理 ==================
    
    def _generate_cache_key(self, node_types, limit, layout_algorithm, color_scheme, filters) -> str:
        """生成缓存键"""
        key_parts = [
            f"types:{','.join([nt.value for nt in (node_types or [])])}",
            f"limit:{limit}",
            f"layout:{layout_algorithm}",
            f"colors:{color_scheme}",
            f"filters:{hash(str(sorted((filters or {}).items())))}"
        ]
        return "|".join(key_parts)
    
    async def _cleanup_expired_cache(self):
        """清理过期缓存"""
        try:
            current_time = time.time()
            expired_keys = [
                key for key, data in self._graph_cache.items()
                if current_time - data["timestamp"] > self.cache_ttl
            ]
            
            for key in expired_keys:
                del self._graph_cache[key]
            
            if expired_keys:
                self.logger.debug(f"清理过期缓存 - 删除: {len(expired_keys)}个")
                
        except Exception as e:
            self.logger.warning(f"清理缓存失败: {e}")
    
    def clear_cache(self):
        """清除所有缓存"""
        self._graph_cache.clear()
        self.logger.info("图谱可视化缓存已清空")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            "enabled": self.enable_caching,
            "cached_graphs": len(self._graph_cache),
            "cache_ttl": self.cache_ttl,
            "total_size_mb": sum(
                len(str(data["data"])) for data in self._graph_cache.values()
            ) / 1024 / 1024
        }


# ================== 全局服务实例 ==================

_graph_visualization_builder: Optional[GraphVisualizationBuilder] = None


def get_graph_visualization_builder(connection_manager: Optional[Neo4jConnectionManager] = None) -> GraphVisualizationBuilder:
    """
    获取图谱可视化构建器实例（单例模式）
    
    Args:
        connection_manager: Neo4j连接管理器（可选）
        
    Returns:
        GraphVisualizationBuilder: 图谱可视化构建器实例
    """
    global _graph_visualization_builder
    if _graph_visualization_builder is None:
        _graph_visualization_builder = GraphVisualizationBuilder(connection_manager)
    return _graph_visualization_builder


# ================== 便捷函数接口 ==================

async def build_visualization_data(node_types: Optional[List[NodeType]] = None,
                                  limit: int = 100,
                                  layout_algorithm: str = "force-directed",
                                  color_scheme: str = "default") -> Dict[str, Any]:
    """
    便捷函数：构建图谱可视化数据
    
    Args:
        node_types: 节点类型过滤
        limit: 节点数量限制
        layout_algorithm: 布局算法
        color_scheme: 颜色方案
        
    Returns:
        Dict[str, Any]: vis.js兼容的图数据
    """
    builder = get_graph_visualization_builder()
    return await builder.build_graph_data(
        node_types=node_types,
        limit=limit,
        layout_algorithm=layout_algorithm,
        color_scheme=color_scheme
    )


if __name__ == "__main__":
    """图谱可视化构建器测试"""
    
    async def test_graph_visualization():
        """测试图谱可视化构建器功能"""
        print("🚀 开始测试图谱可视化构建器...")
        
        try:
            # 获取构建器实例
            builder = get_graph_visualization_builder()
            
            # 使用上下文管理器测试
            async with builder.service_context():
                print("✅ 图谱可视化构建器初始化成功")
                
                # 测试健康检查
                health_check = await builder.health_check()
                print(f"🏥 服务健康状态: {health_check.status.value}")
                print(f"📝 健康检查详情: {health_check.message}")
                
                if health_check.is_healthy():
                    # 测试图数据构建
                    graph_data = await builder.build_graph_data(
                        limit=20,
                        layout_algorithm="force-directed",
                        color_scheme="colorful",
                        include_metadata=True
                    )
                    
                    print(f"✅ 图数据构建成功:")
                    print(f"   - 节点数量: {graph_data['statistics']['total_nodes']}")
                    print(f"   - 边数量: {graph_data['statistics']['total_edges']}")
                    print(f"   - 处理耗时: {graph_data['metadata']['processing_time_seconds']:.2f}s")
                    print(f"   - 节点类型: {list(graph_data['statistics']['node_types'].keys())}")
                    
                    # 测试缓存功能
                    cache_stats = builder.get_cache_stats()
                    print(f"📊 缓存状态: {cache_stats}")
                    
                else:
                    print("⚠️ 服务不健康，跳过详细测试")
                
                print("🎉 图谱可视化构建器测试完成！")
        
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 运行测试
    import asyncio
    asyncio.run(test_graph_visualization())