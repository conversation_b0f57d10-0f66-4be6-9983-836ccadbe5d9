"""
智能记忆引擎 - Statement 节点服务

负责Statement节点的CRUD操作和业务逻辑：
- Statement节点创建、查询、更新、删除
- Statement按Episode分组查询
- Statement重要性评分和验证状态管理
- Statement搜索和关联分析
- 支持完整的生命周期管理

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 16:40:00
"""

import asyncio
import logging
import json
import time
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timezone

from neo4j import AsyncSession
from services.core.base_service import BaseService, ServiceHealthCheck, ServiceStatus
from services.utils.exceptions import (
    NodeOperationError, DatabaseException, ValidationError,
    handle_exceptions
)
from models import Statement

logger = logging.getLogger("smart_memory.statement_service")


class StatementNodeService(BaseService):
    """
    Statement节点服务
    
    负责Statement节点的完整生命周期管理：
    - CRUD操作 (创建、读取、更新、删除)
    - Episode关联管理
    - 重要性评分和验证状态
    - 批量操作和事务管理
    - 数据验证和完整性检查
    """
    
    def __init__(self, neo4j_driver, database: str = "neo4j"):
        """
        初始化Statement节点服务
        
        Args:
            neo4j_driver: Neo4j异步驱动实例
            database: 数据库名称
        """
        super().__init__(
            service_name="statement_node_service",
            config={"database": database}
        )
        
        self.neo4j_driver = neo4j_driver
        self.database = database
        
        # 缓存配置
        self._statement_cache = {}
        self._episode_statements_cache = {}  # Episode->Statements映射缓存
        self._cache_ttl = 300  # 5分钟缓存
        self._cache_max_size = 1000
        
        # Statement评分配置
        self._importance_weights = {
            "confidence": 0.4,      # 置信度权重
            "verification_status": 0.3,  # 验证状态权重
            "content_length": 0.1,  # 内容长度权重
            "subject_object_score": 0.2  # 主谓宾完整性权重
        }
        
        # 验证状态映射
        self._verification_status_scores = {
            "verified": 1.0,
            "unverified": 0.5,
            "contradicted": 0.0,
            "pending": 0.3
        }
        
        # 性能指标
        self._operation_metrics = {
            "create_operations": 0,
            "read_operations": 0,
            "update_operations": 0,
            "delete_operations": 0,
            "episode_query_operations": 0,
            "importance_calculations": 0,
            "cache_hits": 0,
            "cache_misses": 0
        }
        
        logger.info(f"Statement节点服务初始化完成 - 数据库: {database}")
    
    async def _initialize_service(self) -> None:
        """初始化服务的具体实现"""
        try:
            # 验证Neo4j驱动连接
            if not self.neo4j_driver:
                raise DatabaseException("Neo4j驱动未提供")
            
            # 测试数据库连接
            async with self.neo4j_driver.session(database=self.database) as session:
                result = await session.run("RETURN 1 AS test")
                await result.single()
            
            # 确保Statement节点索引存在
            await self._ensure_indexes()
            
            logger.info("Statement节点服务初始化成功")
            
        except Exception as e:
            logger.error(f"Statement节点服务初始化失败: {e}")
            raise DatabaseException(f"Statement节点服务初始化失败: {e}")
    
    async def _cleanup_service(self) -> None:
        """清理服务的具体实现"""
        try:
            # 清理缓存
            self._statement_cache.clear()
            self._episode_statements_cache.clear()
            
            # 记录服务统计信息
            logger.info(f"Statement服务统计 - 操作指标: {self._operation_metrics}")
            
            logger.info("Statement节点服务清理完成")
            
        except Exception as e:
            logger.error(f"Statement节点服务清理失败: {e}")
            raise
    
    async def _perform_health_check(self) -> ServiceHealthCheck:
        """执行健康检查的具体实现"""
        try:
            start_time = time.time()
            
            # 检查数据库连接
            async with self.neo4j_driver.session(database=self.database) as session:
                # 获取Statement统计信息
                result = await session.run("""
                    MATCH (s:Statement) 
                    RETURN count(s) AS statement_count,
                           avg(s.importance_score) AS avg_importance,
                           avg(s.confidence) AS avg_confidence,
                           collect(DISTINCT s.verification_status)[0..5] AS sample_statuses
                """)
                record = await result.single()
                
                statement_count = record["statement_count"] if record else 0
                avg_importance = record["avg_importance"] if record else 0.0
                avg_confidence = record["avg_confidence"] if record else 0.0
                sample_statuses = record["sample_statuses"] if record else []
            
            response_time = time.time() - start_time
            
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=ServiceStatus.READY,
                message=f"Statement服务运行正常，共有 {statement_count} 个Statement节点",
                details={
                    "statement_count": statement_count,
                    "avg_importance_score": round(avg_importance, 3),
                    "avg_confidence": round(avg_confidence, 3),
                    "sample_verification_statuses": sample_statuses,
                    "cache_size": len(self._statement_cache),
                    "episode_cache_size": len(self._episode_statements_cache),
                    "operation_metrics": self._operation_metrics.copy()
                },
                response_time=response_time
            )
            
        except Exception as e:
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=ServiceStatus.ERROR,
                message=f"Statement服务健康检查失败: {str(e)}",
                details={"error": str(e)}
            )
    
    async def _ensure_indexes(self) -> None:
        """确保必要的索引存在"""
        try:
            async with self.neo4j_driver.session(database=self.database) as session:
                # Statement节点的必要索引
                indexes = [
                    ("Statement_id_index", "Statement", "id"),
                    ("Statement_source_episode_index", "Statement", "source_episode"),
                    ("Statement_created_at_index", "Statement", "created_at"),
                    ("Statement_importance_score_index", "Statement", "importance_score"),
                    ("Statement_confidence_index", "Statement", "confidence"),
                    ("Statement_verification_status_index", "Statement", "verification_status"),
                    ("Statement_subject_index", "Statement", "subject"),
                    ("Statement_predicate_index", "Statement", "predicate"),
                    ("Statement_object_index", "Statement", "object")
                ]
                
                for index_name, label, property_name in indexes:
                    try:
                        # 检查索引是否存在
                        check_query = "SHOW INDEXES WHERE name = $index_name"
                        result = await session.run(check_query, index_name=index_name)
                        existing = await result.data()
                        
                        if not existing:
                            # 创建索引
                            create_query = f"""
                            CREATE INDEX {index_name} IF NOT EXISTS
                            FOR (n:{label}) 
                            ON (n.{property_name})
                            """
                            await session.run(create_query)
                            logger.debug(f"创建索引: {index_name}")
                        
                    except Exception as index_error:
                        # 索引创建失败不应影响服务启动
                        logger.warning(f"索引 {index_name} 创建失败: {index_error}")
            
        except Exception as e:
            logger.error(f"确保索引失败: {e}")
            raise
    
    def _serialize_for_neo4j(self, value: Any) -> Any:
        """将复杂数据类型序列化为Neo4j可接受的格式"""
        if value is None:
            return None
        elif isinstance(value, (str, int, float, bool)):
            return value
        elif isinstance(value, list):
            return [self._serialize_for_neo4j(item) for item in value]
        elif isinstance(value, dict):
            return json.dumps(value, ensure_ascii=False)
        else:
            return str(value)
    
    def _deserialize_from_neo4j(self, value: Any) -> Any:
        """将Neo4j中的数据反序列化为Python对象"""
        if isinstance(value, str):
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
        return value
    
    def _get_cache_key(self, statement_id: str) -> str:
        """生成缓存键"""
        return f"statement:{statement_id}"
    
    def _get_episode_cache_key(self, episode_id: str) -> str:
        """生成Episode Statement缓存键"""
        return f"episode_statements:{episode_id}"
    
    def _is_cache_valid(self, cached_time: float) -> bool:
        """检查缓存是否有效"""
        return time.time() - cached_time < self._cache_ttl
    
    def _update_cache(self, statement_id: str, statement_data: Dict[str, Any]) -> None:
        """更新缓存"""
        if len(self._statement_cache) >= self._cache_max_size:
            # 清理最旧的缓存项
            oldest_key = min(self._statement_cache.keys(), 
                           key=lambda k: self._statement_cache[k]['timestamp'])
            del self._statement_cache[oldest_key]
        
        cache_key = self._get_cache_key(statement_id)
        self._statement_cache[cache_key] = {
            'data': statement_data,
            'timestamp': time.time()
        }
    
    def _update_episode_cache(self, episode_id: str, statements: List[Dict[str, Any]]) -> None:
        """更新Episode Statement缓存"""
        if len(self._episode_statements_cache) >= self._cache_max_size:
            # 清理最旧的缓存项
            oldest_key = min(self._episode_statements_cache.keys(), 
                           key=lambda k: self._episode_statements_cache[k]['timestamp'])
            del self._episode_statements_cache[oldest_key]
        
        cache_key = self._get_episode_cache_key(episode_id)
        self._episode_statements_cache[cache_key] = {
            'data': statements,
            'timestamp': time.time()
        }
    
    def _get_from_cache(self, statement_id: str) -> Optional[Dict[str, Any]]:
        """从缓存获取数据"""
        cache_key = self._get_cache_key(statement_id)
        if cache_key in self._statement_cache:
            cached_item = self._statement_cache[cache_key]
            if self._is_cache_valid(cached_item['timestamp']):
                self._operation_metrics["cache_hits"] += 1
                return cached_item['data']
            else:
                # 缓存过期，删除
                del self._statement_cache[cache_key]
        
        self._operation_metrics["cache_misses"] += 1
        return None
    
    def _get_from_episode_cache(self, episode_id: str) -> Optional[List[Dict[str, Any]]]:
        """从Episode缓存获取Statements"""
        cache_key = self._get_episode_cache_key(episode_id)
        if cache_key in self._episode_statements_cache:
            cached_item = self._episode_statements_cache[cache_key]
            if self._is_cache_valid(cached_item['timestamp']):
                self._operation_metrics["cache_hits"] += 1
                return cached_item['data']
            else:
                # 缓存过期，删除
                del self._episode_statements_cache[cache_key]
        
        self._operation_metrics["cache_misses"] += 1
        return None
    
    def _calculate_importance_score(self, statement: Statement) -> float:
        """
        计算Statement重要性分数
        
        Args:
            statement: Statement实例
            
        Returns:
            float: 重要性分数 (0.0-1.0)
        """
        try:
            scores = {}
            
            # 1. 置信度分数
            scores["confidence"] = statement.confidence or 0.5
            
            # 2. 验证状态分数
            verification_status = getattr(statement, 'verification_status', 'unverified')
            scores["verification_status"] = self._verification_status_scores.get(
                verification_status, 0.5
            )
            
            # 3. 内容长度分数 (normalized to 0-1)
            content_length = len(statement.content or "")
            scores["content_length"] = min(1.0, content_length / 500)  # 500字符为满分
            
            # 4. 主谓宾完整性分数
            spo_score = 0.0
            if statement.subject and statement.subject.strip():
                spo_score += 0.4
            if statement.predicate and statement.predicate.strip():
                spo_score += 0.3
            if statement.object and statement.object.strip():
                spo_score += 0.3
            scores["subject_object_score"] = spo_score
            
            # 计算加权总分
            total_score = sum(
                scores[component] * self._importance_weights[component]
                for component in self._importance_weights
            )
            
            # 确保分数在0-1范围内
            final_score = max(0.0, min(1.0, total_score))
            
            self._operation_metrics["importance_calculations"] += 1
            
            logger.debug(f"Statement重要性分数计算: {final_score:.3f} - {scores}")
            
            return final_score
        
        except Exception as e:
            logger.warning(f"计算Statement重要性分数失败: {e}")
            return 0.5  # 默认分数
    
    def _validate_statement(self, statement: Statement) -> None:
        """验证Statement数据"""
        if not statement.id:
            raise ValidationError("Statement ID不能为空", field="id")
        
        if not statement.content or not statement.content.strip():
            raise ValidationError("Statement内容不能为空", field="content")
        
        if len(statement.content) > 10000:  # 内容长度限制
            raise ValidationError("Statement内容过长", field="content", 
                                value=len(statement.content))
        
        if statement.confidence is not None and (statement.confidence < 0 or statement.confidence > 1):
            raise ValidationError("Statement置信度必须在0-1之间", field="confidence", 
                                value=statement.confidence)
        
        if statement.importance_score is not None and (statement.importance_score < 0 or statement.importance_score > 1):
            raise ValidationError("Statement重要性分数必须在0-1之间", field="importance_score", 
                                value=statement.importance_score)
    
    @handle_exceptions(NodeOperationError)
    async def create_statement(self, statement: Statement) -> str:
        """
        创建Statement节点
        
        Args:
            statement: Statement实例
            
        Returns:
            str: 创建的Statement ID
            
        Raises:
            NodeOperationError: 节点创建失败时
            ValidationError: 数据验证失败时
        """
        start_time = time.time()
        
        try:
            # 验证Statement数据
            self._validate_statement(statement)
            
            # 如果没有提供重要性分数，自动计算
            if statement.importance_score is None:
                statement.importance_score = self._calculate_importance_score(statement)
            
            logger.debug(f"创建Statement节点: {statement.id}")
            
            async with self.neo4j_driver.session(database=self.database) as session:
                properties = {
                    "id": statement.id,
                    "content": statement.content,
                    "subject": statement.subject,
                    "predicate": statement.predicate,
                    "object": statement.object,
                    "embedding": self._serialize_for_neo4j(statement.embedding),
                    "confidence": statement.confidence or 0.5,
                    "source_episode": statement.source_episode,
                    "importance_score": statement.importance_score,
                    "verification_status": getattr(statement, 'verification_status', 'unverified'),
                    "created_at": statement.created_at.isoformat(),
                    "updated_at": statement.updated_at.isoformat() if statement.updated_at else None
                }
                
                create_query = """
                CREATE (s:Statement $properties)
                RETURN s.id AS statement_id
                """
                
                result = await session.run(create_query, properties=properties)
                record = await result.single()
                
                if record:
                    created_id = record["statement_id"]
                    
                    # 更新缓存
                    self._update_cache(created_id, properties)
                    
                    # 如果有Episode关联，清除Episode缓存
                    if statement.source_episode:
                        episode_cache_key = self._get_episode_cache_key(statement.source_episode)
                        if episode_cache_key in self._episode_statements_cache:
                            del self._episode_statements_cache[episode_cache_key]
                    
                    # 更新指标
                    self._operation_metrics["create_operations"] += 1
                    self.update_metrics(True, time.time() - start_time)
                    
                    logger.info(f"✅ Statement节点创建成功: {created_id}")
                    return created_id
                else:
                    raise NodeOperationError("Statement节点创建失败：未返回结果")
        
        except ValidationError:
            # 验证错误直接抛出
            self.update_metrics(False, time.time() - start_time)
            raise
        except Exception as e:
            self.update_metrics(False, time.time() - start_time)
            error_msg = f"Statement节点创建失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    @handle_exceptions(NodeOperationError)
    async def get_statement(self, statement_id: str) -> Optional[Dict[str, Any]]:
        """
        根据ID获取Statement节点
        
        Args:
            statement_id: Statement ID
            
        Returns:
            Optional[Dict[str, Any]]: Statement节点数据，不存在则返回None
            
        Raises:
            NodeOperationError: 查询失败时
        """
        start_time = time.time()
        
        try:
            if not statement_id:
                raise ValidationError("Statement ID不能为空", field="statement_id")
            
            # 先检查缓存
            cached_data = self._get_from_cache(statement_id)
            if cached_data:
                self.update_metrics(True, time.time() - start_time)
                return cached_data
            
            logger.debug(f"从数据库获取Statement节点: {statement_id}")
            
            async with self.neo4j_driver.session(database=self.database) as session:
                query = """
                MATCH (s:Statement {id: $statement_id})
                RETURN s
                """
                
                result = await session.run(query, statement_id=statement_id)
                record = await result.single()
                
                if record:
                    statement_data = dict(record["s"])
                    
                    # 反序列化复杂字段
                    if 'embedding' in statement_data:
                        statement_data['embedding'] = self._deserialize_from_neo4j(statement_data['embedding'])
                    
                    # 更新缓存
                    self._update_cache(statement_id, statement_data)
                    
                    # 更新指标
                    self._operation_metrics["read_operations"] += 1
                    self.update_metrics(True, time.time() - start_time)
                    
                    logger.debug(f"Statement节点获取成功: {statement_id}")
                    return statement_data
                else:
                    self.update_metrics(True, time.time() - start_time)
                    logger.debug(f"Statement节点不存在: {statement_id}")
                    return None
        
        except ValidationError:
            self.update_metrics(False, time.time() - start_time)
            raise
        except Exception as e:
            self.update_metrics(False, time.time() - start_time)
            error_msg = f"获取Statement节点失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    @handle_exceptions(NodeOperationError)
    async def update_statement(self, statement_id: str, updates: Dict[str, Any]) -> bool:
        """
        更新Statement节点
        
        Args:
            statement_id: Statement ID
            updates: 更新的字段字典
            
        Returns:
            bool: 更新是否成功
            
        Raises:
            NodeOperationError: 更新失败时
            ValidationError: 数据验证失败时
        """
        start_time = time.time()
        
        try:
            if not statement_id:
                raise ValidationError("Statement ID不能为空", field="statement_id")
            
            if not updates:
                raise ValidationError("更新数据不能为空", field="updates")
            
            # 验证更新字段
            allowed_fields = {
                'content', 'subject', 'predicate', 'object', 'embedding',
                'confidence', 'importance_score', 'verification_status'
            }
            
            for field in updates.keys():
                if field not in allowed_fields:
                    raise ValidationError(f"不允许更新字段: {field}", field=field)
            
            logger.debug(f"更新Statement节点: {statement_id}")
            
            # 添加更新时间
            updates = updates.copy()  # 避免修改原始数据
            updates["updated_at"] = datetime.now(timezone.utc).isoformat()
            
            # 序列化复杂字段
            if 'embedding' in updates:
                updates['embedding'] = self._serialize_for_neo4j(updates['embedding'])
            
            # 验证特定字段
            if 'confidence' in updates and (updates['confidence'] < 0 or updates['confidence'] > 1):
                raise ValidationError("置信度必须在0-1之间", field="confidence", 
                                    value=updates['confidence'])
            
            if 'importance_score' in updates and (updates['importance_score'] < 0 or updates['importance_score'] > 1):
                raise ValidationError("重要性分数必须在0-1之间", field="importance_score", 
                                    value=updates['importance_score'])
            
            async with self.neo4j_driver.session(database=self.database) as session:
                # 构建动态更新查询
                set_clauses = []
                for key in updates.keys():
                    set_clauses.append(f"s.{key} = ${key}")
                
                set_clause = ", ".join(set_clauses)
                
                query = f"""
                MATCH (s:Statement {{id: $statement_id}})
                SET {set_clause}
                RETURN s.id AS updated_id, s.source_episode AS source_episode
                """
                
                params = {"statement_id": statement_id, **updates}
                result = await session.run(query, **params)
                record = await result.single()
                
                if record:
                    # 清除相关缓存
                    cache_key = self._get_cache_key(statement_id)
                    if cache_key in self._statement_cache:
                        del self._statement_cache[cache_key]
                    
                    # 清除Episode缓存
                    source_episode = record.get("source_episode")
                    if source_episode:
                        episode_cache_key = self._get_episode_cache_key(source_episode)
                        if episode_cache_key in self._episode_statements_cache:
                            del self._episode_statements_cache[episode_cache_key]
                    
                    # 更新指标
                    self._operation_metrics["update_operations"] += 1
                    self.update_metrics(True, time.time() - start_time)
                    
                    logger.info(f"✅ Statement节点更新成功: {statement_id}")
                    return True
                else:
                    self.update_metrics(False, time.time() - start_time)
                    logger.warning(f"Statement节点不存在，无法更新: {statement_id}")
                    return False
        
        except ValidationError:
            self.update_metrics(False, time.time() - start_time)
            raise
        except Exception as e:
            self.update_metrics(False, time.time() - start_time)
            error_msg = f"更新Statement节点失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    @handle_exceptions(NodeOperationError)
    async def delete_statement(self, statement_id: str, cascade: bool = False) -> bool:
        """
        删除Statement节点
        
        Args:
            statement_id: Statement ID
            cascade: 是否级联删除相关关系
            
        Returns:
            bool: 删除是否成功
            
        Raises:
            NodeOperationError: 删除失败时
        """
        start_time = time.time()
        
        try:
            if not statement_id:
                raise ValidationError("Statement ID不能为空", field="statement_id")
            
            logger.debug(f"删除Statement节点: {statement_id}, 级联删除: {cascade}")
            
            async with self.neo4j_driver.session(database=self.database) as session:
                # 先获取source_episode信息以便清除缓存
                info_query = """
                MATCH (s:Statement {id: $statement_id})
                RETURN s.source_episode AS source_episode
                """
                
                info_result = await session.run(info_query, statement_id=statement_id)
                info_record = await info_result.single()
                source_episode = info_record["source_episode"] if info_record else None
                
                if cascade:
                    # 级联删除Statement及其相关关系
                    query = """
                    MATCH (s:Statement {id: $statement_id})
                    OPTIONAL MATCH (s)-[r]-()
                    DELETE r, s
                    RETURN count(s) AS deleted_count
                    """
                else:
                    # 只删除Statement节点
                    query = """
                    MATCH (s:Statement {id: $statement_id})
                    DELETE s
                    RETURN count(s) AS deleted_count
                    """
                
                result = await session.run(query, statement_id=statement_id)
                record = await result.single()
                
                deleted_count = record["deleted_count"] if record else 0
                
                if deleted_count > 0:
                    # 清除缓存
                    cache_key = self._get_cache_key(statement_id)
                    if cache_key in self._statement_cache:
                        del self._statement_cache[cache_key]
                    
                    # 清除Episode缓存
                    if source_episode:
                        episode_cache_key = self._get_episode_cache_key(source_episode)
                        if episode_cache_key in self._episode_statements_cache:
                            del self._episode_statements_cache[episode_cache_key]
                    
                    # 更新指标
                    self._operation_metrics["delete_operations"] += 1
                    self.update_metrics(True, time.time() - start_time)
                    
                    logger.info(f"✅ Statement节点删除成功: {statement_id}")
                    return True
                else:
                    self.update_metrics(False, time.time() - start_time)
                    logger.warning(f"Statement节点不存在，无法删除: {statement_id}")
                    return False
        
        except Exception as e:
            self.update_metrics(False, time.time() - start_time)
            error_msg = f"删除Statement节点失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    @handle_exceptions(NodeOperationError)
    async def get_statements_by_episode(self, 
                                      episode_id: str, 
                                      sort_by: str = "importance_score",
                                      sort_order: str = "DESC",
                                      limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取指定Episode的所有Statement
        
        Args:
            episode_id: Episode ID
            sort_by: 排序字段
            sort_order: 排序顺序 (ASC/DESC)
            limit: 结果数量限制
            
        Returns:
            List[Dict[str, Any]]: Statement列表
            
        Raises:
            NodeOperationError: 查询失败时
        """
        start_time = time.time()
        
        try:
            if not episode_id:
                raise ValidationError("Episode ID不能为空", field="episode_id")
            
            # 验证排序参数
            allowed_sort_fields = {'importance_score', 'confidence', 'created_at', 'updated_at', 'content'}
            if sort_by not in allowed_sort_fields:
                raise ValidationError(f"不支持的排序字段: {sort_by}", field="sort_by", value=sort_by)
            
            if sort_order.upper() not in ['ASC', 'DESC']:
                raise ValidationError("sort_order必须为ASC或DESC", field="sort_order", value=sort_order)
            
            # 先检查缓存（仅在默认排序和无限制时）
            if sort_by == "importance_score" and sort_order.upper() == "DESC" and limit is None:
                cached_data = self._get_from_episode_cache(episode_id)
                if cached_data:
                    self.update_metrics(True, time.time() - start_time)
                    return cached_data
            
            logger.debug(f"获取Episode的Statement: {episode_id}, 排序: {sort_by} {sort_order}")
            
            async with self.neo4j_driver.session(database=self.database) as session:
                # 构建查询
                base_query = """
                MATCH (s:Statement {source_episode: $episode_id})
                RETURN s
                ORDER BY s.{sort_field} {sort_order}
                """.format(sort_field=sort_by, sort_order=sort_order.upper())
                
                if limit:
                    base_query += f" LIMIT {limit}"
                
                result = await session.run(base_query, episode_id=episode_id)
                records = await result.data()
                
                statements = []
                for record in records:
                    statement_data = dict(record["s"])
                    
                    # 反序列化复杂字段
                    if 'embedding' in statement_data:
                        statement_data['embedding'] = self._deserialize_from_neo4j(statement_data['embedding'])
                    
                    statements.append(statement_data)
                
                # 如果是默认排序且无限制，更新Episode缓存
                if sort_by == "importance_score" and sort_order.upper() == "DESC" and limit is None:
                    self._update_episode_cache(episode_id, statements)
                
                # 更新指标
                self._operation_metrics["episode_query_operations"] += 1
                self.update_metrics(True, time.time() - start_time)
                
                logger.debug(f"获取到Episode {episode_id} 的 {len(statements)} 个Statement")
                
                return statements
        
        except ValidationError:
            self.update_metrics(False, time.time() - start_time)
            raise
        except Exception as e:
            self.update_metrics(False, time.time() - start_time)
            error_msg = f"获取Episode的Statement失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    @handle_exceptions(NodeOperationError)
    async def search_statements_by_content(self, 
                                         query_text: str, 
                                         fuzzy: bool = True,
                                         limit: int = 50,
                                         min_confidence: Optional[float] = None,
                                         min_importance: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        根据内容搜索Statement
        
        Args:
            query_text: 查询文本
            fuzzy: 是否启用模糊匹配
            limit: 返回结果数量
            min_confidence: 最小置信度阈值
            min_importance: 最小重要性分数阈值
            
        Returns:
            List[Dict[str, Any]]: 匹配的Statement列表
        """
        start_time = time.time()
        
        try:
            if not query_text or not query_text.strip():
                raise ValidationError("查询文本不能为空", field="query_text")
            
            if limit <= 0 or limit > 1000:
                raise ValidationError("limit必须在1-1000之间", field="limit", value=limit)
            
            logger.debug(f"搜索Statement内容: {query_text[:50]}...")
            
            async with self.neo4j_driver.session(database=self.database) as session:
                # 构建查询条件
                where_conditions = []
                params = {"query_text": query_text, "limit": limit}
                
                if fuzzy:
                    where_conditions.append("""
                        (toLower(s.content) CONTAINS toLower($query_text)
                         OR toLower(s.subject) CONTAINS toLower($query_text)
                         OR toLower(s.predicate) CONTAINS toLower($query_text)
                         OR toLower(s.object) CONTAINS toLower($query_text))
                    """)
                else:
                    where_conditions.append("""
                        (s.content = $query_text
                         OR s.subject = $query_text
                         OR s.predicate = $query_text
                         OR s.object = $query_text)
                    """)
                
                # 添加置信度过滤
                if min_confidence is not None:
                    where_conditions.append("s.confidence >= $min_confidence")
                    params["min_confidence"] = min_confidence
                
                # 添加重要性过滤
                if min_importance is not None:
                    where_conditions.append("s.importance_score >= $min_importance")
                    params["min_importance"] = min_importance
                
                where_clause = " AND ".join(where_conditions)
                
                query = f"""
                MATCH (s:Statement)
                WHERE {where_clause}
                RETURN s
                ORDER BY s.importance_score DESC, s.confidence DESC, s.created_at DESC
                LIMIT $limit
                """
                
                result = await session.run(query, **params)
                records = await result.data()
                
                statements = []
                for record in records:
                    statement_data = dict(record["s"])
                    
                    # 反序列化复杂字段
                    if 'embedding' in statement_data:
                        statement_data['embedding'] = self._deserialize_from_neo4j(statement_data['embedding'])
                    
                    statements.append(statement_data)
                
                # 更新指标
                self._operation_metrics["read_operations"] += 1
                self.update_metrics(True, time.time() - start_time)
                
                logger.debug(f"搜索到 {len(statements)} 个匹配的Statement")
                
                return statements
        
        except ValidationError:
            self.update_metrics(False, time.time() - start_time)
            raise
        except Exception as e:
            self.update_metrics(False, time.time() - start_time)
            error_msg = f"搜索Statement内容失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    @handle_exceptions(NodeOperationError)
    async def update_verification_status(self, 
                                       statement_id: str, 
                                       verification_status: str,
                                       recalculate_importance: bool = True) -> bool:
        """
        更新Statement验证状态
        
        Args:
            statement_id: Statement ID
            verification_status: 验证状态 (verified, unverified, contradicted, pending)
            recalculate_importance: 是否重新计算重要性分数
            
        Returns:
            bool: 更新是否成功
        """
        try:
            valid_statuses = {'verified', 'unverified', 'contradicted', 'pending'}
            if verification_status not in valid_statuses:
                raise ValidationError(f"无效的验证状态: {verification_status}", 
                                    field="verification_status", value=verification_status)
            
            updates = {"verification_status": verification_status}
            
            if recalculate_importance:
                # 获取当前Statement数据
                current_statement = await self.get_statement(statement_id)
                if current_statement:
                    # 创建临时Statement对象进行重要性分数计算
                    temp_statement = Statement(
                        id=current_statement["id"],
                        content=current_statement.get("content", ""),
                        subject=current_statement.get("subject"),
                        predicate=current_statement.get("predicate"),
                        object=current_statement.get("object"),
                        confidence=current_statement.get("confidence")
                    )
                    # 设置新的验证状态
                    temp_statement.verification_status = verification_status
                    
                    # 重新计算重要性分数
                    new_importance = self._calculate_importance_score(temp_statement)
                    updates["importance_score"] = new_importance
            
            return await self.update_statement(statement_id, updates)
        
        except Exception as e:
            error_msg = f"更新Statement验证状态失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    async def get_statement_statistics(self) -> Dict[str, Any]:
        """
        获取Statement统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            async with self.neo4j_driver.session(database=self.database) as session:
                # 基础统计
                basic_stats_query = """
                MATCH (s:Statement)
                RETURN 
                    count(s) AS total_statements,
                    avg(s.confidence) AS avg_confidence,
                    avg(s.importance_score) AS avg_importance_score,
                    count(DISTINCT s.source_episode) AS unique_episodes,
                    count(DISTINCT s.verification_status) AS unique_verification_statuses
                """
                
                result = await session.run(basic_stats_query)
                basic_stats = await result.single()
                
                # 验证状态分布统计
                verification_stats_query = """
                MATCH (s:Statement)
                RETURN s.verification_status AS status, 
                       count(s) AS count,
                       avg(s.confidence) AS avg_confidence,
                       avg(s.importance_score) AS avg_importance
                ORDER BY count DESC
                """
                
                verification_result = await session.run(verification_stats_query)
                verification_records = await verification_result.data()
                
                # Episode统计
                episode_stats_query = """
                MATCH (s:Statement)
                WHERE s.source_episode IS NOT NULL
                WITH s.source_episode AS episode, count(s) AS statement_count
                RETURN 
                    avg(statement_count) AS avg_statements_per_episode,
                    max(statement_count) AS max_statements_per_episode,
                    min(statement_count) AS min_statements_per_episode
                """
                
                episode_result = await session.run(episode_stats_query)
                episode_stats = await episode_result.single()
                
                # 重要性分数分布
                importance_stats_query = """
                MATCH (s:Statement)
                WHERE s.importance_score IS NOT NULL
                WITH 
                    CASE 
                        WHEN s.importance_score >= 0.8 THEN 'high'
                        WHEN s.importance_score >= 0.5 THEN 'medium'
                        ELSE 'low'
                    END AS importance_level,
                    count(s) AS count
                RETURN importance_level, count
                """
                
                importance_result = await session.run(importance_stats_query)
                importance_records = await importance_result.data()
                
                return {
                    "total_statements": basic_stats["total_statements"] if basic_stats else 0,
                    "avg_confidence": round(basic_stats["avg_confidence"] or 0, 3),
                    "avg_importance_score": round(basic_stats["avg_importance_score"] or 0, 3),
                    "unique_episodes": basic_stats["unique_episodes"] if basic_stats else 0,
                    "unique_verification_statuses": basic_stats["unique_verification_statuses"] if basic_stats else 0,
                    "verification_status_distribution": {
                        record["status"]: {
                            "count": record["count"],
                            "avg_confidence": round(record["avg_confidence"] or 0, 3),
                            "avg_importance": round(record["avg_importance"] or 0, 3)
                        } for record in verification_records
                    },
                    "episode_statistics": {
                        "avg_statements_per_episode": round(episode_stats["avg_statements_per_episode"] or 0, 2),
                        "max_statements_per_episode": episode_stats["max_statements_per_episode"] or 0,
                        "min_statements_per_episode": episode_stats["min_statements_per_episode"] or 0
                    } if episode_stats else {},
                    "importance_distribution": {
                        record["importance_level"]: record["count"] 
                        for record in importance_records
                    },
                    "cache_stats": {
                        "statement_cache_size": len(self._statement_cache),
                        "episode_cache_size": len(self._episode_statements_cache),
                        "cache_hits": self._operation_metrics["cache_hits"],
                        "cache_misses": self._operation_metrics["cache_misses"],
                        "hit_rate": round(
                            self._operation_metrics["cache_hits"] / 
                            max(1, self._operation_metrics["cache_hits"] + self._operation_metrics["cache_misses"]) * 100, 2
                        )
                    },
                    "operation_metrics": self._operation_metrics.copy(),
                    "importance_calculation_config": {
                        "weights": self._importance_weights,
                        "verification_status_scores": self._verification_status_scores
                    },
                    "generated_at": datetime.now(timezone.utc).isoformat()
                }
        
        except Exception as e:
            error_msg = f"获取Statement统计信息失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)


# 全局服务实例管理
_statement_service_instance: Optional[StatementNodeService] = None

def get_statement_service(neo4j_driver, database: str = "neo4j") -> StatementNodeService:
    """获取Statement节点服务实例"""
    global _statement_service_instance
    if _statement_service_instance is None:
        _statement_service_instance = StatementNodeService(neo4j_driver, database)
    return _statement_service_instance


# 便捷函数接口
async def create_statement_node(neo4j_driver, statement: Statement) -> str:
    """便捷函数：创建Statement节点"""
    service = get_statement_service(neo4j_driver)
    async with service.service_context():
        return await service.create_statement(statement)


async def get_statement_node(neo4j_driver, statement_id: str) -> Optional[Dict[str, Any]]:
    """便捷函数：获取Statement节点"""
    service = get_statement_service(neo4j_driver)
    async with service.service_context():
        return await service.get_statement(statement_id)


async def get_episode_statements(neo4j_driver, episode_id: str) -> List[Dict[str, Any]]:
    """便捷函数：获取Episode的所有Statement"""
    service = get_statement_service(neo4j_driver)
    async with service.service_context():
        return await service.get_statements_by_episode(episode_id)


# 模块测试代码
if __name__ == "__main__":
    """Statement节点服务模块测试"""
    
    async def test_statement_service():
        """测试Statement节点服务功能"""
        print("🚀 开始测试Statement节点服务...")
        
        # 这里需要实际的Neo4j驱动实例进行测试
        # 在实际使用中，从主应用传入驱动实例
        print("⚠️ Statement节点服务模块已创建，需要Neo4j驱动进行完整测试")
    
    # 运行测试
    asyncio.run(test_statement_service())