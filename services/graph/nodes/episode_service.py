"""
智能记忆引擎 - Episode 节点服务

负责Episode节点的CRUD操作和业务逻辑：
- Episode节点创建、查询、更新、删除
- Episode列表查询和分页
- Episode相关统计和状态管理
- 支持完整的生命周期管理

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 16:30:00
"""

import asyncio
import logging
import json
import time
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone

from neo4j import AsyncSession
from services.core.base_service import BaseService, ServiceHealthCheck, ServiceStatus
from services.utils.exceptions import (
    NodeOperationError, DatabaseException, ValidationError,
    handle_exceptions
)
from models import Episode, ContentSource, ProcessingStatus

logger = logging.getLogger("smart_memory.episode_service")


class EpisodeNodeService(BaseService):
    """
    Episode节点服务
    
    负责Episode节点的完整生命周期管理：
    - CRUD操作 (创建、读取、更新、删除)
    - 批量操作和事务管理
    - 数据验证和完整性检查
    - 性能优化和缓存
    """
    
    def __init__(self, neo4j_driver, database: str = "neo4j"):
        """
        初始化Episode节点服务
        
        Args:
            neo4j_driver: Neo4j异步驱动实例
            database: 数据库名称
        """
        super().__init__(
            service_name="episode_node_service",
            config={"database": database}
        )
        
        self.neo4j_driver = neo4j_driver
        self.database = database
        
        # 缓存配置
        self._episode_cache = {}
        self._cache_ttl = 300  # 5分钟缓存
        self._cache_max_size = 1000
        
        # 性能指标
        self._operation_metrics = {
            "create_operations": 0,
            "read_operations": 0,
            "update_operations": 0,
            "delete_operations": 0,
            "cache_hits": 0,
            "cache_misses": 0
        }
        
        logger.info(f"Episode节点服务初始化完成 - 数据库: {database}")
    
    async def _initialize_service(self) -> None:
        """初始化服务的具体实现"""
        try:
            # 验证Neo4j驱动连接
            if not self.neo4j_driver:
                raise DatabaseException("Neo4j驱动未提供")
            
            # 测试数据库连接
            async with self.neo4j_driver.session(database=self.database) as session:
                result = await session.run("RETURN 1 AS test")
                await result.single()
            
            # 确保Episode节点索引存在
            await self._ensure_indexes()
            
            logger.info("Episode节点服务初始化成功")
            
        except Exception as e:
            logger.error(f"Episode节点服务初始化失败: {e}")
            raise DatabaseException(f"Episode节点服务初始化失败: {e}")
    
    async def _cleanup_service(self) -> None:
        """清理服务的具体实现"""
        try:
            # 清理缓存
            self._episode_cache.clear()
            
            # 记录服务统计信息
            logger.info(f"Episode服务统计 - 操作指标: {self._operation_metrics}")
            
            logger.info("Episode节点服务清理完成")
            
        except Exception as e:
            logger.error(f"Episode节点服务清理失败: {e}")
            raise
    
    async def _perform_health_check(self) -> ServiceHealthCheck:
        """执行健康检查的具体实现"""
        try:
            start_time = time.time()
            
            # 检查数据库连接
            async with self.neo4j_driver.session(database=self.database) as session:
                result = await session.run("MATCH (e:Episode) RETURN count(e) AS episode_count LIMIT 1")
                record = await result.single()
                episode_count = record["episode_count"] if record else 0
            
            response_time = time.time() - start_time
            
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=ServiceStatus.READY,
                message=f"Episode服务运行正常，共有 {episode_count} 个Episode节点",
                details={
                    "episode_count": episode_count,
                    "cache_size": len(self._episode_cache),
                    "operation_metrics": self._operation_metrics.copy()
                },
                response_time=response_time
            )
            
        except Exception as e:
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=ServiceStatus.ERROR,
                message=f"Episode服务健康检查失败: {str(e)}",
                details={"error": str(e)}
            )
    
    async def _ensure_indexes(self) -> None:
        """确保必要的索引存在"""
        try:
            async with self.neo4j_driver.session(database=self.database) as session:
                # Episode节点的必要索引
                indexes = [
                    ("Episode_id_index", "Episode", "id"),
                    ("Episode_created_at_index", "Episode", "created_at"),
                    ("Episode_processing_status_index", "Episode", "processing_status"),
                    ("Episode_source_index", "Episode", "source")
                ]
                
                for index_name, label, property_name in indexes:
                    try:
                        # 检查索引是否存在
                        check_query = "SHOW INDEXES WHERE name = $index_name"
                        result = await session.run(check_query, index_name=index_name)
                        existing = await result.data()
                        
                        if not existing:
                            # 创建索引
                            create_query = f"""
                            CREATE INDEX {index_name} IF NOT EXISTS
                            FOR (n:{label}) 
                            ON (n.{property_name})
                            """
                            await session.run(create_query)
                            logger.debug(f"创建索引: {index_name}")
                        
                    except Exception as index_error:
                        # 索引创建失败不应影响服务启动
                        logger.warning(f"索引 {index_name} 创建失败: {index_error}")
            
        except Exception as e:
            logger.error(f"确保索引失败: {e}")
            raise
    
    def _serialize_for_neo4j(self, value: Any) -> Any:
        """将复杂数据类型序列化为Neo4j可接受的格式"""
        if value is None:
            return None
        elif isinstance(value, (str, int, float, bool)):
            return value
        elif isinstance(value, list):
            return [self._serialize_for_neo4j(item) for item in value]
        elif isinstance(value, dict):
            return json.dumps(value, ensure_ascii=False)
        else:
            return str(value)
    
    def _deserialize_from_neo4j(self, value: Any) -> Any:
        """将Neo4j中的数据反序列化为Python对象"""
        if isinstance(value, str):
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
        return value
    
    def _get_cache_key(self, episode_id: str) -> str:
        """生成缓存键"""
        return f"episode:{episode_id}"
    
    def _is_cache_valid(self, cached_time: float) -> bool:
        """检查缓存是否有效"""
        return time.time() - cached_time < self._cache_ttl
    
    def _update_cache(self, episode_id: str, episode_data: Dict[str, Any]) -> None:
        """更新缓存"""
        if len(self._episode_cache) >= self._cache_max_size:
            # 清理最旧的缓存项
            oldest_key = min(self._episode_cache.keys(), 
                           key=lambda k: self._episode_cache[k]['timestamp'])
            del self._episode_cache[oldest_key]
        
        cache_key = self._get_cache_key(episode_id)
        self._episode_cache[cache_key] = {
            'data': episode_data,
            'timestamp': time.time()
        }
    
    def _get_from_cache(self, episode_id: str) -> Optional[Dict[str, Any]]:
        """从缓存获取数据"""
        cache_key = self._get_cache_key(episode_id)
        if cache_key in self._episode_cache:
            cached_item = self._episode_cache[cache_key]
            if self._is_cache_valid(cached_item['timestamp']):
                self._operation_metrics["cache_hits"] += 1
                return cached_item['data']
            else:
                # 缓存过期，删除
                del self._episode_cache[cache_key]
        
        self._operation_metrics["cache_misses"] += 1
        return None
    
    def _validate_episode(self, episode: Episode) -> None:
        """验证Episode数据"""
        if not episode.id:
            raise ValidationError("Episode ID不能为空", field="id")
        
        if not episode.content or not episode.content.strip():
            raise ValidationError("Episode内容不能为空", field="content")
        
        if len(episode.content) > 100000:  # 100KB限制
            raise ValidationError("Episode内容过长", field="content", 
                                value=len(episode.content))
        
        # 兼容性处理：由于use_enum_values=True，source会被序列化为字符串
        # 需要验证字符串值是否为有效的ContentSource值
        if isinstance(episode.source, str):
            if episode.source not in [e.value for e in ContentSource]:
                raise ValidationError(f"Episode来源值无效: {episode.source}，有效值: {[e.value for e in ContentSource]}", 
                                    field="source", value=episode.source)
        elif isinstance(episode.source, ContentSource):
            # 如果是枚举对象，也是有效的
            pass
        else:
            raise ValidationError(f"Episode来源类型无效: 期望ContentSource或字符串，实际类型={type(episode.source)}, 值={episode.source}", 
                                field="source", value=episode.source)
        
        # 同样处理processing_status字段
        if isinstance(episode.processing_status, str):
            if episode.processing_status not in [e.value for e in ProcessingStatus]:
                raise ValidationError(f"Episode处理状态值无效: {episode.processing_status}，有效值: {[e.value for e in ProcessingStatus]}", 
                                    field="processing_status", value=episode.processing_status)
        elif isinstance(episode.processing_status, ProcessingStatus):
            # 如果是枚举对象，也是有效的
            pass
        else:
            raise ValidationError("Episode处理状态类型无效", field="processing_status", 
                                value=episode.processing_status)
    
    @handle_exceptions(NodeOperationError)
    async def create_episode(self, episode: Episode) -> str:
        """
        创建Episode节点
        
        Args:
            episode: Episode实例
            
        Returns:
            str: 创建的Episode ID
            
        Raises:
            NodeOperationError: 节点创建失败时
            ValidationError: 数据验证失败时
        """
        start_time = time.time()
        
        try:
            # 验证Episode数据
            self._validate_episode(episode)
            
            logger.debug(f"创建Episode节点: {episode.id}")
            
            async with self.neo4j_driver.session(database=self.database) as session:
                # 构建Episode节点属性
                properties = {
                    "id": episode.id,
                    "content": episode.content,
                    "title": episode.title,
                    "summary": episode.summary,
                    "source": episode.source.value if hasattr(episode.source, 'value') else str(episode.source),
                    "session_id": episode.session_id,
                    "tags": self._serialize_for_neo4j(episode.tags),
                    "metadata": self._serialize_for_neo4j(episode.metadata),
                    "embedding": self._serialize_for_neo4j(episode.embedding),
                    "entities_count": episode.entities_count,
                    "statements_count": episode.statements_count,
                    "processing_status": episode.processing_status.value if hasattr(episode.processing_status, 'value') else str(episode.processing_status),
                    "processing_time_ms": episode.processing_time_ms,
                    "quality_score": episode.quality_score,
                    "created_at": episode.created_at.isoformat(),
                    "updated_at": episode.updated_at.isoformat() if episode.updated_at else None
                }
                
                # 创建Episode节点的Cypher查询
                create_query = """
                CREATE (e:Episode $properties)
                RETURN e.id AS episode_id
                """
                
                result = await session.run(create_query, properties=properties)
                record = await result.single()
                
                if record:
                    created_id = record["episode_id"]
                    
                    # 更新缓存
                    episode_data = properties.copy()
                    self._update_cache(created_id, episode_data)
                    
                    # 更新指标
                    self._operation_metrics["create_operations"] += 1
                    processing_time = time.time() - start_time
                    self.update_metrics(True, processing_time)
                    
                    logger.info(f"✅ Episode节点创建成功: {created_id}")
                    return created_id
                else:
                    raise NodeOperationError("Episode节点创建失败：未返回结果")
        
        except ValidationError:
            # 验证错误直接抛出
            self.update_metrics(False, time.time() - start_time)
            raise
        except Exception as e:
            self.update_metrics(False, time.time() - start_time)
            error_msg = f"Episode节点创建失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    @handle_exceptions(NodeOperationError)
    async def get_episode(self, episode_id: str) -> Optional[Dict[str, Any]]:
        """
        根据ID获取Episode节点
        
        Args:
            episode_id: Episode ID
            
        Returns:
            Optional[Dict[str, Any]]: Episode节点数据，不存在则返回None
            
        Raises:
            NodeOperationError: 查询失败时
        """
        start_time = time.time()
        
        try:
            if not episode_id:
                raise ValidationError("Episode ID不能为空", field="episode_id")
            
            # 先检查缓存
            cached_data = self._get_from_cache(episode_id)
            if cached_data:
                self.update_metrics(True, time.time() - start_time)
                return cached_data
            
            logger.debug(f"从数据库获取Episode节点: {episode_id}")
            
            async with self.neo4j_driver.session(database=self.database) as session:
                query = """
                MATCH (e:Episode {id: $episode_id})
                RETURN e
                """
                
                result = await session.run(query, episode_id=episode_id)
                record = await result.single()
                
                if record:
                    episode_data = dict(record["e"])
                    
                    # 反序列化复杂字段
                    for field in ['tags', 'metadata', 'embedding']:
                        if field in episode_data:
                            episode_data[field] = self._deserialize_from_neo4j(episode_data[field])
                    
                    # 更新缓存
                    self._update_cache(episode_id, episode_data)
                    
                    # 更新指标
                    self._operation_metrics["read_operations"] += 1
                    self.update_metrics(True, time.time() - start_time)
                    
                    logger.debug(f"Episode节点获取成功: {episode_id}")
                    return episode_data
                else:
                    self.update_metrics(True, time.time() - start_time)
                    logger.debug(f"Episode节点不存在: {episode_id}")
                    return None
        
        except ValidationError:
            self.update_metrics(False, time.time() - start_time)
            raise
        except Exception as e:
            self.update_metrics(False, time.time() - start_time)
            error_msg = f"获取Episode节点失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    @handle_exceptions(NodeOperationError)
    async def update_episode(self, episode_id: str, updates: Dict[str, Any]) -> bool:
        """
        更新Episode节点
        
        Args:
            episode_id: Episode ID
            updates: 更新的字段字典
            
        Returns:
            bool: 更新是否成功
            
        Raises:
            NodeOperationError: 更新失败时
            ValidationError: 数据验证失败时
        """
        start_time = time.time()
        
        try:
            if not episode_id:
                raise ValidationError("Episode ID不能为空", field="episode_id")
            
            if not updates:
                raise ValidationError("更新数据不能为空", field="updates")
            
            # 验证更新字段
            allowed_fields = {
                'content', 'title', 'summary', 'tags', 'metadata', 
                'processing_status', 'processing_time_ms', 'quality_score',
                'entities_count', 'statements_count'
            }
            
            for field in updates.keys():
                if field not in allowed_fields:
                    raise ValidationError(f"不允许更新字段: {field}", field=field)
            
            logger.debug(f"更新Episode节点: {episode_id}")
            
            # 添加更新时间
            updates = updates.copy()  # 避免修改原始数据
            updates["updated_at"] = datetime.now(timezone.utc).isoformat()
            
            # 序列化复杂字段
            for field in ['tags', 'metadata']:
                if field in updates:
                    updates[field] = self._serialize_for_neo4j(updates[field])
            
            async with self.neo4j_driver.session(database=self.database) as session:
                # 构建动态更新查询
                set_clauses = []
                for key in updates.keys():
                    set_clauses.append(f"e.{key} = ${key}")
                
                set_clause = ", ".join(set_clauses)
                
                query = f"""
                MATCH (e:Episode {{id: $episode_id}})
                SET {set_clause}
                RETURN e.id AS updated_id
                """
                
                params = {"episode_id": episode_id, **updates}
                result = await session.run(query, **params)
                record = await result.single()
                
                if record:
                    # 清除缓存
                    cache_key = self._get_cache_key(episode_id)
                    if cache_key in self._episode_cache:
                        del self._episode_cache[cache_key]
                    
                    # 更新指标
                    self._operation_metrics["update_operations"] += 1
                    self.update_metrics(True, time.time() - start_time)
                    
                    logger.info(f"✅ Episode节点更新成功: {episode_id}")
                    return True
                else:
                    self.update_metrics(False, time.time() - start_time)
                    logger.warning(f"Episode节点不存在，无法更新: {episode_id}")
                    return False
        
        except ValidationError:
            self.update_metrics(False, time.time() - start_time)
            raise
        except Exception as e:
            self.update_metrics(False, time.time() - start_time)
            error_msg = f"更新Episode节点失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    @handle_exceptions(NodeOperationError)
    async def delete_episode(self, episode_id: str, cascade: bool = False) -> bool:
        """
        删除Episode节点
        
        Args:
            episode_id: Episode ID
            cascade: 是否级联删除相关节点和关系
            
        Returns:
            bool: 删除是否成功
            
        Raises:
            NodeOperationError: 删除失败时
        """
        start_time = time.time()
        
        try:
            if not episode_id:
                raise ValidationError("Episode ID不能为空", field="episode_id")
            
            logger.debug(f"删除Episode节点: {episode_id}, 级联删除: {cascade}")
            
            async with self.neo4j_driver.session(database=self.database) as session:
                if cascade:
                    # 级联删除Episode及其相关关系
                    query = """
                    MATCH (e:Episode {id: $episode_id})
                    OPTIONAL MATCH (e)-[r]-()
                    DELETE r, e
                    RETURN count(e) AS deleted_count
                    """
                else:
                    # 只删除Episode节点
                    query = """
                    MATCH (e:Episode {id: $episode_id})
                    DELETE e
                    RETURN count(e) AS deleted_count
                    """
                
                result = await session.run(query, episode_id=episode_id)
                record = await result.single()
                
                deleted_count = record["deleted_count"] if record else 0
                
                if deleted_count > 0:
                    # 清除缓存
                    cache_key = self._get_cache_key(episode_id)
                    if cache_key in self._episode_cache:
                        del self._episode_cache[cache_key]
                    
                    # 更新指标
                    self._operation_metrics["delete_operations"] += 1
                    self.update_metrics(True, time.time() - start_time)
                    
                    logger.info(f"✅ Episode节点删除成功: {episode_id}")
                    return True
                else:
                    self.update_metrics(False, time.time() - start_time)
                    logger.warning(f"Episode节点不存在，无法删除: {episode_id}")
                    return False
        
        except Exception as e:
            self.update_metrics(False, time.time() - start_time)
            error_msg = f"删除Episode节点失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    @handle_exceptions(NodeOperationError)
    async def list_episodes(self, 
                          limit: int = 100, 
                          offset: int = 0,
                          filters: Optional[Dict[str, Any]] = None,
                          sort_by: str = "created_at",
                          sort_order: str = "DESC") -> Dict[str, Any]:
        """
        列出Episode节点（支持分页、过滤和排序）
        
        Args:
            limit: 返回数量限制
            offset: 偏移量
            filters: 过滤条件字典
            sort_by: 排序字段
            sort_order: 排序顺序 (ASC/DESC)
            
        Returns:
            Dict[str, Any]: 包含episodes列表和分页信息的字典
            
        Raises:
            NodeOperationError: 查询失败时
        """
        start_time = time.time()
        
        try:
            # 参数验证
            if limit <= 0 or limit > 1000:
                raise ValidationError("limit必须在1-1000之间", field="limit", value=limit)
            
            if offset < 0:
                raise ValidationError("offset不能为负数", field="offset", value=offset)
            
            allowed_sort_fields = {'created_at', 'updated_at', 'title', 'quality_score', 'processing_time_ms'}
            if sort_by not in allowed_sort_fields:
                raise ValidationError(f"不支持的排序字段: {sort_by}", field="sort_by", value=sort_by)
            
            if sort_order.upper() not in ['ASC', 'DESC']:
                raise ValidationError("sort_order必须为ASC或DESC", field="sort_order", value=sort_order)
            
            logger.debug(f"列出Episode节点 - limit: {limit}, offset: {offset}, sort_by: {sort_by}")
            
            async with self.neo4j_driver.session(database=self.database) as session:
                # 构建基础查询
                where_clauses = []
                params = {"limit": limit, "offset": offset}
                
                if filters:
                    for key, value in filters.items():
                        if key in ['source', 'processing_status', 'session_id']:
                            where_clauses.append(f"e.{key} = ${key}")
                            params[key] = value
                        elif key == 'quality_score_min':
                            where_clauses.append(f"e.quality_score >= ${key}")
                            params[key] = value
                        elif key == 'quality_score_max':
                            where_clauses.append(f"e.quality_score <= ${key}")
                            params[key] = value
                        elif key == 'content_contains':
                            where_clauses.append(f"toLower(e.content) CONTAINS toLower(${key})")
                            params[key] = value
                        elif key == 'title_contains':
                            where_clauses.append(f"toLower(e.title) CONTAINS toLower(${key})")
                            params[key] = value
                
                where_clause = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""
                
                # 数据查询
                data_query = f"""
                MATCH (e:Episode)
                {where_clause}
                RETURN e
                ORDER BY e.{sort_by} {sort_order.upper()}
                SKIP $offset
                LIMIT $limit
                """
                
                # 总数查询
                count_query = f"""
                MATCH (e:Episode)
                {where_clause}
                RETURN count(e) AS total_count
                """
                
                # 执行查询
                result = await session.run(data_query, **params)
                records = await result.data()
                
                count_result = await session.run(count_query, **params)
                count_record = await count_result.single()
                total_count = count_record["total_count"] if count_record else 0
                
                episodes = []
                for record in records:
                    episode_data = dict(record["e"])
                    
                    # 反序列化复杂字段
                    for field in ['tags', 'metadata', 'embedding']:
                        if field in episode_data:
                            episode_data[field] = self._deserialize_from_neo4j(episode_data[field])
                    
                    episodes.append(episode_data)
                
                # 更新指标
                self._operation_metrics["read_operations"] += 1
                self.update_metrics(True, time.time() - start_time)
                
                result_data = {
                    "episodes": episodes,
                    "pagination": {
                        "total_count": total_count,
                        "current_page": offset // limit + 1,
                        "page_size": limit,
                        "total_pages": (total_count + limit - 1) // limit,
                        "has_next": offset + limit < total_count,
                        "has_prev": offset > 0
                    },
                    "filters": filters or {},
                    "sort": {
                        "field": sort_by,
                        "order": sort_order.upper()
                    }
                }
                
                logger.debug(f"获取到 {len(episodes)} 个Episode节点，总计 {total_count} 个")
                
                return result_data
        
        except ValidationError:
            self.update_metrics(False, time.time() - start_time)
            raise
        except Exception as e:
            self.update_metrics(False, time.time() - start_time)
            error_msg = f"列出Episode节点失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    @handle_exceptions(NodeOperationError)
    async def get_episodes_by_session(self, session_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        根据会话ID获取Episode列表
        
        Args:
            session_id: 会话ID
            limit: 返回数量限制
            
        Returns:
            List[Dict[str, Any]]: Episode列表
        """
        return await self.list_episodes(
            limit=limit,
            filters={"session_id": session_id},
            sort_by="created_at",
            sort_order="DESC"
        )
    
    @handle_exceptions(NodeOperationError)
    async def get_recent_episodes(self, days: int = 7, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取最近的Episode
        
        Args:
            days: 天数范围
            limit: 返回数量限制
            
        Returns:
            List[Dict[str, Any]]: 最近Episode列表
        """
        start_time = time.time()
        
        try:
            if days <= 0:
                raise ValidationError("days必须大于0", field="days", value=days)
            
            async with self.neo4j_driver.session(database=self.database) as session:
                query = """
                MATCH (e:Episode)
                WHERE datetime(e.created_at) > datetime() - duration({days: $days})
                RETURN e
                ORDER BY e.created_at DESC
                LIMIT $limit
                """
                
                result = await session.run(query, days=days, limit=limit)
                records = await result.data()
                
                episodes = []
                for record in records:
                    episode_data = dict(record["e"])
                    
                    # 反序列化复杂字段
                    for field in ['tags', 'metadata', 'embedding']:
                        if field in episode_data:
                            episode_data[field] = self._deserialize_from_neo4j(episode_data[field])
                    
                    episodes.append(episode_data)
                
                self.update_metrics(True, time.time() - start_time)
                
                logger.debug(f"获取到 {len(episodes)} 个最近Episode")
                return episodes
        
        except ValidationError:
            self.update_metrics(False, time.time() - start_time)
            raise
        except Exception as e:
            self.update_metrics(False, time.time() - start_time)
            error_msg = f"获取最近Episode失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)
    
    @handle_exceptions(NodeOperationError)
    async def update_episode_processing_status(self, episode_id: str, 
                                             status: ProcessingStatus,
                                             processing_time_ms: Optional[int] = None,
                                             quality_score: Optional[float] = None) -> bool:
        """
        更新Episode处理状态
        
        Args:
            episode_id: Episode ID
            status: 处理状态
            processing_time_ms: 处理时间（毫秒）
            quality_score: 质量分数
            
        Returns:
            bool: 更新是否成功
        """
        updates = {
            "processing_status": status.value if hasattr(status, 'value') else str(status)
        }
        
        if processing_time_ms is not None:
            updates["processing_time_ms"] = processing_time_ms
        
        if quality_score is not None:
            updates["quality_score"] = quality_score
        
        return await self.update_episode(episode_id, updates)
    
    async def get_episode_statistics(self) -> Dict[str, Any]:
        """
        获取Episode统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            async with self.neo4j_driver.session(database=self.database) as session:
                # 基础统计
                basic_stats_query = """
                MATCH (e:Episode)
                RETURN 
                    count(e) AS total_episodes,
                    avg(e.quality_score) AS avg_quality_score,
                    avg(e.processing_time_ms) AS avg_processing_time,
                    avg(e.entities_count) AS avg_entities_count,
                    avg(e.statements_count) AS avg_statements_count
                """
                
                result = await session.run(basic_stats_query)
                basic_stats = await result.single()
                
                # 状态分布统计
                status_stats_query = """
                MATCH (e:Episode)
                RETURN e.processing_status AS status, count(e) AS count
                """
                
                status_result = await session.run(status_stats_query)
                status_records = await status_result.data()
                
                # 来源分布统计
                source_stats_query = """
                MATCH (e:Episode)
                RETURN e.source AS source, count(e) AS count
                """
                
                source_result = await session.run(source_stats_query)
                source_records = await source_result.data()
                
                return {
                    "total_episodes": basic_stats["total_episodes"] if basic_stats else 0,
                    "avg_quality_score": round(basic_stats["avg_quality_score"] or 0, 2),
                    "avg_processing_time_ms": round(basic_stats["avg_processing_time"] or 0, 2),
                    "avg_entities_count": round(basic_stats["avg_entities_count"] or 0, 2),
                    "avg_statements_count": round(basic_stats["avg_statements_count"] or 0, 2),
                    "status_distribution": {record["status"]: record["count"] for record in status_records},
                    "source_distribution": {record["source"]: record["count"] for record in source_records},
                    "cache_stats": {
                        "cache_size": len(self._episode_cache),
                        "cache_hits": self._operation_metrics["cache_hits"],
                        "cache_misses": self._operation_metrics["cache_misses"],
                        "hit_rate": round(
                            self._operation_metrics["cache_hits"] / 
                            max(1, self._operation_metrics["cache_hits"] + self._operation_metrics["cache_misses"]) * 100, 2
                        )
                    },
                    "operation_metrics": self._operation_metrics.copy(),
                    "generated_at": datetime.now(timezone.utc).isoformat()
                }
        
        except Exception as e:
            error_msg = f"获取Episode统计信息失败: {e}"
            logger.error(error_msg)
            raise NodeOperationError(error_msg, cause=e)


    @handle_exceptions(NodeOperationError)
    async def link_to_entity(self, episode_id: str, entity_id: str, relationship_type: str = "CONTAINS") -> bool:
        """
        创建Episode与Entity之间的关系
        
        Args:
            episode_id: Episode节点ID
            entity_id: Entity节点ID
            relationship_type: 关系类型，默认为CONTAINS
            
        Returns:
            bool: 关系创建是否成功
        """
        start_time = time.time()
        
        if not self.is_ready:
            await self.initialize()
        
        try:
            async with self.neo4j_driver.session(database=self.database) as session:
                # 创建Episode与Entity的关系
                query = """
                MATCH (e:Episode {id: $episode_id})
                MATCH (entity:Entity {id: $entity_id})
                MERGE (e)-[r:%s]->(entity)
                SET r.created_at = datetime()
                RETURN r
                """ % relationship_type
                
                result = await session.run(query, episode_id=episode_id, entity_id=entity_id)
                record = await result.single()
                
                success = record is not None
                self.logger.debug(f"Episode-Entity关系创建: {episode_id} -> {entity_id} ({relationship_type}): {'成功' if success else '失败'}")
                
                # 更新指标
                self._operation_metrics["relationship_operations"] = self._operation_metrics.get("relationship_operations", 0) + 1
                self.update_metrics(success, time.time() - start_time)
                
                return success
                
        except Exception as e:
            self.logger.error(f"Episode-Entity关系创建失败: {episode_id} -> {entity_id}: {e}")
            self.update_metrics(False, time.time() - start_time)
            raise NodeOperationError(f"Episode-Entity关系创建失败: {e}")

    @handle_exceptions(NodeOperationError) 
    async def link_to_statement(self, episode_id: str, statement_id: str, relationship_type: str = "CONTAINS") -> bool:
        """
        创建Episode与Statement之间的关系
        
        Args:
            episode_id: Episode节点ID
            statement_id: Statement节点ID
            relationship_type: 关系类型，默认为CONTAINS
            
        Returns:
            bool: 关系创建是否成功
        """
        start_time = time.time()
        
        if not self.is_ready:
            await self.initialize()
        
        try:
            async with self.neo4j_driver.session(database=self.database) as session:
                # 创建Episode与Statement的关系
                query = """
                MATCH (e:Episode {id: $episode_id})
                MATCH (s:Statement {id: $statement_id})
                MERGE (e)-[r:%s]->(s)
                SET r.created_at = datetime()
                RETURN r
                """ % relationship_type
                
                result = await session.run(query, episode_id=episode_id, statement_id=statement_id)
                record = await result.single()
                
                success = record is not None
                self.logger.debug(f"Episode-Statement关系创建: {episode_id} -> {statement_id} ({relationship_type}): {'成功' if success else '失败'}")
                
                # 更新指标
                self._operation_metrics["relationship_operations"] = self._operation_metrics.get("relationship_operations", 0) + 1
                self.update_metrics(success, time.time() - start_time)
                
                return success
                
        except Exception as e:
            self.logger.error(f"Episode-Statement关系创建失败: {episode_id} -> {statement_id}: {e}")
            self.update_metrics(False, time.time() - start_time)
            raise NodeOperationError(f"Episode-Statement关系创建失败: {e}")


# 全局服务实例管理
_episode_service_instance: Optional[EpisodeNodeService] = None

def get_episode_service(neo4j_driver, database: str = "neo4j") -> EpisodeNodeService:
    """获取Episode节点服务实例"""
    global _episode_service_instance
    if _episode_service_instance is None:
        _episode_service_instance = EpisodeNodeService(neo4j_driver, database)
    return _episode_service_instance


# 便捷函数接口
async def create_episode_node(neo4j_driver, episode: Episode) -> str:
    """便捷函数：创建Episode节点"""
    service = get_episode_service(neo4j_driver)
    async with service.service_context():
        return await service.create_episode(episode)


async def get_episode_node(neo4j_driver, episode_id: str) -> Optional[Dict[str, Any]]:
    """便捷函数：获取Episode节点"""
    service = get_episode_service(neo4j_driver)
    async with service.service_context():
        return await service.get_episode(episode_id)


# 模块测试代码
if __name__ == "__main__":
    """Episode节点服务模块测试"""
    
    async def test_episode_service():
        """测试Episode节点服务功能"""
        print("🚀 开始测试Episode节点服务...")
        
        # 这里需要实际的Neo4j驱动实例进行测试
        # 在实际使用中，从主应用传入驱动实例
        print("⚠️ Episode节点服务模块已创建，需要Neo4j驱动进行完整测试")
    
    # 运行测试
    asyncio.run(test_episode_service())