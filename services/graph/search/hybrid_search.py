"""
智能记忆引擎 MVP v2.0 - 混合搜索服务

提供向量相似度和文本匹配相结合的混合搜索功能：
- 向量语义搜索 + 关键词文本搜索
- 可配置的权重组合策略
- 智能结果去重和排序
- 高亮片段提取

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 15:33:51
"""

import time
import logging
from typing import List, Dict, Any, Optional, Set

from neo4j import AsyncSession
from services.graph.search.search_base import (
    BaseSearchService,
    SearchParameters,
    SearchResultItem,
    SearchExecutionError,
    SearchParameterError,
    SearchMode,
)
from services.graph.search.vector_search import VectorSearchService
from models import NodeType
from config import settings


# 配置日志记录器
logger = logging.getLogger("smart_memory.hybrid_search")


class HybridSearchService(BaseSearchService):
    """
    混合搜索服务

    结合向量相似度和文本匹配的智能搜索:
    - 语义向量搜索 + 关键词文本搜索
    - 可配置的权重组合策略
    - 多策略结果融合算法
    - 智能去重和相关性排序
    """

    def __init__(self, neo4j_driver=None):
        """初始化混合搜索服务"""
        super().__init__("混合搜索服务")

        self.neo4j_driver = neo4j_driver
        self.database_name = settings.NEO4J_DATABASE

        # 初始化向量搜索服务
        self.vector_search_service = VectorSearchService(neo4j_driver)

        # 混合搜索配置
        self.default_vector_weight = 0.6
        self.default_text_weight = 0.4
        self.min_score_threshold = 0.1
        self.max_results_per_mode = 100

        # 文本搜索优化配置
        self.text_match_boost = 1.2  # 精确匹配加权
        self.keyword_boost = 1.1  # 关键词匹配加权
        self.length_penalty_factor = 0.1  # 长度惩罚因子

        logger.info("混合搜索服务初始化完成")

    async def initialize(self) -> bool:
        """
        初始化混合搜索服务

        Returns:
            bool: 初始化是否成功
        """
        try:
            if self.neo4j_driver is None:
                logger.error("Neo4j驱动未提供，无法初始化混合搜索服务")
                return False

            # 初始化向量搜索服务
            vector_init_success = await self.vector_search_service.initialize()
            if not vector_init_success:
                logger.warning("向量搜索服务初始化失败，将仅支持文本搜索")

            # 验证数据库连接
            async with self.neo4j_driver.session(
                database=self.database_name
            ) as session:
                result = await session.run("RETURN 1 AS test")
                record = await result.single()

                if record and record["test"] == 1:
                    self.is_initialized = True
                    logger.info("✅ 混合搜索服务初始化成功")
                    return True
                else:
                    raise SearchExecutionError("数据库连接测试失败")

        except Exception as e:
            logger.error(f"❌ 混合搜索服务初始化失败: {e}")
            return False

    async def search(self, params: SearchParameters) -> List[SearchResultItem]:
        """
        执行混合搜索

        Args:
            params: 搜索参数

        Returns:
            List[SearchResultItem]: 搜索结果列表
        """
        if not self.is_initialized:
            await self.initialize()

        # 验证搜索参数
        self.validate_search_parameters(params)

        start_time = time.time()

        try:
            logger.info(
                f"开始混合搜索 - 查询: '{params.query[:50]}...', "
                f"向量: {'有' if params.query_embedding else '无'}, "
                f"节点类型: {params.node_types}"
            )

            # 执行混合搜索策略
            search_results = await self._execute_hybrid_search(params)

            # 记录性能指标
            self.record_search_performance(start_time, len(search_results))

            return search_results

        except Exception as e:
            error_msg = f"混合搜索执行失败: {e}"
            logger.error(error_msg)
            raise SearchExecutionError(error_msg)

    async def _execute_hybrid_search(self, params: SearchParameters) -> List[SearchResultItem]:
        """
        执行混合搜索的核心逻辑

        Args:
            params: 搜索参数

        Returns:
            List[SearchResultItem]: 搜索结果
        """
        # 提取权重参数
        vector_weight = params.filters.get("vector_weight", self.default_vector_weight)
        text_weight = params.filters.get("text_weight", self.default_text_weight)

        # 确保权重之和为1
        total_weight = vector_weight + text_weight
        if total_weight > 0:
            vector_weight /= total_weight
            text_weight /= total_weight

        # 并行执行向量搜索和文本搜索
        vector_results = []
        text_results = []

        # 向量搜索
        if params.query_embedding and self.vector_search_service.is_initialized:
            try:
                vector_params = SearchParameters(
                    query_embedding=params.query_embedding,
                    node_types=params.node_types,
                    limit=self.max_results_per_mode,
                    threshold=max(0.3, params.threshold * 0.8),  # 降低向量搜索阈值
                    include_metadata=params.include_metadata,
                    filters=params.filters,
                )
                vector_results = await self.vector_search_service.search(vector_params)
            except Exception as e:
                logger.warning(f"向量搜索失败，将跳过: {e}")

        # 文本搜索
        if params.query:
            try:
                text_params = SearchParameters(
                    query=params.query,
                    node_types=params.node_types,
                    limit=self.max_results_per_mode,
                    threshold=max(0.1, params.threshold * 0.6),  # 降低文本搜索阈值
                    include_metadata=params.include_metadata,
                    filters=params.filters,
                )
                text_results = await self._execute_text_search(text_params)
            except Exception as e:
                logger.warning(f"文本搜索失败，将跳过: {e}")

        # 融合搜索结果
        hybrid_results = self._merge_search_results(
            vector_results, text_results, vector_weight, text_weight, params
        )

        # 应用最终的阈值过滤和限制
        hybrid_results = [r for r in hybrid_results if r.score >= params.threshold]
        hybrid_results = hybrid_results[: params.limit]

        logger.info(
            f"混合搜索完成 - 向量结果: {len(vector_results)}, "
            f"文本结果: {len(text_results)}, 融合结果: {len(hybrid_results)}"
        )

        return hybrid_results

    async def _execute_text_search(self, params: SearchParameters) -> List[SearchResultItem]:
        """
        执行文本搜索

        Args:
            params: 搜索参数

        Returns:
            List[SearchResultItem]: 文本搜索结果
        """
        try:
            async with self.neo4j_driver.session(
                database=self.database_name
            ) as session:
                # 构建节点类型过滤
                node_filter = self.build_node_filter(params.node_types)

                # 文本搜索查询
                query = f"""
                MATCH (n)
                {node_filter}
                WHERE toLower(n.content) CONTAINS toLower($query_text)
                   OR (n.name IS NOT NULL AND toLower(n.name) CONTAINS toLower($query_text))
                   OR (n.title IS NOT NULL AND toLower(n.title) CONTAINS toLower($query_text))
                   OR (n.description IS NOT NULL AND toLower(n.description) CONTAINS toLower($query_text))
                RETURN n, labels(n)[0] AS node_type
                """

                result = await session.run(query, query_text=params.query)
                records = await result.data()

                text_results = []

                for record in records:
                    try:
                        node_data = dict(record["n"])
                        node_type = record["node_type"]

                        # 计算文本匹配分数
                        text_score = self.calculate_text_relevance_score(
                            params.query, node_data
                        )

                        if text_score >= params.threshold:
                            # 创建搜索结果项
                            search_item = SearchResultItem(
                                id=node_data["id"],
                                node_type=node_type,
                                score=text_score,
                                title=node_data.get("title")
                                or node_data.get("name")
                                or node_data.get("content", "")[:50] + "...",
                                content=node_data.get("content", ""),
                                created_at=node_data.get("created_at", ""),
                                confidence=node_data.get("confidence", 1.0),
                            )

                            # 添加文本搜索特有的元数据
                            if params.include_metadata:
                                search_item.metadata.update(
                                    {
                                        "text_score": text_score,
                                        "highlight_snippets": self.extract_highlight_snippets(
                                            params.query, node_data.get("content", "")
                                        ),
                                        "properties": node_data.get("properties", {}),
                                        "tags": node_data.get("tags", []),
                                        "source": node_data.get("source"),
                                        "processing_status": node_data.get(
                                            "processing_status"
                                        ),
                                    }
                                )

                            text_results.append(search_item)

                    except Exception as e:
                        logger.warning(f"处理文本搜索结果失败: {e}")
                        continue

                # 按文本相关性排序
                text_results.sort(key=lambda x: x.score, reverse=True)

                return text_results

        except Exception as e:
            raise SearchExecutionError(f"文本搜索执行失败: {e}")

    def _merge_search_results(
        self,
        vector_results: List[SearchResultItem],
        text_results: List[SearchResultItem],
        vector_weight: float,
        text_weight: float,
        params: SearchParameters,
    ) -> List[SearchResultItem]:
        """
        融合向量搜索和文本搜索结果

        Args:
            vector_results: 向量搜索结果
            text_results: 文本搜索结果
            vector_weight: 向量搜索权重
            text_weight: 文本搜索权重
            params: 搜索参数

        Returns:
            List[SearchResultItem]: 融合后的搜索结果
        """
        try:
            # 创建结果索引
            vector_index = {item.id: item for item in vector_results}
            text_index = {item.id: item for item in text_results}

            # 获取所有唯一ID
            all_ids = set(vector_index.keys()) | set(text_index.keys())

            merged_results = []

            for item_id in all_ids:
                vector_item = vector_index.get(item_id)
                text_item = text_index.get(item_id)

                # 计算融合分数
                combined_score = 0.0
                vector_score = vector_item.score if vector_item else 0.0
                text_score = text_item.score if text_item else 0.0

                # 应用权重
                if vector_item and text_item:
                    # 两种搜索都有结果 - 使用加权平均
                    combined_score = (
                        vector_score * vector_weight + text_score * text_weight
                    )
                    # 同时匹配的结果给予额外加分
                    combined_score *= 1.1
                elif vector_item:
                    # 仅向量搜索有结果
                    combined_score = vector_score * vector_weight
                elif text_item:
                    # 仅文本搜索有结果
                    combined_score = text_score * text_weight

                # 选择最佳的结果项作为基础
                base_item = vector_item or text_item
                if vector_item and text_item:
                    # 选择内容更丰富的项
                    base_item = (
                        vector_item
                        if len(vector_item.content) > len(text_item.content)
                        else text_item
                    )

                # 创建融合后的结果项
                merged_item = SearchResultItem(
                    id=base_item.id,
                    node_type=base_item.node_type,
                    score=combined_score,
                    title=base_item.title,
                    content=base_item.content,
                    created_at=base_item.created_at,
                    confidence=base_item.confidence,
                )

                # 融合元数据
                if params.include_metadata:
                    merged_item.metadata = base_item.metadata.copy()
                    merged_item.metadata.update(
                        {
                            "vector_score": vector_score,
                            "text_score": text_score,
                            "combined_score": combined_score,
                            "search_modes": [],
                        }
                    )

                    if vector_item:
                        merged_item.metadata["search_modes"].append(SearchMode.VECTOR)
                    if text_item:
                        merged_item.metadata["search_modes"].append(SearchMode.TEXT)
                        # 添加高亮片段
                        if "highlight_snippets" not in merged_item.metadata:
                            merged_item.metadata["highlight_snippets"] = (
                                self.extract_highlight_snippets(
                                    params.query, base_item.content
                                )
                            )

                merged_results.append(merged_item)

            # 按融合分数排序
            merged_results.sort(key=lambda x: x.score, reverse=True)

            return merged_results

        except Exception as e:
            logger.error(f"搜索结果融合失败: {e}")
            # 如果融合失败，返回向量搜索结果作为备用
            return vector_results if vector_results else text_results

    async def advanced_hybrid_search(
        self,
        query: str,
        query_embedding: Optional[List[float]] = None,
        node_types: Optional[List[NodeType]] = None,
        vector_weight: float = 0.6,
        text_weight: float = 0.4,
        limit: int = 20,
        threshold: float = 0.5,
        boost_recent: bool = False,
        boost_high_confidence: bool = True,
    ) -> List[SearchResultItem]:
        """
        高级混合搜索，支持更多优化策略

        Args:
            query: 查询文本
            query_embedding: 查询向量
            node_types: 节点类型过滤
            vector_weight: 向量搜索权重
            text_weight: 文本搜索权重
            limit: 结果数量限制
            threshold: 相似度阈值
            boost_recent: 是否提升最近创建内容的排名
            boost_high_confidence: 是否提升高置信度内容的排名

        Returns:
            List[SearchResultItem]: 搜索结果
        """
        try:
            # 构建搜索参数
            params = SearchParameters(
                query=query,
                query_embedding=query_embedding,
                node_types=node_types,
                limit=limit * 2,  # 获取更多结果以便后续优化
                threshold=threshold * 0.8,  # 降低初始阈值
                include_metadata=True,
                filters={
                    "vector_weight": vector_weight,
                    "text_weight": text_weight,
                    "boost_recent": boost_recent,
                    "boost_high_confidence": boost_high_confidence,
                },
            )

            # 执行基础混合搜索
            results = await self.search(params)

            # 应用高级优化策略
            optimized_results = self._apply_advanced_ranking(results, params)

            # 应用最终阈值和限制
            final_results = [r for r in optimized_results if r.score >= threshold]
            final_results = final_results[:limit]

            logger.info(
                f"高级混合搜索完成 - 基础结果: {len(results)}, "
                f"优化后结果: {len(final_results)}"
            )

            return final_results

        except Exception as e:
            logger.error(f"高级混合搜索失败: {e}")
            raise SearchExecutionError(f"高级混合搜索失败: {e}")

    def _apply_advanced_ranking(
        self, results: List[SearchResultItem], params: SearchParameters
    ) -> List[SearchResultItem]:
        """
        应用高级排名优化策略

        Args:
            results: 搜索结果列表
            params: 搜索参数

        Returns:
            List[SearchResultItem]: 优化后的结果列表
        """
        try:
            boost_recent = params.filters.get("boost_recent", False)
            boost_high_confidence = params.filters.get("boost_high_confidence", True)

            for result in results:
                original_score = result.score

                # 时间衰减/提升
                if boost_recent and result.created_at:
                    try:
                        from datetime import datetime, timezone

                        created_time = datetime.fromisoformat(
                            result.created_at.replace("Z", "+00:00")
                        )
                        now = datetime.now(timezone.utc)
                        days_old = (now - created_time).days

                        # 最近30天内的内容给予提升
                        if days_old <= 30:
                            time_boost = 1.0 + (30 - days_old) / 100.0
                            result.score *= time_boost
                    except Exception:
                        pass

                # 置信度提升
                if boost_high_confidence and result.confidence > 0.8:
                    confidence_boost = 1.0 + (result.confidence - 0.8) * 0.5
                    result.score *= confidence_boost

                # 内容长度调整 - 适中长度的内容略微提升
                content_length = len(result.content)
                if 100 <= content_length <= 500:
                    result.score *= 1.05
                elif content_length > 1000:
                    result.score *= 0.95

                # 记录优化信息
                if hasattr(result, "metadata") and result.metadata:
                    result.metadata["original_score"] = original_score
                    result.metadata["ranking_applied"] = True

            # 重新排序
            results.sort(key=lambda x: x.score, reverse=True)

            return results

        except Exception as e:
            logger.warning(f"应用高级排名策略失败: {e}")
            return results

    async def search_with_filters(
        self,
        query: str,
        filters: Dict[str, Any],
        query_embedding: Optional[List[float]] = None,
        limit: int = 20,
    ) -> List[SearchResultItem]:
        """
        带过滤条件的混合搜索

        Args:
            query: 查询文本
            filters: 过滤条件字典
            query_embedding: 查询向量
            limit: 结果限制

        Returns:
            List[SearchResultItem]: 搜索结果
        """
        try:
            # 解析过滤条件
            node_types = None
            if "node_types" in filters:
                node_types = [NodeType(nt) for nt in filters["node_types"]]

            source_filter = filters.get("source")
            date_range = filters.get("date_range")
            confidence_min = filters.get("confidence_min", 0.0)

            # 构建搜索参数
            params = SearchParameters(
                query=query,
                query_embedding=query_embedding,
                node_types=node_types,
                limit=limit * 2,  # 预留空间进行过滤
                threshold=0.3,
                include_metadata=True,
                filters=filters,
            )

            # 执行搜索
            results = await self.search(params)

            # 应用额外过滤
            filtered_results = []
            for result in results:
                # 置信度过滤
                if result.confidence < confidence_min:
                    continue

                # 来源过滤
                if source_filter and result.metadata.get("source") != source_filter:
                    continue

                # 日期范围过滤
                if date_range and result.created_at:
                    try:
                        from datetime import datetime

                        created_date = datetime.fromisoformat(
                            result.created_at.replace("Z", "+00:00")
                        )
                        start_date = datetime.fromisoformat(date_range["start"])
                        end_date = datetime.fromisoformat(date_range["end"])

                        if not (start_date <= created_date <= end_date):
                            continue
                    except Exception:
                        continue

                filtered_results.append(result)

            # 限制结果数量
            filtered_results = filtered_results[:limit]

            logger.info(
                f"带过滤的混合搜索完成 - 过滤前: {len(results)}, "
                f"过滤后: {len(filtered_results)}"
            )

            return filtered_results

        except Exception as e:
            logger.error(f"带过滤的混合搜索失败: {e}")
            raise SearchExecutionError(f"过滤搜索失败: {e}")

    async def cleanup(self):
        """清理资源"""
        try:
            # 清理向量搜索服务
            if self.vector_search_service:
                await self.vector_search_service.cleanup()

            self.is_initialized = False

            logger.info("混合搜索服务资源清理完成")

        except Exception as e:
            logger.error(f"混合搜索服务清理失败: {e}")

    async def get_search_statistics(self) -> Dict[str, Any]:
        """
        获取搜索统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 获取基础服务统计
            service_stats = self.get_service_stats()

            # 获取向量搜索统计
            vector_stats = {}
            if self.vector_search_service and self.vector_search_service.is_initialized:
                vector_stats = await self.vector_search_service.get_vector_statistics()

            return {
                "hybrid_search": service_stats,
                "vector_search": vector_stats,
                "configuration": {
                    "default_vector_weight": self.default_vector_weight,
                    "default_text_weight": self.default_text_weight,
                    "min_score_threshold": self.min_score_threshold,
                    "max_results_per_mode": self.max_results_per_mode,
                },
            }

        except Exception as e:
            logger.error(f"获取搜索统计失败: {e}")
            return {"error": str(e)}


# ================== 便捷函数 ==================


async def create_hybrid_search_service(neo4j_driver=None) -> HybridSearchService:
    """
    创建并初始化混合搜索服务

    Args:
        neo4j_driver: Neo4j驱动实例

    Returns:
        HybridSearchService: 初始化后的服务实例
    """
    service = HybridSearchService(neo4j_driver)
    await service.initialize()
    return service


if __name__ == "__main__":
    """混合搜索服务测试"""

    import asyncio

    async def test_hybrid_search():
        """测试混合搜索服务"""
        print("🔍 开始测试混合搜索服务...")

        try:
            # 创建测试服务
            service = HybridSearchService()
            print(f"📊 服务统计: {service.get_service_stats()}")

            # 测试参数验证
            from services.graph.search.search_base import SearchParameters

            test_params = SearchParameters(
                query="测试查询文本",
                query_embedding=[0.1] * 1024,
                threshold=0.5,
                limit=10,
                filters={"vector_weight": 0.7, "text_weight": 0.3},
            )

            is_valid = service.validate_search_parameters(test_params)
            print(f"✅ 参数验证测试: {'通过' if is_valid else '失败'}")

            # 测试文本相关性计算
            content_data = {
                "content": "这是一个关于人工智能的测试文档",
                "title": "AI测试",
                "description": "人工智能技术介绍",
            }
            relevance_score = service.calculate_text_relevance_score(
                "人工智能", content_data
            )
            print(f"✅ 文本相关性计算测试: {relevance_score:.3f}")

            # 测试高亮片段提取
            snippets = service.extract_highlight_snippets(
                "人工智能",
                "人工智能是计算机科学的一个分支，它研究如何让机器模拟人类的智能行为。",
            )
            print(f"✅ 高亮片段提取测试: {len(snippets)} 个片段")

            print("🎉 混合搜索服务测试完成！")

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback

            traceback.print_exc()

    # 运行测试
    asyncio.run(test_hybrid_search())
