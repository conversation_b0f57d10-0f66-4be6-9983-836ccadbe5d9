"""
智能记忆引擎 - 知识图谱服务协调器 v2.0

重新组织的轻量级知识图谱服务协调器：
- 集成connection、nodes、search、visualization各个子服务
- 提供向后兼容的统一API接口
- 依赖注入模式整合各子服务
- 继承BaseService提供标准服务接口
- 完整的错误处理和日志记录

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 15:33:51
"""

import asyncio
import logging
import time
import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
from contextlib import asynccontextmanager

from ..core.base_service import BaseService, ServiceHealthCheck, ServiceStatus
from .connection.neo4j_driver import Neo4jDriverService as Neo4jConnectionManager
from .search import VectorSearchService, HybridSearchService
from .visualization.graph_builder import GraphVisualizationBuilder
from .nodes import EntityNodeService, StatementNodeService, EpisodeNodeService
from models import (
    Episode,
    Entity,
    Statement,
    NodeType,
    RelationshipType,
    ProcessingStatus,
    ContentSource,
)
from config import settings


class KnowledgeGraphOrchestrator(BaseService):
    """
    知识图谱服务协调器 v2.0

    核心功能：
    1. 统一管理所有图谱相关子服务
    2. 提供与原有knowledge_service.py兼容的API
    3. 智能服务编排和依赖管理
    4. 完整的生命周期管理和错误处理
    5. 性能监控和健康检查
    """

    def __init__(
        self,
        config: Optional[Dict[str, Any]] = None,
        logger: Optional[logging.Logger] = None,
    ):
        super().__init__(
            service_name="knowledge_graph_orchestrator",
            config=config or {},
            logger=logger or logging.getLogger("smart_memory.knowledge_orchestrator"),
        )

        # 核心子服务
        self.connection_manager: Optional[Neo4jConnectionManager] = None
        self.vector_search: Optional[VectorSearchService] = None
        self.hybrid_search: Optional[HybridSearchService] = None
        self.visualization_builder: Optional[GraphVisualizationBuilder] = None

        # 节点服务
        self.entity_service: Optional[EntityNodeService] = None
        self.statement_service: Optional[StatementNodeService] = None
        self.episode_service: Optional[EpisodeNodeService] = None

        # 配置参数
        self.enable_advanced_search = self.config.get("enable_advanced_search", True)
        self.enable_visualization = self.config.get("enable_visualization", True)
        self.batch_size = self.config.get("batch_size", 100)
        self.max_retries = self.config.get("max_retries", 3)

        # 性能配置
        self.connection_timeout = self.config.get("connection_timeout", 30)
        self.query_timeout = self.config.get("query_timeout", 60)
        self.health_check_interval = self.config.get(
            "health_check_interval", 300
        )  # 5分钟

        # 缓存配置
        self.enable_result_caching = self.config.get("enable_result_caching", True)
        self.cache_ttl = self.config.get("cache_ttl", 600)  # 10分钟

        # Neo4j配置
        self.neo4j_config = settings.get_neo4j_config()

        # 服务状态跟踪
        self._last_health_check = 0
        self._service_available = False
        self._initialization_time = None

    async def _initialize_service(self) -> None:
        """初始化知识图谱协调器的所有子服务"""
        try:
            start_time = time.time()
            self.logger.info("开始初始化知识图谱服务协调器...")

            # 1. 初始化连接管理器（核心依赖）
            await self._initialize_connection_manager()

            # 2. 初始化节点服务（核心功能）
            await self._initialize_node_services()

            # 3. 初始化搜索服务（可选）
            if self.enable_advanced_search:
                await self._initialize_search_services()

            # 4. 初始化可视化构建器（可选）
            if self.enable_visualization:
                await self._initialize_visualization_builder()

            # 5. 验证服务依赖关系
            await self._validate_service_dependencies()

            # 6. 执行初始健康检查
            await self._perform_initial_health_check()

            self._initialization_time = time.time() - start_time
            self._service_available = True

            self.logger.info(
                f"✅ 知识图谱服务协调器初始化完成 - 耗时: {self._initialization_time:.2f}s"
            )

        except Exception as e:
            self.logger.error(f"知识图谱服务协调器初始化失败: {e}")
            await self._cleanup_service()
            raise

    async def _initialize_connection_manager(self) -> None:
        """初始化Neo4j连接管理器"""
        try:
            self.logger.info("初始化Neo4j连接管理器...")

            # 创建连接管理器实例
            self.connection_manager = Neo4jConnectionManager()

            # 初始化连接管理器
            await self.connection_manager.initialize()

            # 验证初始化是否成功
            if not self.connection_manager.is_ready:
                raise Exception("Neo4j连接管理器初始化失败")

            self.logger.info("✅ Neo4j连接管理器初始化成功")

        except Exception as e:
            self.logger.error(f"Neo4j连接管理器初始化失败: {e}")
            raise

    async def _initialize_node_services(self) -> None:
        """初始化节点服务"""
        try:
            self.logger.info("初始化节点服务...")

            # 获取Neo4j驱动
            driver = (
                self.connection_manager.get_driver()
                if self.connection_manager
                else None
            )

            if not driver:
                raise Exception("Neo4j驱动不可用")

            # 初始化实体服务
            try:
                self.entity_service = EntityNodeService(driver)
                await self.entity_service.initialize()
                self.logger.info("✅ 实体服务初始化成功")
            except Exception as e:
                self.logger.error(f"实体服务初始化失败: {e}")
                self.entity_service = None
                raise

            # 初始化陈述服务
            try:
                self.statement_service = StatementNodeService(driver)
                await self.statement_service.initialize()
                self.logger.info("✅ 陈述服务初始化成功")
            except Exception as e:
                self.logger.error(f"陈述服务初始化失败: {e}")
                self.statement_service = None
                raise

            # 初始化Episode服务
            try:
                self.episode_service = EpisodeNodeService(driver)
                await self.episode_service.initialize()
                self.logger.info("✅ Episode服务初始化成功")
            except Exception as e:
                self.logger.error(f"Episode服务初始化失败: {e}")
                self.episode_service = None
                raise

            self.logger.info("🎯 所有节点服务初始化完成")

        except Exception as e:
            self.logger.error(f"节点服务初始化失败: {e}")
            raise

    async def _initialize_search_services(self) -> None:
        """初始化搜索服务"""
        try:
            self.logger.info("初始化搜索服务...")

            # 获取Neo4j驱动
            driver = (
                self.connection_manager.get_driver()
                if self.connection_manager
                else None
            )

            # 初始化向量搜索服务
            try:
                self.vector_search = VectorSearchService(driver)
                await self.vector_search.initialize()
                self.logger.info("✅ 向量搜索服务初始化成功")
            except Exception as e:
                self.logger.warning(f"向量搜索服务初始化失败: {e}")
                self.vector_search = None

            # 初始化混合搜索服务 - 重新启用并修复调用问题
            try:
                self.hybrid_search = HybridSearchService(driver)
                await self.hybrid_search.initialize()
                self.logger.info("✅ 混合搜索服务初始化成功")
            except Exception as e:
                self.logger.warning(f"混合搜索服务初始化失败: {e}")
                self.hybrid_search = None

            # 至少要有一个搜索服务可用
            if not (self.vector_search or self.hybrid_search):
                self.logger.warning("所有搜索服务初始化失败，将使用基础搜索")
            else:
                self.logger.info("🔍 搜索服务初始化完成")

        except Exception as e:
            self.logger.warning(f"搜索服务初始化失败: {e}")

    async def _initialize_visualization_builder(self) -> None:
        """初始化可视化构建器"""
        try:
            self.logger.info("初始化可视化构建器...")

            # 获取Neo4j驱动
            driver = (
                self.connection_manager.get_driver()
                if self.connection_manager
                else None
            )

            # 创建可视化构建器实例
            self.visualization_builder = GraphVisualizationBuilder(driver)

            # 初始化可视化构建器
            await self.visualization_builder.initialize()

            self.logger.info("✅ 可视化构建器初始化成功")

        except Exception as e:
            self.logger.warning(f"可视化构建器初始化失败: {e}")
            self.visualization_builder = None

    async def _validate_service_dependencies(self) -> None:
        """验证服务依赖关系"""
        try:
            self.logger.info("验证服务依赖关系...")

            # 验证核心依赖
            if not self.connection_manager:
                raise Exception("Neo4j连接管理器未初始化")

            # 记录可用服务
            available_services = ["ConnectionManager"]

            if self.vector_search:
                available_services.append("VectorSearch")

            if self.hybrid_search:
                available_services.append("HybridSearch")

            if self.visualization_builder:
                available_services.append("Visualization")

            self.logger.info(f"✅ 可用服务: {', '.join(available_services)}")

        except Exception as e:
            self.logger.error(f"服务依赖验证失败: {e}")
            raise

    async def _perform_initial_health_check(self) -> None:
        """执行初始健康检查"""
        try:
            self.logger.info("执行初始健康检查...")

            health_check = await self._perform_health_check()

            if not health_check.is_healthy():
                self.logger.warning(f"初始健康检查未通过: {health_check.message}")
                # 不抛出异常，允许服务在降级模式下运行
            else:
                self.logger.info("✅ 初始健康检查通过")

        except Exception as e:
            self.logger.warning(f"初始健康检查失败: {e}")

    async def _cleanup_service(self) -> None:
        """清理所有子服务"""
        self.logger.info("开始清理知识图谱服务协调器...")

        # 清理子服务
        services_to_cleanup = [
            ("可视化构建器", self.visualization_builder),
            ("混合搜索服务", self.hybrid_search),
            ("向量搜索服务", self.vector_search),
            ("连接管理器", self.connection_manager),
        ]

        for service_name, service in services_to_cleanup:
            if service:
                try:
                    await service.cleanup()
                    self.logger.debug(f"{service_name}清理完成")
                except Exception as e:
                    self.logger.warning(f"清理{service_name}失败: {e}")

        # 重置所有服务引用
        self.connection_manager = None
        self.vector_search = None
        self.hybrid_search = None
        self.visualization_builder = None

        # 重置状态
        self._service_available = False
        self._last_health_check = 0
        self._initialization_time = None

        self.logger.info("✅ 知识图谱服务协调器清理完成")

    async def _perform_health_check(self) -> ServiceHealthCheck:
        """执行健康检查"""
        try:
            start_time = time.time()
            details = {}
            all_healthy = True
            messages = []

            # 检查连接管理器
            if self.connection_manager:
                try:
                    is_healthy = await self.connection_manager.is_connection_healthy()
                    details["connection_manager"] = {
                        "status": "healthy" if is_healthy else "unhealthy",
                        "connection_info": self.connection_manager.get_connection_info(),
                    }
                    if not is_healthy:
                        all_healthy = False
                        messages.append("Neo4j连接不健康")
                except Exception as e:
                    details["connection_manager"] = {"status": "error", "error": str(e)}
                    all_healthy = False
                    messages.append(f"连接管理器检查失败: {e}")
            else:
                details["connection_manager"] = {"status": "not_initialized"}
                all_healthy = False
                messages.append("连接管理器未初始化")

            # 检查搜索服务
            search_services_healthy = False

            if self.vector_search:
                try:
                    vector_health = await self.vector_search.health_check()
                    details["vector_search"] = vector_health.to_dict()
                    if vector_health.is_healthy():
                        search_services_healthy = True
                except Exception as e:
                    details["vector_search"] = {"status": "error", "error": str(e)}
            else:
                details["vector_search"] = {"status": "not_available"}

            if self.hybrid_search:
                try:
                    hybrid_health = await self.hybrid_search.health_check()
                    details["hybrid_search"] = hybrid_health.to_dict()
                    if hybrid_health.is_healthy():
                        search_services_healthy = True
                except Exception as e:
                    details["hybrid_search"] = {"status": "error", "error": str(e)}
            else:
                details["hybrid_search"] = {"status": "not_available"}

            if not search_services_healthy:
                messages.append("搜索服务不可用")

            # 检查可视化构建器
            if self.visualization_builder:
                try:
                    vis_health = await self.visualization_builder.health_check()
                    details["visualization_builder"] = vis_health.to_dict()
                    # 可视化服务不是必需的，不影响整体健康状态
                except Exception as e:
                    details["visualization_builder"] = {
                        "status": "error",
                        "error": str(e),
                    }
            else:
                details["visualization_builder"] = {"status": "not_available"}

            # 确定整体状态
            if all_healthy:
                status = ServiceStatus.READY
                message = "知识图谱服务运行正常"
            elif self.connection_manager:
                # 核心连接可用，但其他服务可能有问题
                status = ServiceStatus.READY  # 降级但可用
                message = f"服务降级运行: {'; '.join(messages)}"
            else:
                status = ServiceStatus.ERROR
                message = f"服务不可用: {'; '.join(messages)}"

            # 更新健康检查时间戳
            self._last_health_check = time.time()

            return ServiceHealthCheck(
                service_name=self.service_name,
                status=status,
                message=message,
                details=details,
                response_time=time.time() - start_time,
            )

        except Exception as e:
            self.logger.error(f"知识图谱协调器健康检查失败: {e}")
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=ServiceStatus.ERROR,
                message=f"健康检查异常: {str(e)}",
                response_time=time.time() - start_time,
            )

    # ================== 向后兼容的API接口 ==================

    async def create_episode(self, episode: Episode) -> str:
        """
        创建Episode节点（临时实现）

        Args:
            episode: Episode实例

        Returns:
            str: 创建的Episode ID
        """
        if not self.is_ready:
            await self.initialize()

        if not self.connection_manager:
            raise Exception("连接管理器不可用")

        # 临时实现：直接返回生成的ID
        # TODO: 实现真正的数据库操作
        episode_id = str(uuid.uuid4())
        self.logger.warning(f"临时实现：创建Episode {episode_id}")
        return episode_id

    async def get_episode(self, episode_id: str) -> Optional[Dict[str, Any]]:
        """
        根据ID获取Episode节点（临时实现）

        Args:
            episode_id: Episode ID

        Returns:
            Optional[Dict[str, Any]]: Episode节点数据
        """
        if not self.is_ready:
            await self.initialize()

        if not self.connection_manager:
            raise Exception("连接管理器不可用")

        # 临时实现：返回空结果
        self.logger.warning(f"临时实现：查询Episode {episode_id}")
        return None

    async def create_entity(self, entity: Entity) -> str:
        """
        创建或更新Entity节点（临时实现）

        Args:
            entity: Entity实例

        Returns:
            str: 实体ID
        """
        if not self.is_ready:
            await self.initialize()

        if not self.connection_manager:
            raise Exception("连接管理器不可用")

        # 临时实现：生成并返回ID
        entity_id = str(uuid.uuid4())
        self.logger.warning(f"临时实现：创建Entity {entity_id}")
        return entity_id

    async def get_entity(self, entity_id: str) -> Optional[Dict[str, Any]]:
        """
        根据ID获取Entity节点（临时实现）

        Args:
            entity_id: Entity ID

        Returns:
            Optional[Dict[str, Any]]: Entity节点数据
        """
        if not self.is_ready:
            await self.initialize()

        if not self.connection_manager:
            raise Exception("连接管理器不可用")

        # 临时实现：返回空结果
        self.logger.warning(f"临时实现：查询Entity {entity_id}")
        return None

    async def create_statement(self, statement: Statement) -> str:
        """
        创建Statement节点（临时实现）

        Args:
            statement: Statement实例

        Returns:
            str: 创建的Statement ID
        """
        if not self.is_ready:
            await self.initialize()

        if not self.connection_manager:
            raise Exception("连接管理器不可用")

        # 临时实现：生成并返回ID
        statement_id = str(uuid.uuid4())
        self.logger.warning(f"临时实现：创建Statement {statement_id}")
        return statement_id

    async def get_statement(self, statement_id: str) -> Optional[Dict[str, Any]]:
        """
        根据ID获取Statement节点（临时实现）

        Args:
            statement_id: Statement ID

        Returns:
            Optional[Dict[str, Any]]: Statement节点数据
        """
        if not self.is_ready:
            await self.initialize()

        if not self.connection_manager:
            raise Exception("连接管理器不可用")

        # 临时实现：返回空结果
        self.logger.warning(f"临时实现：查询Statement {statement_id}")
        return None

    async def create_episode_with_knowledge(
        self, episode: Episode, entities: List[Entity], statements: List[Statement]
    ) -> Dict[str, Any]:
        """
        批量创建Episode及其关联的实体和陈述

        Args:
            episode: Episode实例
            entities: 实体列表
            statements: 陈述列表

        Returns:
            Dict[str, Any]: 创建结果统计
        """
        if not self.is_ready:
            await self.initialize()

        if not all([self.episode_service, self.entity_service, self.statement_service]):
            raise Exception("节点服务不可用")

        try:
            self.logger.info(
                f"开始批量创建Episode及知识: Episode ID={episode.id}, 实体数={len(entities)}, 陈述数={len(statements)}"
            )

            # 1. 创建Episode
            episode_id = await self.episode_service.create_episode(episode)
            self.logger.debug(f"Episode创建成功: {episode_id}")

            # 2. 批量创建实体
            entity_ids = []
            for entity in entities:
                entity_id = await self.entity_service.create_entity(entity)
                entity_ids.append(entity_id)
                # 创建Episode与Entity的关系
                await self.episode_service.link_to_entity(
                    episode_id, entity_id, "CONTAINS"
                )

            self.logger.debug(f"实体创建成功: {len(entity_ids)}个")

            # 3. 批量创建陈述
            statement_ids = []
            for statement in statements:
                statement_id = await self.statement_service.create_statement(statement)
                statement_ids.append(statement_id)
                # 创建Episode与Statement的关系
                await self.episode_service.link_to_statement(
                    episode_id, statement_id, "CONTAINS"
                )

            self.logger.debug(f"陈述创建成功: {len(statement_ids)}个")

            self.logger.info(
                f"✅ 批量创建完成: Episode {episode_id}, 实体: {len(entity_ids)}, 陈述: {len(statement_ids)}"
            )

            return {
                "episode_id": episode_id,
                "entities_created": len(entity_ids),
                "statements_created": len(statement_ids),
                "success": True,
                "entity_ids": entity_ids,
                "statement_ids": statement_ids,
            }

        except Exception as e:
            self.logger.error(f"批量创建失败: {e}")
            return {
                "episode_id": None,
                "entities_created": 0,
                "statements_created": 0,
                "success": False,
                "error": str(e),
            }

    # ================== 搜索功能 ==================

    async def similarity_search(
        self,
        query_embedding: List[float],
        node_types: Optional[List[NodeType]] = None,
        limit: int = 10,
        threshold: float = 0.7,
        include_metadata: bool = False,
    ) -> List[Dict[str, Any]]:
        """
        基于向量相似度的语义搜索（向后兼容）

        Args:
            query_embedding: 查询向量
            node_types: 搜索的节点类型过滤
            limit: 返回结果数量限制
            threshold: 相似度阈值
            include_metadata: 是否包含详细元数据

        Returns:
            List[Dict[str, Any]]: 相似度搜索结果列表
        """
        if not self.is_ready:
            await self.initialize()

        # 优先使用向量搜索服务
        if self.vector_search:
            try:
                # 构建搜索参数 - 使用SearchParameters对象
                from .search.search_base import SearchParameters

                search_params = SearchParameters(
                    query="",  # 语义搜索不需要文本查询
                    query_embedding=query_embedding,
                    node_types=node_types,
                    limit=limit,
                    threshold=threshold,
                    include_metadata=include_metadata,
                )

                results = await self.vector_search.search(search_params)

                # 转换为兼容格式
                compatible_results = []
                for result in results:
                    result_dict = {
                        "id": result.id,
                        "node_type": result.node_type,
                        "similarity_score": result.score,
                        "title": result.title,
                        "content": result.content,
                        "created_at": result.created_at,
                        "confidence": result.confidence,
                    }

                    if include_metadata and result.metadata:
                        result_dict.update(result.metadata)

                    compatible_results.append(result_dict)

                return compatible_results

            except Exception as e:
                self.logger.warning(f"向量搜索失败，回退到基础实现: {e}")

        # 回退到基础搜索实现
        return await self._fallback_similarity_search(
            query_embedding, node_types, limit, threshold, include_metadata
        )

    async def hybrid_search(
        self,
        query_text: str,
        query_embedding: Optional[List[float]] = None,
        node_types: Optional[List[NodeType]] = None,
        limit: int = 10,
        vector_weight: float = 0.6,
        text_weight: float = 0.4,
        similarity_threshold: float = 0.5,
        include_metadata: bool = False,
    ) -> List[Dict[str, Any]]:
        """
        混合搜索：结合向量相似度和文本匹配（向后兼容）

        Args:
            query_text: 查询文本
            query_embedding: 查询向量（可选）
            node_types: 搜索的节点类型过滤
            limit: 返回结果数量限制
            vector_weight: 向量相似度权重
            text_weight: 文本匹配权重
            similarity_threshold: 最小相似度阈值
            include_metadata: 是否包含详细元数据

        Returns:
            List[Dict[str, Any]]: 混合搜索结果列表
        """
        if not self.is_ready:
            await self.initialize()

        # 优先使用混合搜索服务
        if self.hybrid_search:
            try:
                # 构建搜索参数 - 使用SearchParameters对象
                from .search.search_base import SearchParameters

                search_params = SearchParameters(
                    query=query_text or "",
                    query_embedding=query_embedding,
                    node_types=node_types,
                    limit=limit,
                    threshold=similarity_threshold,
                    include_metadata=include_metadata,
                    filters={
                        "vector_weight": vector_weight,
                        "text_weight": text_weight,
                    },
                )

                results = await self.hybrid_search.search(search_params)

                # 转换为兼容格式
                compatible_results = []
                for result in results:
                    result_dict = {
                        "id": result.id,
                        "node_type": result.node_type,
                        "combined_score": result.score,
                        "vector_score": getattr(result, "vector_score", 0.0),
                        "text_score": getattr(result, "text_score", 0.0),
                        "title": result.title,
                        "content": result.content,
                        "created_at": result.created_at,
                        "confidence": result.confidence,
                        "highlight_snippets": getattr(result, "highlight_snippets", []),
                    }

                    if include_metadata and result.metadata:
                        result_dict.update(result.metadata)

                    compatible_results.append(result_dict)

                return compatible_results

            except Exception as e:
                self.logger.warning(f"混合搜索失败，回退到基础实现: {e}")

        # 回退到基础搜索实现
        return await self._fallback_hybrid_search(
            query_text,
            query_embedding,
            node_types,
            limit,
            vector_weight,
            text_weight,
            similarity_threshold,
            include_metadata,
        )

    # ================== 可视化功能 ==================

    async def get_graph_data(
        self,
        node_types: Optional[List[NodeType]] = None,
        limit: int = 100,
        include_metadata: bool = True,
        layout_algorithm: str = "force-directed",
        color_scheme: str = "default",
    ) -> Dict[str, Any]:
        """
        生成vis.js兼容的图谱数据（向后兼容）

        Args:
            node_types: 要包含的节点类型列表
            limit: 节点数量限制
            include_metadata: 是否包含详细元数据
            layout_algorithm: 布局算法
            color_scheme: 色彩方案

        Returns:
            Dict[str, Any]: vis.js兼容的图数据结构
        """
        if not self.is_ready:
            await self.initialize()

        # 使用可视化构建器
        if self.visualization_builder:
            try:
                return await self.visualization_builder.build_graph_data(
                    node_types=node_types,
                    limit=limit,
                    include_metadata=include_metadata,
                    layout_algorithm=layout_algorithm,
                    color_scheme=color_scheme,
                )
            except Exception as e:
                self.logger.warning(f"可视化构建器失败，回退到基础实现: {e}")

        # 回退到基础图数据生成
        return await self._fallback_get_graph_data(
            node_types, limit, include_metadata, layout_algorithm, color_scheme
        )

    # ================== 统计和状态 ==================

    async def get_graph_statistics(self) -> Dict[str, Any]:
        """获取图数据库统计信息（向后兼容）"""
        if not self.is_ready:
            await self.initialize()

        if not self.connection_manager:
            raise Exception("连接管理器不可用")

        try:
            async with self.connection_manager.get_session() as session:
                # 获取节点统计
                node_stats_query = """
                MATCH (n)
                RETURN 
                    labels(n)[0] AS node_type,
                    count(n) AS count
                """

                result = await session.run(node_stats_query)
                node_stats = await result.data()

                # 获取关系统计
                rel_stats_query = """
                MATCH ()-[r]->()
                RETURN type(r) AS relationship_type, count(r) AS count
                """

                rel_result = await session.run(rel_stats_query)
                rel_stats = await rel_result.data()

                return {
                    "nodes": {
                        record["node_type"]: record["count"] for record in node_stats
                    },
                    "relationships": {
                        record["relationship_type"]: record["count"]
                        for record in rel_stats
                    },
                    "total_nodes": sum(record["count"] for record in node_stats),
                    "total_relationships": sum(record["count"] for record in rel_stats),
                    "last_updated": datetime.now(timezone.utc).isoformat(),
                }

        except Exception as e:
            self.logger.error(f"获取图统计信息失败: {e}")
            raise

    async def is_service_healthy(self) -> bool:
        """
        检查服务健康状态（向后兼容）

        Returns:
            bool: 服务是否健康
        """
        try:
            health_check = await self._perform_health_check()
            return health_check.is_healthy()
        except Exception:
            return False

    # ================== Fallback实现 ==================

    async def _fallback_similarity_search(
        self, query_embedding, node_types, limit, threshold, include_metadata
    ):
        """基础向量相似度搜索实现"""
        # 这里实现基础的向量搜索逻辑
        # 为了简化，返回空结果
        self.logger.warning("使用fallback向量搜索实现")
        return []

    async def _fallback_hybrid_search(
        self,
        query_text,
        query_embedding,
        node_types,
        limit,
        vector_weight,
        text_weight,
        similarity_threshold,
        include_metadata,
    ):
        """基础混合搜索实现"""
        # 这里实现基础的混合搜索逻辑
        # 为了简化，返回空结果
        self.logger.warning("使用fallback混合搜索实现")
        return []

    async def _fallback_get_graph_data(
        self, node_types, limit, include_metadata, layout_algorithm, color_scheme
    ):
        """基础图数据生成实现"""
        # 这里实现基础的图数据生成逻辑
        # 为了简化，返回基础结构
        self.logger.warning("使用fallback图数据生成实现")
        return {
            "nodes": [],
            "edges": [],
            "layout": {"randomSeed": 2},
            "statistics": {"total_nodes": 0, "total_edges": 0},
            "metadata": {
                "generated_at": datetime.now(timezone.utc).isoformat(),
                "fallback_mode": True,
            },
        }

    # ================== 便捷方法 ==================

    def get_available_services(self) -> Dict[str, bool]:
        """获取可用服务状态"""
        return {
            "connection_manager": self.connection_manager is not None,
            "vector_search": self.vector_search is not None,
            "hybrid_search": self.hybrid_search is not None,
            "visualization_builder": self.visualization_builder is not None,
        }

    def get_service_versions(self) -> Dict[str, str]:
        """获取服务版本信息"""
        return {
            "orchestrator": "v2.0",
            "connection_manager": getattr(
                self.connection_manager, "version", "unknown"
            ),
            "vector_search": getattr(self.vector_search, "version", "unknown"),
            "hybrid_search": getattr(self.hybrid_search, "version", "unknown"),
            "visualization_builder": getattr(
                self.visualization_builder, "version", "unknown"
            ),
        }


# ================== 全局服务实例 ==================

_knowledge_graph_orchestrator: Optional[KnowledgeGraphOrchestrator] = None


def get_knowledge_graph_orchestrator() -> KnowledgeGraphOrchestrator:
    """
    获取知识图谱服务协调器实例（单例模式）

    Returns:
        KnowledgeGraphOrchestrator: 知识图谱服务协调器实例
    """
    global _knowledge_graph_orchestrator
    if _knowledge_graph_orchestrator is None:
        _knowledge_graph_orchestrator = KnowledgeGraphOrchestrator()
    return _knowledge_graph_orchestrator


# ================== 便捷函数接口 ==================


async def create_knowledge_episode(
    episode: Episode, entities: List[Entity], statements: List[Statement]
) -> Dict[str, Any]:
    """便捷函数：创建完整的知识Episode"""
    orchestrator = get_knowledge_graph_orchestrator()
    return await orchestrator.create_episode_with_knowledge(
        episode, entities, statements
    )


async def get_graph_statistics() -> Dict[str, Any]:
    """便捷函数：获取图数据库统计信息"""
    orchestrator = get_knowledge_graph_orchestrator()
    return await orchestrator.get_graph_statistics()


async def build_graph_visualization(
    node_types: Optional[List[NodeType]] = None,
    limit: int = 100,
    layout_algorithm: str = "force-directed",
) -> Dict[str, Any]:
    """便捷函数：构建图谱可视化数据"""
    orchestrator = get_knowledge_graph_orchestrator()
    return await orchestrator.get_graph_data(
        node_types=node_types, limit=limit, layout_algorithm=layout_algorithm
    )


# ================== 向后兼容别名 ==================

# 为了保持向后兼容性，提供原有接口的别名
KnowledgeGraphService = KnowledgeGraphOrchestrator
get_knowledge_service = get_knowledge_graph_orchestrator


if __name__ == "__main__":
    """知识图谱服务协调器测试"""

    async def test_knowledge_graph_orchestrator():
        """测试知识图谱服务协调器功能"""
        print("🚀 开始测试知识图谱服务协调器...")

        try:
            # 获取协调器实例
            orchestrator = get_knowledge_graph_orchestrator()

            # 使用上下文管理器测试
            async with orchestrator.service_context():
                print("✅ 知识图谱服务协调器初始化成功")

                # 测试健康检查
                health_check = await orchestrator.health_check()
                print(f"🏥 服务健康状态: {health_check.status.value}")
                print(f"📝 健康检查详情: {health_check.message}")

                # 显示可用服务
                available_services = orchestrator.get_available_services()
                print(f"🔧 可用服务: {available_services}")

                # 显示服务版本
                service_versions = orchestrator.get_service_versions()
                print(f"📊 服务版本: {service_versions}")

                if health_check.is_healthy():
                    # 测试统计信息获取
                    try:
                        stats = await orchestrator.get_graph_statistics()
                        print(f"📈 图统计信息: {stats}")
                    except Exception as e:
                        print(f"⚠️ 统计信息获取失败: {e}")

                    # 测试图数据生成
                    try:
                        graph_data = await orchestrator.get_graph_data(limit=10)
                        print(
                            f"🌐 图数据生成成功 - 节点: {len(graph_data['nodes'])}, 边: {len(graph_data['edges'])}"
                        )
                    except Exception as e:
                        print(f"⚠️ 图数据生成失败: {e}")

                else:
                    print("⚠️ 服务不健康，跳过详细测试")

                print("🎉 知识图谱服务协调器测试完成！")

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback

            traceback.print_exc()

    # 运行测试
    asyncio.run(test_knowledge_graph_orchestrator())
