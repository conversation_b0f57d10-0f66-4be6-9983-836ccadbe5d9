"""
智能记忆引擎 - 自定义异常类系统

提供分层的异常处理机制，支持详细的错误信息和上下文追踪：
- 基础服务异常类
- AI服务相关异常
- 图数据库相关异常
- 业务逻辑异常
- 数据验证异常

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 15:26:23
"""

from typing import Optional, Dict, Any, List
from enum import Enum
import traceback
from datetime import datetime, timezone


class ErrorCode(str, Enum):
    """错误码枚举"""
    # 通用错误 (1000-1999)
    UNKNOWN_ERROR = "1000"
    INVALID_INPUT = "1001"
    CONFIGURATION_ERROR = "1002"
    TIMEOUT_ERROR = "1003"
    PERMISSION_DENIED = "1004"
    
    # AI服务错误 (2000-2999)
    AI_SERVICE_UNAVAILABLE = "2000"
    EMBEDDING_GENERATION_FAILED = "2001"
    LLM_REQUEST_FAILED = "2002"
    KNOWLEDGE_EXTRACTION_FAILED = "2003"
    VECTOR_DIMENSION_MISMATCH = "2004"
    API_QUOTA_EXCEEDED = "2005"
    MODEL_NOT_FOUND = "2006"
    
    # 图数据库错误 (3000-3999)
    DATABASE_CONNECTION_FAILED = "3000"
    QUERY_EXECUTION_FAILED = "3001"
    NODE_NOT_FOUND = "3002"
    RELATIONSHIP_CREATION_FAILED = "3003"
    INDEX_OPERATION_FAILED = "3004"
    CONSTRAINT_VIOLATION = "3005"
    TRANSACTION_FAILED = "3006"
    
    # 数据验证错误 (4000-4999)
    VALIDATION_ERROR = "4000"
    SCHEMA_VALIDATION_FAILED = "4001"
    DATA_TYPE_ERROR = "4002"
    BUSINESS_RULE_VIOLATION = "4003"
    CONTENT_TOO_LONG = "4004"
    EMPTY_CONTENT = "4005"
    INVALID_ENCODING = "4006"
    
    # 搜索和查询错误 (5000-5999)
    SEARCH_FAILED = "5000"
    INVALID_QUERY = "5001"
    NO_RESULTS_FOUND = "5002"
    QUERY_TIMEOUT = "5003"


class BaseServiceException(Exception):
    """
    基础服务异常类
    
    提供统一的异常处理接口，包含错误码、详细信息、上下文等
    """
    
    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
        details: Optional[Dict[str, Any]] = None,
        context: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
        http_status: int = 500
    ):
        """
        初始化异常
        
        Args:
            message: 错误消息
            error_code: 错误码
            details: 错误详细信息
            context: 异常上下文信息
            cause: 原始异常
            http_status: HTTP状态码
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.context = context or {}
        self.cause = cause
        self.http_status = http_status
        self.timestamp = datetime.now(timezone.utc).isoformat()
        
        # 自动捕获调用栈
        self.traceback = traceback.format_exc()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            包含异常信息的字典
        """
        result = {
            "error_code": self.error_code.value,
            "message": self.message,
            "timestamp": self.timestamp,
            "http_status": self.http_status,
            "details": self.details,
            "context": self.context
        }
        
        if self.cause:
            result["cause"] = {
                "type": type(self.cause).__name__,
                "message": str(self.cause)
            }
        
        return result
    
    def __str__(self) -> str:
        return f"[{self.error_code.value}] {self.message}"
    
    def __repr__(self) -> str:
        return (
            f"{self.__class__.__name__}("
            f"error_code={self.error_code.value}, "
            f"message='{self.message}', "
            f"http_status={self.http_status})"
        )


class ValidationError(BaseServiceException):
    """数据验证异常"""
    
    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        value: Any = None,
        constraints: Optional[List[str]] = None,
        **kwargs
    ):
        details = kwargs.pop("details", {})
        if field:
            details["field"] = field
        if value is not None:
            details["value"] = str(value)
        if constraints:
            details["constraints"] = constraints
        
        super().__init__(
            message=message,
            error_code=ErrorCode.VALIDATION_ERROR,
            details=details,
            http_status=400,
            **kwargs
        )
        self.field = field
        self.value = value
        self.constraints = constraints or []


class AIServiceException(BaseServiceException):
    """AI服务异常基类"""
    
    def __init__(self, message: str, **kwargs):
        kwargs.setdefault("http_status", 503)
        super().__init__(message, **kwargs)


class EmbeddingServiceException(AIServiceException):
    """向量化服务异常"""
    
    def __init__(
        self,
        message: str,
        service_url: Optional[str] = None,
        dimension: Optional[int] = None,
        **kwargs
    ):
        details = kwargs.pop("details", {})
        if service_url:
            details["service_url"] = service_url
        if dimension:
            details["dimension"] = dimension
        
        super().__init__(
            message=message,
            error_code=ErrorCode.EMBEDDING_GENERATION_FAILED,
            details=details,
            **kwargs
        )


class LLMServiceException(AIServiceException):
    """大语言模型服务异常"""
    
    def __init__(
        self,
        message: str,
        model: Optional[str] = None,
        provider: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.pop("details", {})
        if model:
            details["model"] = model
        if provider:
            details["provider"] = provider
        
        super().__init__(
            message=message,
            error_code=ErrorCode.LLM_REQUEST_FAILED,
            details=details,
            **kwargs
        )


class KnowledgeExtractionException(AIServiceException):
    """知识提取异常"""
    
    def __init__(
        self,
        message: str,
        content_length: Optional[int] = None,
        extraction_type: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.pop("details", {})
        if content_length:
            details["content_length"] = content_length
        if extraction_type:
            details["extraction_type"] = extraction_type
        
        super().__init__(
            message=message,
            error_code=ErrorCode.KNOWLEDGE_EXTRACTION_FAILED,
            details=details,
            **kwargs
        )


class DatabaseException(BaseServiceException):
    """图数据库异常基类"""
    
    def __init__(self, message: str, **kwargs):
        kwargs.setdefault("http_status", 500)
        super().__init__(message, **kwargs)


class DatabaseConnectionException(DatabaseException):
    """数据库连接异常"""
    
    def __init__(
        self,
        message: str,
        host: Optional[str] = None,
        port: Optional[int] = None,
        database: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.pop("details", {})
        if host:
            details["host"] = host
        if port:
            details["port"] = port
        if database:
            details["database"] = database
        
        super().__init__(
            message=message,
            error_code=ErrorCode.DATABASE_CONNECTION_FAILED,
            details=details,
            **kwargs
        )


class QueryExecutionException(DatabaseException):
    """查询执行异常"""
    
    def __init__(
        self,
        message: str,
        query: Optional[str] = None,
        parameters: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        details = kwargs.pop("details", {})
        if query:
            details["query"] = query
        if parameters:
            details["parameters"] = parameters
        
        super().__init__(
            message=message,
            error_code=ErrorCode.QUERY_EXECUTION_FAILED,
            details=details,
            **kwargs
        )


class NodeNotFoundException(DatabaseException):
    """节点未找到异常"""
    
    def __init__(
        self,
        message: str,
        node_id: Optional[str] = None,
        node_type: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.pop("details", {})
        if node_id:
            details["node_id"] = node_id
        if node_type:
            details["node_type"] = node_type
        
        super().__init__(
            message=message,
            error_code=ErrorCode.NODE_NOT_FOUND,
            details=details,
            http_status=404,
            **kwargs
        )


class SearchException(BaseServiceException):
    """搜索异常基类"""
    
    def __init__(self, message: str, **kwargs):
        kwargs.setdefault("error_code", ErrorCode.SEARCH_FAILED)
        kwargs.setdefault("http_status", 400)
        super().__init__(message, **kwargs)


class InvalidQueryException(SearchException):
    """无效查询异常"""
    
    def __init__(
        self,
        message: str,
        query: Optional[str] = None,
        query_type: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.pop("details", {})
        if query:
            details["query"] = query
        if query_type:
            details["query_type"] = query_type
        
        super().__init__(
            message=message,
            error_code=ErrorCode.INVALID_QUERY,
            details=details,
            **kwargs
        )


class NoResultsFoundException(SearchException):
    """无搜索结果异常"""
    
    def __init__(
        self,
        message: str,
        query: Optional[str] = None,
        search_mode: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.pop("details", {})
        if query:
            details["query"] = query
        if search_mode:
            details["search_mode"] = search_mode
        
        super().__init__(
            message=message,
            error_code=ErrorCode.NO_RESULTS_FOUND,
            details=details,
            http_status=404,
            **kwargs
        )


class NodeOperationError(DatabaseException):
    """节点操作异常"""
    
    def __init__(
        self,
        message: str,
        node_id: Optional[str] = None,
        node_type: Optional[str] = None,
        operation: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.pop("details", {})
        if node_id:
            details["node_id"] = node_id
        if node_type:
            details["node_type"] = node_type
        if operation:
            details["operation"] = operation
        
        super().__init__(
            message=message,
            error_code=ErrorCode.QUERY_EXECUTION_FAILED,
            details=details,
            **kwargs
        )


class ExtractionError(AIServiceException):
    """知识提取异常基类"""
    
    def __init__(self, message: str, **kwargs):
        kwargs.setdefault("error_code", ErrorCode.KNOWLEDGE_EXTRACTION_FAILED)
        super().__init__(message, **kwargs)


class EntityExtractionError(ExtractionError):
    """实体提取异常"""
    pass


class StatementExtractionError(ExtractionError):
    """陈述提取异常"""
    pass


class ConfigurationException(BaseServiceException):
    """配置异常"""
    
    def __init__(
        self,
        message: str,
        config_key: Optional[str] = None,
        config_value: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.pop("details", {})
        if config_key:
            details["config_key"] = config_key
        if config_value:
            details["config_value"] = config_value
        
        super().__init__(
            message=message,
            error_code=ErrorCode.CONFIGURATION_ERROR,
            details=details,
            http_status=500,
            **kwargs
        )


# 异常处理装饰器
def handle_exceptions(
    default_exception: type = BaseServiceException,
    log_errors: bool = True
):
    """
    异常处理装饰器
    
    Args:
        default_exception: 默认异常类型
        log_errors: 是否记录错误日志
    
    Returns:
        装饰器函数
    
    Example:
        @handle_exceptions(AIServiceException)
        async def extract_knowledge(content: str):
            # 可能抛出异常的代码
            pass
    """
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except BaseServiceException:
                # 已经是自定义异常，直接抛出
                raise
            except Exception as e:
                if log_errors:
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.error(f"Unexpected error in {func.__name__}: {e}", exc_info=True)
                
                raise default_exception(
                    message=f"Unexpected error in {func.__name__}: {str(e)}",
                    cause=e
                )
        
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except BaseServiceException:
                # 已经是自定义异常，直接抛出
                raise
            except Exception as e:
                if log_errors:
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.error(f"Unexpected error in {func.__name__}: {e}", exc_info=True)
                
                raise default_exception(
                    message=f"Unexpected error in {func.__name__}: {str(e)}",
                    cause=e
                )
        
        # 检查是否为异步函数
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# 使用示例和测试代码
if __name__ == "__main__":
    # 基础异常使用示例
    try:
        raise ValidationError(
            "用户名长度不符合要求",
            field="username",
            value="ab",
            constraints=["长度必须在3-50之间", "只能包含字母、数字和下划线"]
        )
    except ValidationError as e:
        print("验证异常示例:", e.to_dict())
    
    # AI服务异常示例
    try:
        raise EmbeddingServiceException(
            "向量化服务连接失败",
            service_url="http://localhost:8004",
            dimension=1024,
            cause=ConnectionError("Connection refused")
        )
    except EmbeddingServiceException as e:
        print("AI服务异常示例:", e.to_dict())
    
    # 数据库异常示例
    try:
        raise QueryExecutionException(
            "Cypher查询执行失败",
            query="MATCH (n:Entity) WHERE n.id = $id RETURN n",
            parameters={"id": "non-existent-id"}
        )
    except QueryExecutionException as e:
        print("数据库异常示例:", e.to_dict())