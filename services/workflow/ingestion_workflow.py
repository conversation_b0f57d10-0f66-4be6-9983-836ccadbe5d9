"""
智能记忆引擎 - 数据摄入业务工作流服务

高层业务工作流协调服务，整合AI服务和图谱服务：
- 统一的数据摄入入口点，协调复杂的业务流程
- 支持事务管理、错误恢复、重试机制
- 完整的工作流状态跟踪和进度监控
- 批处理支持、异步处理、性能优化
- 保持与现有app.py中/api/ingest端点的兼容性

作者: CORE Team
版本: v2.0
创建时间: 2025-08-29 16:32:15
"""

import asyncio
import logging
import time
import uuid
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime, timezone
from enum import Enum
from dataclasses import dataclass, field

from ..core.base_service import BaseService, ServiceHealthCheck, ServiceStatus
from ..ai.orchestrator import AIServiceOrchestrator, get_ai_orchestrator
from ..graph.knowledge_service import KnowledgeGraphOrchestrator, get_knowledge_graph_orchestrator
from ..utils.exceptions import (
    BaseServiceException, ValidationError,
    KnowledgeExtractionException, DatabaseException, ErrorCode
)
# 验证器类暂时使用简单实现
class ContentValidator:
    def validate_content(self, content, min_length=1, max_length=1000000, allowed_formats=None):
        if len(content) < min_length:
            raise ValidationError(f"内容长度不能少于{min_length}字符")
        if len(content) > max_length:
            raise ValidationError(f"内容长度不能超过{max_length}字符")
        return True

class MetadataValidator:
    def validate_metadata(self, metadata):
        if not isinstance(metadata, dict):
            raise ValidationError("元数据必须是字典类型")
        return True
from models import (
    Episode, Entity, Statement, ContentInput, ProcessingResult,
    ContentSource, ProcessingStatus
)


class WorkflowStage(str, Enum):
    """工作流阶段枚举"""
    VALIDATION = "validation"           # 输入验证
    PREPROCESSING = "preprocessing"     # 内容预处理
    AI_EXTRACTION = "ai_extraction"     # AI知识提取
    DATA_CONSTRUCTION = "data_construction"  # 数据构建
    GRAPH_STORAGE = "graph_storage"     # 图谱存储
    POST_PROCESSING = "post_processing" # 后处理优化
    COMPLETED = "completed"             # 完成
    FAILED = "failed"                   # 失败


class WorkflowMode(str, Enum):
    """工作流模式枚举"""
    QUICK = "quick"           # 快速模式：优化性能
    COMPLETE = "complete"     # 完整模式：最高质量
    BATCH = "batch"           # 批处理模式：处理多个项目
    STREAMING = "streaming"   # 流式处理：实时处理


@dataclass
class WorkflowProgress:
    """工作流进度状态"""
    workflow_id: str
    current_stage: WorkflowStage
    progress_percentage: float = 0.0
    stage_details: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    start_time: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    last_updated: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "workflow_id": self.workflow_id,
            "current_stage": self.current_stage.value,
            "progress_percentage": self.progress_percentage,
            "stage_details": self.stage_details,
            "error_message": self.error_message,
            "start_time": self.start_time.isoformat(),
            "last_updated": self.last_updated.isoformat(),
            "elapsed_time_seconds": (datetime.now(timezone.utc) - self.start_time).total_seconds()
        }


@dataclass
class WorkflowConfig:
    """工作流配置"""
    mode: WorkflowMode = WorkflowMode.COMPLETE
    enable_validation: bool = True
    enable_preprocessing: bool = True
    enable_ai_extraction: bool = True
    enable_post_processing: bool = True
    enable_transaction: bool = True
    enable_retry: bool = True
    max_retries: int = 3
    retry_delay: float = 1.0
    timeout_seconds: float = 300.0  # 5分钟
    batch_size: int = 10
    parallel_workers: int = 4
    progress_callback: Optional[Callable[[WorkflowProgress], None]] = None


@dataclass
class WorkflowResult:
    """工作流执行结果"""
    workflow_id: str
    success: bool
    processing_result: Optional[ProcessingResult] = None
    episode_id: Optional[str] = None
    entities_count: int = 0
    statements_count: int = 0
    processing_time_seconds: float = 0.0
    stage_timings: Dict[str, float] = field(default_factory=dict)
    error_details: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "workflow_id": self.workflow_id,
            "success": self.success,
            "processing_time_seconds": self.processing_time_seconds,
            "stage_timings": self.stage_timings,
            "entities_count": self.entities_count,
            "statements_count": self.statements_count,
            "metadata": self.metadata
        }
        
        if self.processing_result:
            result["processing_result"] = self.processing_result.model_dump()
        
        if self.episode_id:
            result["episode_id"] = self.episode_id
            
        if self.error_details:
            result["error_details"] = self.error_details
            
        return result


class IngestionWorkflowService(BaseService):
    """
    数据摄入业务工作流服务
    
    核心功能：
    1. 统一的数据摄入业务流程协调
    2. AI服务和图谱服务的集成管理
    3. 完整的事务管理和错误恢复
    4. 工作流进度跟踪和状态监控
    5. 批处理和异步处理支持
    6. 性能监控和优化
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, logger: Optional[logging.Logger] = None):
        super().__init__(
            service_name="ingestion_workflow_service",
            config=config or {},
            logger=logger or logging.getLogger("smart_memory.workflow")
        )
        
        # 核心服务依赖
        self.ai_orchestrator: Optional[AIServiceOrchestrator] = None
        self.knowledge_orchestrator: Optional[KnowledgeGraphOrchestrator] = None
        
        # 验证器
        self.content_validator: Optional[ContentValidator] = None
        self.metadata_validator: Optional[MetadataValidator] = None
        
        # 工作流状态跟踪
        self.active_workflows: Dict[str, WorkflowProgress] = {}
        self.workflow_history: List[WorkflowResult] = []
        
        # 性能指标
        self.workflow_metrics = {
            "total_workflows": 0,
            "successful_workflows": 0,
            "failed_workflows": 0,
            "average_processing_time": 0.0,
            "total_entities_processed": 0,
            "total_statements_processed": 0
        }
        
        # 配置参数
        self.default_workflow_config = WorkflowConfig(
            mode=WorkflowMode(self.config.get("default_mode", "complete")),
            enable_validation=self.config.get("enable_validation", True),
            enable_preprocessing=self.config.get("enable_preprocessing", True),
            enable_ai_extraction=self.config.get("enable_ai_extraction", True),
            enable_post_processing=self.config.get("enable_post_processing", True),
            enable_transaction=self.config.get("enable_transaction", True),
            enable_retry=self.config.get("enable_retry", True),
            max_retries=self.config.get("max_retries", 3),
            retry_delay=self.config.get("retry_delay", 1.0),
            timeout_seconds=self.config.get("timeout_seconds", 300.0),
            batch_size=self.config.get("batch_size", 10),
            parallel_workers=self.config.get("parallel_workers", 4)
        )
        
        # 并发控制
        self._workflow_semaphore = asyncio.Semaphore(self.default_workflow_config.parallel_workers)
        self._progress_lock = asyncio.Lock()
    
    async def _initialize_service(self) -> None:
        """初始化工作流服务的所有依赖"""
        try:
            self.logger.info("开始初始化数据摄入工作流服务...")
            
            # 1. 初始化AI服务协调器
            await self._initialize_ai_orchestrator()
            
            # 2. 初始化知识图谱协调器
            await self._initialize_knowledge_orchestrator()
            
            # 3. 初始化验证器
            await self._initialize_validators()
            
            # 4. 验证服务依赖
            await self._validate_service_dependencies()
            
            self.logger.info("✅ 数据摄入工作流服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"数据摄入工作流服务初始化失败: {e}")
            raise
    
    async def _initialize_ai_orchestrator(self) -> None:
        """初始化AI服务协调器"""
        try:
            self.logger.info("初始化AI服务协调器...")
            
            try:
                # 获取AI服务协调器实例
                self.ai_orchestrator = get_ai_orchestrator()
                
                # 确保AI服务已初始化
                if not self.ai_orchestrator.is_ready:
                    await self.ai_orchestrator.initialize()
                
                # 验证AI服务健康状态
                health_check = await self.ai_orchestrator.health_check()
                if not health_check.is_healthy():
                    self.logger.warning(f"AI服务不健康但继续运行: {health_check.message}")
            except ImportError:
                self.logger.error("AI服务协调器模块未找到，请确保已完成服务重构")
                raise
            
            self.logger.info("✅ AI服务协调器初始化成功")
            
        except Exception as e:
            self.logger.error(f"AI服务协调器初始化失败: {e}")
            raise
    
    async def _initialize_knowledge_orchestrator(self) -> None:
        """初始化知识图谱协调器"""
        try:
            self.logger.info("初始化知识图谱协调器...")
            
            try:
                # 获取知识图谱服务协调器实例
                self.knowledge_orchestrator = get_knowledge_graph_orchestrator()
                
                # 确保图谱服务已初始化
                if not self.knowledge_orchestrator.is_ready:
                    await self.knowledge_orchestrator.initialize()
                
                # 验证图谱服务健康状态
                health_check = await self.knowledge_orchestrator.health_check()
                if not health_check.is_healthy():
                    self.logger.warning(f"图谱服务不健康但继续运行: {health_check.message}")
            except ImportError:
                self.logger.error("知识图谱协调器模块未找到，请确保已完成服务重构")
                raise
            
            self.logger.info("✅ 知识图谱协调器初始化成功")
            
        except Exception as e:
            self.logger.error(f"知识图谱协调器初始化失败: {e}")
            raise
    
    async def _initialize_validators(self) -> None:
        """初始化验证器"""
        try:
            self.logger.info("初始化验证器...")
            
            # 创建验证器实例
            self.content_validator = ContentValidator()
            self.metadata_validator = MetadataValidator()
            
            self.logger.info("✅ 验证器初始化成功")
            
        except Exception as e:
            self.logger.error(f"验证器初始化失败: {e}")
            raise
    
    async def _validate_service_dependencies(self) -> None:
        """验证服务依赖关系"""
        try:
            self.logger.info("验证服务依赖关系...")
            
            # 验证必需服务
            if not self.ai_orchestrator:
                raise Exception("AI服务协调器未初始化")
            
            if not self.knowledge_orchestrator:
                raise Exception("知识图谱协调器未初始化")
            
            if not self.content_validator or not self.metadata_validator:
                raise Exception("验证器未初始化")
            
            self.logger.info("✅ 服务依赖验证通过")
            
        except Exception as e:
            self.logger.error(f"服务依赖验证失败: {e}")
            raise
    
    async def _cleanup_service(self) -> None:
        """清理工作流服务资源"""
        self.logger.info("开始清理数据摄入工作流服务...")
        
        # 等待所有活跃的工作流完成
        if self.active_workflows:
            self.logger.info(f"等待 {len(self.active_workflows)} 个活跃工作流完成...")
            
            # 等待最多30秒
            max_wait_time = 30
            wait_interval = 1
            waited_time = 0
            
            while self.active_workflows and waited_time < max_wait_time:
                await asyncio.sleep(wait_interval)
                waited_time += wait_interval
            
            if self.active_workflows:
                self.logger.warning(f"仍有 {len(self.active_workflows)} 个工作流未完成，强制清理")
        
        # 清理服务引用
        self.ai_orchestrator = None
        self.knowledge_orchestrator = None
        self.content_validator = None
        self.metadata_validator = None
        
        # 重置状态
        self.active_workflows.clear()
        self.workflow_history.clear()
        
        self.logger.info("✅ 数据摄入工作流服务清理完成")
    
    async def _perform_health_check(self) -> ServiceHealthCheck:
        """执行健康检查"""
        try:
            start_time = time.time()
            details = {}
            all_healthy = True
            messages = []
            
            # 检查AI服务协调器
            if self.ai_orchestrator:
                try:
                    ai_health = await self.ai_orchestrator.health_check()
                    details["ai_orchestrator"] = ai_health.to_dict()
                    if not ai_health.is_healthy():
                        all_healthy = False
                        messages.append(f"AI服务不健康: {ai_health.message}")
                except Exception as e:
                    details["ai_orchestrator"] = {"status": "error", "error": str(e)}
                    all_healthy = False
                    messages.append(f"AI服务检查失败: {e}")
            else:
                details["ai_orchestrator"] = {"status": "not_initialized"}
                all_healthy = False
                messages.append("AI服务协调器未初始化")
            
            # 检查知识图谱协调器
            if self.knowledge_orchestrator:
                try:
                    knowledge_health = await self.knowledge_orchestrator.health_check()
                    details["knowledge_orchestrator"] = knowledge_health.to_dict()
                    if not knowledge_health.is_healthy():
                        all_healthy = False
                        messages.append(f"知识图谱服务不健康: {knowledge_health.message}")
                except Exception as e:
                    details["knowledge_orchestrator"] = {"status": "error", "error": str(e)}
                    all_healthy = False
                    messages.append(f"知识图谱服务检查失败: {e}")
            else:
                details["knowledge_orchestrator"] = {"status": "not_initialized"}
                all_healthy = False
                messages.append("知识图谱协调器未初始化")
            
            # 工作流状态统计
            workflow_stats = {
                "active_workflows": len(self.active_workflows),
                "completed_workflows": len(self.workflow_history),
                "success_rate": (
                    self.workflow_metrics["successful_workflows"] / 
                    max(self.workflow_metrics["total_workflows"], 1)
                ) * 100,
                "average_processing_time": self.workflow_metrics["average_processing_time"]
            }
            details["workflow_stats"] = workflow_stats
            
            # 确定整体状态
            if all_healthy:
                status = ServiceStatus.READY
                message = "工作流服务运行正常"
            else:
                # 检查是否有基本功能可用
                if self.ai_orchestrator and self.knowledge_orchestrator:
                    status = ServiceStatus.READY  # 降级但可用
                    message = f"服务降级运行: {'; '.join(messages)}"
                else:
                    status = ServiceStatus.ERROR
                    message = f"服务不可用: {'; '.join(messages)}"
            
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=status,
                message=message,
                details=details,
                response_time=time.time() - start_time
            )
            
        except Exception as e:
            self.logger.error(f"工作流服务健康检查失败: {e}")
            return ServiceHealthCheck(
                service_name=self.service_name,
                status=ServiceStatus.ERROR,
                message=f"健康检查异常: {str(e)}",
                response_time=time.time() - start_time
            )
    
    # ================== 核心工作流接口 ==================
    
    async def ingest_content(
        self,
        content_input: ContentInput,
        workflow_config: Optional[WorkflowConfig] = None
    ) -> WorkflowResult:
        """
        核心数据摄入工作流入口
        
        执行完整的数据摄入业务流程：
        1. 输入验证和预处理
        2. AI知识提取
        3. 数据构建和转换
        4. 图谱存储
        5. 后处理优化
        
        Args:
            content_input: 内容输入数据
            workflow_config: 工作流配置（可选）
            
        Returns:
            WorkflowResult: 工作流执行结果
            
        Raises:
            BaseServiceException: 工作流执行失败时
        """
        # 使用默认配置
        config = workflow_config or self.default_workflow_config
        workflow_id = str(uuid.uuid4())
        
        # 初始化工作流进度
        progress = WorkflowProgress(
            workflow_id=workflow_id,
            current_stage=WorkflowStage.VALIDATION
        )
        
        start_time = time.time()
        result = WorkflowResult(workflow_id=workflow_id, success=False)
        
        try:
            async with self._workflow_semaphore:  # 并发控制
                await self._register_workflow(progress)
                
                # 设置超时
                async with asyncio.timeout(config.timeout_seconds):
                    result = await self._execute_workflow_stages(
                        content_input, config, progress, result
                    )
                
                result.success = True
                result.processing_time_seconds = time.time() - start_time
                
                # 更新性能指标
                await self._update_workflow_metrics(result)
                
                self.logger.info(
                    f"工作流 {workflow_id} 完成 - "
                    f"实体: {result.entities_count}, 陈述: {result.statements_count}, "
                    f"耗时: {result.processing_time_seconds:.2f}s"
                )
                
                return result
                
        except Exception as e:
            result.success = False
            result.processing_time_seconds = time.time() - start_time
            result.error_details = {
                "error_type": type(e).__name__,
                "error_message": str(e),
                "stage": progress.current_stage.value
            }
            
            await self._handle_workflow_error(progress, e, config)
            await self._update_workflow_metrics(result)
            
            self.logger.error(f"工作流 {workflow_id} 失败: {e}")
            
            # 转换为适当的异常类型
            if isinstance(e, BaseServiceException):
                raise
            else:
                raise BaseServiceException(
                    message=f"工作流执行失败: {str(e)}",
                    error_code=ErrorCode.UNKNOWN_ERROR,
                    context={"workflow_id": workflow_id, "stage": progress.current_stage.value},
                    cause=e
                )
                
        finally:
            await self._unregister_workflow(workflow_id)
    
    async def _execute_workflow_stages(
        self,
        content_input: ContentInput,
        config: WorkflowConfig,
        progress: WorkflowProgress,
        result: WorkflowResult
    ) -> WorkflowResult:
        """执行工作流各个阶段"""
        stage_start_time = time.time()
        
        try:
            # 阶段1: 输入验证
            if config.enable_validation:
                await self._update_progress(progress, WorkflowStage.VALIDATION, 10.0)
                stage_start = time.time()
                
                validated_input = await self._validate_input(content_input)
                
                result.stage_timings[WorkflowStage.VALIDATION.value] = time.time() - stage_start
                self.logger.debug(f"工作流 {progress.workflow_id} - 验证阶段完成")
            else:
                validated_input = content_input
            
            # 阶段2: 内容预处理
            if config.enable_preprocessing:
                await self._update_progress(progress, WorkflowStage.PREPROCESSING, 20.0)
                stage_start = time.time()
                
                preprocessed_content = await self._preprocess_content(validated_input)
                
                result.stage_timings[WorkflowStage.PREPROCESSING.value] = time.time() - stage_start
                self.logger.debug(f"工作流 {progress.workflow_id} - 预处理阶段完成")
            else:
                preprocessed_content = validated_input.content
            
            # 阶段3: AI知识提取
            if config.enable_ai_extraction:
                await self._update_progress(progress, WorkflowStage.AI_EXTRACTION, 50.0)
                stage_start = time.time()
                
                extraction_result = await self._extract_knowledge(
                    preprocessed_content, validated_input, config
                )
                
                result.stage_timings[WorkflowStage.AI_EXTRACTION.value] = time.time() - stage_start
                self.logger.debug(f"工作流 {progress.workflow_id} - AI提取阶段完成")
            else:
                raise ValidationError("AI知识提取是必需的阶段")
            
            # 阶段4: 数据构建
            await self._update_progress(progress, WorkflowStage.DATA_CONSTRUCTION, 70.0)
            stage_start = time.time()
            
            episode, entities, statements = await self._construct_graph_data(
                extraction_result, validated_input
            )
            
            result.stage_timings[WorkflowStage.DATA_CONSTRUCTION.value] = time.time() - stage_start
            result.entities_count = len(entities)
            result.statements_count = len(statements)
            self.logger.debug(f"工作流 {progress.workflow_id} - 数据构建阶段完成")
            
            # 阶段5: 图谱存储
            await self._update_progress(progress, WorkflowStage.GRAPH_STORAGE, 85.0)
            stage_start = time.time()
            
            if config.enable_transaction:
                episode_id = await self._store_with_transaction(episode, entities, statements)
            else:
                episode_id = await self._store_without_transaction(episode, entities, statements)
            
            result.episode_id = episode_id
            result.stage_timings[WorkflowStage.GRAPH_STORAGE.value] = time.time() - stage_start
            self.logger.debug(f"工作流 {progress.workflow_id} - 存储阶段完成")
            
            # 阶段6: 后处理优化
            if config.enable_post_processing:
                await self._update_progress(progress, WorkflowStage.POST_PROCESSING, 95.0)
                stage_start = time.time()
                
                await self._post_process_episode(episode_id, config)
                
                result.stage_timings[WorkflowStage.POST_PROCESSING.value] = time.time() - stage_start
                self.logger.debug(f"工作流 {progress.workflow_id} - 后处理阶段完成")
            
            # 完成
            await self._update_progress(progress, WorkflowStage.COMPLETED, 100.0)
            
            # 构建处理结果
            result.processing_result = ProcessingResult(
                episode_id=episode_id,
                entities_extracted=len(entities),
                statements_created=len(statements),  # 使用正确的字段名
                processing_time_ms=int((time.time() - stage_start_time) * 1000),  # 转换为毫秒
                confidence_scores=[],  # 简化
                status=ProcessingStatus.COMPLETED
            )
            
            return result
            
        except Exception as e:
            await self._update_progress(progress, WorkflowStage.FAILED, progress.progress_percentage, str(e))
            raise
    
    async def _validate_input(self, content_input: ContentInput) -> ContentInput:
        """验证输入数据"""
        try:
            # 内容验证
            if self.content_validator:
                self.content_validator.validate_content(
                    content_input.content,
                    min_length=1,
                    max_length=1000000,  # 1MB
                    allowed_formats=['text/plain', 'text/markdown']
                )
            
            # 元数据验证
            if self.metadata_validator:
                self.metadata_validator.validate_metadata(content_input.metadata)
            
            # 业务规则验证
            if not content_input.content.strip():
                raise ValidationError("内容不能为空", field="content")
            
            if len(content_input.content) > 500000:  # 500KB
                self.logger.warning(f"内容较长 ({len(content_input.content)} 字符)，可能影响处理性能")
            
            return content_input
            
        except Exception as e:
            raise ValidationError(f"输入验证失败: {str(e)}", cause=e)
    
    async def _preprocess_content(self, content_input: ContentInput) -> str:
        """预处理内容"""
        try:
            content = content_input.content.strip()
            
            # 清理和规范化
            content = content.replace('\r\n', '\n')
            content = content.replace('\r', '\n')
            
            # 移除过多的空行
            import re
            content = re.sub(r'\n{3,}', '\n\n', content)
            
            # 基本去重处理（移除重复的句子）
            sentences = content.split('\n')
            unique_sentences = []
            seen = set()
            
            for sentence in sentences:
                sentence = sentence.strip()
                if sentence and sentence not in seen:
                    unique_sentences.append(sentence)
                    seen.add(sentence)
            
            processed_content = '\n'.join(unique_sentences)
            
            return processed_content
            
        except Exception as e:
            self.logger.warning(f"内容预处理失败，使用原始内容: {e}")
            return content_input.content
    
    async def _extract_knowledge(
        self,
        content: str,
        content_input: ContentInput,
        config: WorkflowConfig
    ) -> Dict[str, Any]:
        """AI知识提取"""
        try:
            if not self.ai_orchestrator:
                raise KnowledgeExtractionException("AI服务不可用")
            
            # 构建提取上下文
            context = {
                "source": content_input.source.value if content_input.source else "manual",
                "metadata": content_input.metadata,
                "workflow_config": {
                    "mode": config.mode.value,
                    "enable_retry": config.enable_retry
                }
            }
            
            # 执行知识提取（带重试机制）
            if config.enable_retry:
                extraction_result = await self._execute_with_retry(
                    lambda: self.ai_orchestrator.extract_knowledge(content, context),
                    max_retries=config.max_retries,
                    delay=config.retry_delay
                )
            else:
                extraction_result = await self.ai_orchestrator.extract_knowledge(content, context)
            
            return extraction_result
            
        except Exception as e:
            raise KnowledgeExtractionException(
                f"AI知识提取失败: {str(e)}",
                content_length=len(content),
                extraction_type="full",
                cause=e
            )
    
    async def _construct_graph_data(
        self,
        extraction_result: Dict[str, Any],
        content_input: ContentInput
    ) -> tuple[Episode, List[Entity], List[Statement]]:
        """构建图谱数据结构"""
        try:
            # 确保source是ContentSource类型
            source = content_input.source
            if isinstance(source, str):
                try:
                    source = ContentSource(source)
                except ValueError:
                    source = ContentSource.MANUAL  # 默认值
            
            # 创建Episode
            episode = Episode(
                content=content_input.content,
                title=content_input.metadata.get("title", "")[:200],  # 限制长度
                source=source,
                metadata=content_input.metadata,
                embedding=extraction_result.get("content_embedding")
                # 移除status字段，使用默认值
            )
            
            # 转换实体数据
            entities = []
            for entity_data in extraction_result.get("entities", []):
                entity = Entity(
                    name=entity_data.get("name", ""),
                    type=entity_data.get("type", "Concept"),  # 使用正确的字段名
                    description=entity_data.get("description", ""),
                    confidence=entity_data.get("confidence", 0.5),
                    embedding=entity_data.get("embedding")
                    # 移除不存在的metadata字段
                )
                entities.append(entity)
            
            # 转换陈述数据
            statements = []
            for statement_data in extraction_result.get("statements", []):
                content = statement_data.get("content", "").strip()
                if not content:  # 跳过空内容的陈述
                    continue
                    
                statement = Statement(
                    content=content,
                    subject=statement_data.get("subject", ""),  # 使用正确的字段名
                    predicate=statement_data.get("predicate", ""),
                    object=statement_data.get("object", ""),  # 使用正确的字段名
                    confidence=statement_data.get("confidence", 0.5),
                    embedding=statement_data.get("embedding"),
                    source_episode=""  # 稍后会设置
                    # 移除不存在的metadata字段
                )
                statements.append(statement)
            
            return episode, entities, statements
            
        except Exception as e:
            raise BaseServiceException(
                f"数据构建失败: {str(e)}",
                error_code=ErrorCode.DATA_TYPE_ERROR,
                cause=e
            )
    
    async def _store_with_transaction(
        self,
        episode: Episode,
        entities: List[Entity],
        statements: List[Statement]
    ) -> str:
        """在事务中存储数据"""
        try:
            if not self.knowledge_orchestrator:
                raise DatabaseException("知识图谱服务不可用")
            
            # 使用事务存储
            result = await self.knowledge_orchestrator.create_episode_with_knowledge(
                episode, entities, statements
            )
            
            episode_id = result.get("episode_id")
            if not episode_id:
                raise DatabaseException("Episode创建失败，未返回ID")
            
            return episode_id
            
        except Exception as e:
            raise DatabaseException(
                f"事务存储失败: {str(e)}",
                error_code=ErrorCode.TRANSACTION_FAILED,
                cause=e
            )
    
    async def _store_without_transaction(
        self,
        episode: Episode,
        entities: List[Entity],
        statements: List[Statement]
    ) -> str:
        """不使用事务存储数据（逐步存储）"""
        try:
            if not self.knowledge_orchestrator:
                raise DatabaseException("知识图谱服务不可用")
            
            # 先创建Episode
            episode_id = await self.knowledge_orchestrator.create_episode(episode)
            
            # 创建实体
            entity_ids = []
            for entity in entities:
                entity_id = await self.knowledge_orchestrator.create_entity(entity)
                entity_ids.append(entity_id)
            
            # 创建陈述
            for statement in statements:
                await self.knowledge_orchestrator.create_statement(statement)
            
            return episode_id
            
        except Exception as e:
            raise DatabaseException(
                f"分步存储失败: {str(e)}",
                error_code=ErrorCode.NODE_NOT_FOUND,
                cause=e
            )
    
    async def _post_process_episode(self, episode_id: str, config: WorkflowConfig) -> None:
        """后处理Episode"""
        try:
            # 更新Episode状态为完成
            # 这里可以添加更多的后处理逻辑，如索引更新、缓存刷新等
            self.logger.debug(f"Episode {episode_id} 后处理完成")
            
        except Exception as e:
            self.logger.warning(f"Episode {episode_id} 后处理失败: {e}")
            # 后处理失败不影响主流程
    
    async def _register_workflow(self, progress: WorkflowProgress) -> None:
        """注册工作流"""
        async with self._progress_lock:
            self.active_workflows[progress.workflow_id] = progress
    
    async def _unregister_workflow(self, workflow_id: str) -> None:
        """注销工作流"""
        async with self._progress_lock:
            if workflow_id in self.active_workflows:
                self.active_workflows.pop(workflow_id)
                
                # 添加到历史记录  
                if len(self.workflow_history) >= 100:  # 保持最近100个记录
                    self.workflow_history.pop(0)
    
    async def _update_progress(
        self,
        progress: WorkflowProgress,
        stage: WorkflowStage,
        percentage: float,
        error_message: Optional[str] = None
    ) -> None:
        """更新工作流进度"""
        async with self._progress_lock:
            progress.current_stage = stage
            progress.progress_percentage = percentage
            progress.last_updated = datetime.now(timezone.utc)
            
            if error_message:
                progress.error_message = error_message
                
            # 调用进度回调
            if (hasattr(self.default_workflow_config, 'progress_callback') and 
                self.default_workflow_config.progress_callback):
                try:
                    self.default_workflow_config.progress_callback(progress)
                except Exception as e:
                    self.logger.warning(f"进度回调失败: {e}")
    
    async def _handle_workflow_error(
        self,
        progress: WorkflowProgress,
        error: Exception,
        config: WorkflowConfig
    ) -> None:
        """处理工作流错误"""
        try:
            # 记录错误
            self.logger.error(f"工作流 {progress.workflow_id} 在 {progress.current_stage.value} 阶段失败: {error}")
            
            # 更新进度状态
            await self._update_progress(progress, WorkflowStage.FAILED, progress.progress_percentage, str(error))
            
            # 可以添加错误通知、告警等逻辑
            
        except Exception as e:
            self.logger.error(f"错误处理失败: {e}")
    
    async def _execute_with_retry(
        self,
        func,
        max_retries: int = 3,
        delay: float = 1.0
    ) -> Any:
        """执行带重试机制的函数"""
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                return await func()
            except Exception as e:
                last_exception = e
                if attempt < max_retries:
                    self.logger.warning(f"执行失败，重试 {attempt + 1}/{max_retries}: {e}")
                    await asyncio.sleep(delay)
                else:
                    self.logger.error(f"执行失败，已达到最大重试次数: {e}")
        
        raise last_exception
    
    async def _update_workflow_metrics(self, result: WorkflowResult) -> None:
        """更新工作流性能指标"""
        try:
            self.workflow_metrics["total_workflows"] += 1
            
            if result.success:
                self.workflow_metrics["successful_workflows"] += 1
                self.workflow_metrics["total_entities_processed"] += result.entities_count
                self.workflow_metrics["total_statements_processed"] += result.statements_count
            else:
                self.workflow_metrics["failed_workflows"] += 1
            
            # 更新平均处理时间
            total = self.workflow_metrics["total_workflows"]
            current_avg = self.workflow_metrics["average_processing_time"]
            new_time = result.processing_time_seconds
            
            self.workflow_metrics["average_processing_time"] = (
                (current_avg * (total - 1) + new_time) / total
            )
            
        except Exception as e:
            self.logger.warning(f"更新性能指标失败: {e}")
    
    # ================== 状态查询接口 ==================
    
    async def get_workflow_progress(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """获取工作流进度"""
        async with self._progress_lock:
            progress = self.active_workflows.get(workflow_id)
            return progress.to_dict() if progress else None
    
    async def list_active_workflows(self) -> List[Dict[str, Any]]:
        """列出活跃的工作流"""
        async with self._progress_lock:
            return [progress.to_dict() for progress in self.active_workflows.values()]
    
    def get_workflow_metrics(self) -> Dict[str, Any]:
        """获取工作流性能指标"""
        return {
            **self.workflow_metrics,
            "active_workflows_count": len(self.active_workflows),
            "success_rate_percentage": (
                self.workflow_metrics["successful_workflows"] / 
                max(self.workflow_metrics["total_workflows"], 1)
            ) * 100
        }
    
    # ================== 批处理接口 ==================
    
    async def batch_ingest_content(
        self,
        content_inputs: List[ContentInput],
        workflow_config: Optional[WorkflowConfig] = None
    ) -> List[WorkflowResult]:
        """批量数据摄入"""
        if not content_inputs:
            return []
        
        config = workflow_config or self.default_workflow_config
        config.mode = WorkflowMode.BATCH  # 强制批处理模式
        
        results = []
        semaphore = asyncio.Semaphore(config.parallel_workers)
        
        async def process_single(content_input):
            async with semaphore:
                return await self.ingest_content(content_input, config)
        
        # 并行处理
        tasks = [process_single(content_input) for content_input in content_inputs]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                error_result = WorkflowResult(
                    workflow_id=str(uuid.uuid4()),
                    success=False,
                    error_details={
                        "error_type": type(result).__name__,
                        "error_message": str(result),
                        "index": i
                    }
                )
                processed_results.append(error_result)
            else:
                processed_results.append(result)
        
        return processed_results


# ================== 全局服务实例 ==================

_ingestion_workflow_service: Optional[IngestionWorkflowService] = None


def get_ingestion_workflow_service() -> IngestionWorkflowService:
    """
    获取数据摄入工作流服务实例（单例模式）
    
    Returns:
        IngestionWorkflowService: 工作流服务实例
    """
    global _ingestion_workflow_service
    if _ingestion_workflow_service is None:
        _ingestion_workflow_service = IngestionWorkflowService()
    return _ingestion_workflow_service


# ================== 便捷函数接口 ==================

async def ingest_content_workflow(
    content: str,
    source: ContentSource = ContentSource.MANUAL,
    metadata: Optional[Dict[str, Any]] = None,
    workflow_mode: WorkflowMode = WorkflowMode.COMPLETE
) -> WorkflowResult:
    """
    便捷函数：执行内容摄入工作流
    
    Args:
        content: 内容文本
        source: 内容来源
        metadata: 元数据
        workflow_mode: 工作流模式
        
    Returns:
        WorkflowResult: 工作流执行结果
    """
    service = get_ingestion_workflow_service()
    
    content_input = ContentInput(
        content=content,
        source=source,
        metadata=metadata or {}
    )
    
    config = WorkflowConfig(mode=workflow_mode)
    
    return await service.ingest_content(content_input, config)


async def batch_ingest_contents(
    contents: List[str],
    source: ContentSource = ContentSource.API,
    metadata: Optional[Dict[str, Any]] = None,
    parallel_workers: int = 4
) -> List[WorkflowResult]:
    """
    便捷函数：批量内容摄入
    
    Args:
        contents: 内容列表
        source: 内容来源
        metadata: 通用元数据
        parallel_workers: 并行工作线程数
        
    Returns:
        List[WorkflowResult]: 工作流结果列表
    """
    service = get_ingestion_workflow_service()
    
    content_inputs = [
        ContentInput(
            content=content,
            source=source,
            metadata={**(metadata or {}), "index": i}
        )
        for i, content in enumerate(contents)
    ]
    
    config = WorkflowConfig(
        mode=WorkflowMode.BATCH,
        parallel_workers=parallel_workers
    )
    
    return await service.batch_ingest_content(content_inputs, config)


# ================== 兼容性接口 ==================

# 为了保持与现有app.py的兼容性，提供便捷的转换函数
def workflow_result_to_processing_result(workflow_result: WorkflowResult) -> ProcessingResult:
    """将工作流结果转换为处理结果（兼容现有API）"""
    if workflow_result.processing_result:
        return workflow_result.processing_result
    
    # 构建基础的ProcessingResult
    return ProcessingResult(
        episode_id=workflow_result.episode_id or "",
        entities_extracted=workflow_result.entities_count,
        statements_created=workflow_result.statements_count,  # 修复字段名
        processing_time_ms=int(workflow_result.processing_time_seconds * 1000),  # 转换为毫秒
        confidence_scores=[],
        status=ProcessingStatus.COMPLETED if workflow_result.success else ProcessingStatus.FAILED
    )


if __name__ == "__main__":
    """数据摄入工作流服务测试"""
    
    async def test_ingestion_workflow():
        """测试数据摄入工作流功能"""
        print("🚀 开始测试数据摄入工作流服务...")
        
        try:
            # 获取服务实例
            service = get_ingestion_workflow_service()
            
            # 使用上下文管理器测试
            async with service.service_context():
                print("✅ 数据摄入工作流服务初始化成功")
                
                # 测试健康检查
                health_check = await service.health_check()
                print(f"🏥 服务健康状态: {health_check.status.value}")
                print(f"📝 健康检查详情: {health_check.message}")
                
                if health_check.is_healthy():
                    # 测试单个内容摄入
                    test_content = """
                    智能记忆引擎是一个基于知识图谱和向量检索的智能内容管理系统。
                    它由CORE团队开发，支持多种内容来源的统一处理和AI知识提取。
                    系统使用Neo4j作为图数据库，BGE-M3作为向量化模型，Gemini作为大语言模型。
                    """
                    
                    content_input = ContentInput(
                        content=test_content.strip(),
                        source=ContentSource.MANUAL,
                        metadata={"title": "测试文档", "category": "技术"}
                    )
                    
                    print("🔄 执行内容摄入工作流...")
                    result = await service.ingest_content(content_input)
                    
                    if result.success:
                        print(f"✅ 工作流执行成功:")
                        print(f"   - Episode ID: {result.episode_id}")
                        print(f"   - 实体数量: {result.entities_count}")
                        print(f"   - 陈述数量: {result.statements_count}")
                        print(f"   - 处理耗时: {result.processing_time_seconds:.2f}s")
                        print(f"   - 阶段耗时: {result.stage_timings}")
                    else:
                        print(f"❌ 工作流执行失败: {result.error_details}")
                    
                    # 显示性能指标
                    metrics = service.get_workflow_metrics()
                    print(f"📊 性能指标: {metrics}")
                    
                else:
                    print("⚠️ 服务不健康，跳过详细测试")
                
                print("🎉 数据摄入工作流服务测试完成！")
        
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 运行测试
    asyncio.run(test_ingestion_workflow())
