"""
智能记忆引擎 MVP v2.0 - AI服务模块

集成 BGE-M3 Embedding 服务和 OpenAI API 的统一AI服务接口。
提供文本向量化、知识提取、智能分析等AI核心功能。

BGE-M3特点：
- 支持多语言文本嵌入
- 1024维向量表示
- 高效的批量处理能力
- 优秀的语义相似性表现

作者: CORE Team
版本: v2.0
创建时间: 2025年08月28日
"""

import asyncio
import logging
import time
import json
import re
from typing import List, Dict, Any, Optional
from contextlib import asynccontextmanager

import jieba
import jieba.posseg as pseg
from config import settings
from .ai.embedding import get_bge_service, BGEEmbeddingService, BGEEmbeddingError
from .ai.llm import get_openai_service, OpenAIService, LLMError, LLMConnectionError, LLMContentError

# 配置日志记录器
logger = logging.getLogger("smart_memory.ai_service")


# BGE异常现在从embedding模块导入


class ServiceHealthError(Exception):
    """服务健康检查异常"""
    pass


# OpenAI异常类已移至LLM模块
# OpenAIClientError 现在由 LLMConnectionError 替代


class EntityExtractionError(Exception):
    """实体提取异常"""
    pass


class AIExtractionService:
    """
    AI 知识提取服务 - 集成 BGE-M3 Embedding 服务和 OpenAI API

    核心功能：
    1. BGE-M3文本向量化服务（单个/批量）
    2. 服务健康状态监控
    3. 自动重试和错误恢复
    4. 连接池管理和资源清理
    5. 为OpenAI集成预留接口
    """

    def __init__(self):
        """初始化AI服务客户端"""
        # BGE-M3向量服务（使用新的独立模块）
        self.embedding_service: Optional[BGEEmbeddingService] = None

        # 服务状态跟踪
        self._is_initialized = False
        self._last_health_check = 0
        self._health_check_interval = 300  # 5分钟
        self._service_available = False

        # LLM 服务（OpenAI/Gemini）
        self.llm_service: Optional[OpenAIService] = None
        
        # 实体提取配置
        self.entity_types = [
            "Person",      # 人名
            "Organization", # 组织机构
            "Location",    # 地点
            "Concept",     # 概念
            "Event",       # 事件
            "Product",     # 产品
            "Time"         # 时间
        ]

        logger.info("AI服务客户端初始化完成")

    async def initialize(self) -> bool:
        """
        初始化AI服务连接

        Returns:
            bool: 初始化是否成功

        Raises:
            ServiceHealthError: 当服务初始化失败时
        """
        if self._is_initialized:
            logger.info("AI服务已初始化，跳过重复初始化")
            return True

        try:
            logger.info("开始初始化AI服务连接...")

            # 初始化BGE-M3向量服务
            await self._initialize_embedding_service()

            # 执行服务健康检查
            await self._perform_health_check()

            # 初始化LLM服务
            await self._initialize_llm_service()

            self._is_initialized = True
            logger.info("✅ AI服务初始化成功")
            return True

        except Exception as e:
            logger.error(f"❌ AI服务初始化失败: {e}")
            await self.cleanup()
            raise ServiceHealthError(f"AI服务初始化失败: {e}")

    async def _initialize_embedding_service(self):
        """初始化BGE-M3向量服务"""
        try:
            # 获取BGE向量服务实例
            self.embedding_service = get_bge_service()
            
            # 初始化向量服务
            await self.embedding_service.initialize()
            
            logger.info("BGE-M3向量服务初始化完成")

        except Exception as e:
            logger.error(f"BGE-M3向量服务初始化失败: {e}")
            raise

    async def _initialize_llm_service(self):
        """初始化LLM服务"""
        try:
            # 获取LLM服务实例
            self.llm_service = get_openai_service()
            
            # 检查是否有API密钥配置
            openai_config = settings.get_openai_config()
            if not openai_config["api_key"]:
                logger.warning("OpenAI API密钥未配置，LLM功能将不可用")
                return
            
            # 初始化LLM服务
            await self.llm_service.initialize()
            
            logger.info(f"LLM服务初始化完成 - 模型: {openai_config['model']}")
            
        except Exception as e:
            logger.error(f"LLM服务初始化失败: {e}")
            # LLM服务初始化失败不应该影响BGE-M3服务
            logger.warning("继续使用BGE-M3服务和jieba fallback，LLM功能将不可用")
            self.llm_service = None

    async def _perform_health_check(self) -> bool:
        """
        执行服务健康检查

        Returns:
            bool: 服务是否健康

        Raises:
            ServiceHealthError: 当服务不可用时
        """
        current_time = time.time()

        # 检查是否需要进行健康检查（避免频繁检查）
        if (current_time - self._last_health_check) < self._health_check_interval and self._service_available:
            return True

        try:
            logger.info("执行BGE-M3服务健康检查...")

            # 使用BGE向量服务进行健康检查
            if self.embedding_service:
                is_healthy = await self.embedding_service.is_service_healthy()
                if is_healthy:
                    self._service_available = True
                    self._last_health_check = current_time
                    logger.info("✅ BGE-M3服务健康检查通过")
                    return True
                else:
                    raise ServiceHealthError("健康检查返回服务不可用")
            else:
                raise ServiceHealthError("BGE向量服务未初始化")

        except Exception as e:
            self._service_available = False
            error_msg = f"BGE-M3服务健康检查失败: {e}"
            logger.error(f"❌ {error_msg}")
            raise ServiceHealthError(error_msg)

    async def is_service_healthy(self) -> bool:
        """
        检查服务是否健康（公共接口）

        Returns:
            bool: 服务健康状态
        """
        try:
            return await self._perform_health_check()
        except ServiceHealthError:
            return False

    async def get_embedding(self, text: str) -> List[float]:
        """
        生成单个文本的向量表示

        Args:
            text: 输入文本

        Returns:
            List[float]: 1024维向量列表

        Raises:
            BGEEmbeddingError: 当向量生成失败时
            ValueError: 当输入参数无效时
        """
        if not text or not text.strip():
            raise ValueError("输入文本不能为空")

        if not self._is_initialized:
            await self.initialize()

        if not self.embedding_service:
            raise BGEEmbeddingError("BGE向量服务未初始化")

        try:
            logger.debug(f"生成单个文本向量 - 文本长度: {len(text)}")
            result = await self.embedding_service.get_embedding(text)
            logger.debug(f"向量生成成功 - 维度: {len(result)}")
            return result
        except Exception as e:
            error_msg = f"单个文本向量生成失败: {e}"
            logger.error(error_msg)
            raise BGEEmbeddingError(error_msg)

    async def get_batch_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        批量生成文本向量表示

        Args:
            texts: 文本列表

        Returns:
            List[List[float]]: 向量列表，每个向量包含1024个浮点数

        Raises:
            BGEEmbeddingError: 当批量向量生成失败时
            ValueError: 当输入参数无效时
        """
        if not texts:
            raise ValueError("文本列表不能为空")

        if not all(text and text.strip() for text in texts):
            raise ValueError("文本列表包含空文本")

        if not self._is_initialized:
            await self.initialize()
            
        if not self.embedding_service:
            raise BGEEmbeddingError("BGE向量服务未初始化")

        try:
            logger.debug(f"批量生成文本向量 - 文本数量: {len(texts)}")
            result = await self.embedding_service.get_batch_embeddings(texts)
            logger.debug(f"批量向量生成成功 - 向量数量: {len(result)}, 向量维度: {len(result[0]) if result else 0}")
            return result
        except Exception as e:
            error_msg = f"批量文本向量生成失败: {e}"
            logger.error(error_msg)
            raise BGEEmbeddingError(error_msg)

    # BGE-M3相关的内部方法已移至独立的BGE服务模块
    # _process_large_batch 和 _call_embedding_api 不再需要

    # ================== 实体提取核心功能 ==================
    
    async def extract_entities(self, content: str) -> List[Dict[str, Any]]:
        """
        提取文本中的实体信息
        
        Args:
            content: 输入文本内容
            
        Returns:
            List[Dict[str, Any]]: 提取的实体列表，每个实体包含类型、名称、描述和向量等信息
            
        Raises:
            EntityExtractionError: 当实体提取失败时
            ValueError: 当输入参数无效时
        """
        if not content or not content.strip():
            raise ValueError("输入内容不能为空")
            
        if len(content.strip()) < 10:
            logger.warning("输入内容过短，可能影响实体提取效果")
            
        if not self._is_initialized:
            await self.initialize()
            
        try:
            logger.debug(f"开始提取实体 - 内容长度: {len(content)}")
            
            entities = []
            
            # 使用 LLM 进行智能实体提取
            if self.llm_service and self.llm_service.is_ready:
                try:
                    entities = await self.llm_service.extract_entities(content)
                    logger.info(f"✅ LLM实体提取成功 - 提取到 {len(entities)} 个实体")
                except Exception as e:
                    logger.warning(f"⚠️ LLM实体提取失败，原因: {e}")
                    # 使用jieba fallback方法
                    logger.info("🔄 启用jieba fallback实体提取")
                    entities = await self._jieba_extract_entities(content)
            else:
                logger.warning("LLM服务未配置或不可用，使用jieba fallback实体提取")
                entities = await self._jieba_extract_entities(content)
                
            # 为每个实体生成向量
            if entities:
                entities = await self._generate_entity_embeddings(entities)
                
            logger.info(f"实体提取完成 - 总计提取 {len(entities)} 个实体")
            return entities
            
        except EntityExtractionError:
            raise
        except Exception as e:
            error_msg = f"实体提取失败: {e}"
            logger.error(error_msg)
            raise EntityExtractionError(error_msg)
    
    async def _llm_extract_entities(self, content: str) -> List[Dict[str, Any]]:
        """
        使用 LLM 提取实体（已弃用，直接使用LLM服务）
        
        Args:
            content: 输入文本内容
            
        Returns:
            List[Dict[str, Any]]: 提取的实体列表
            
        Raises:
            EntityExtractionError: 当实体提取失败时
        """
        if not self.llm_service or not self.llm_service.is_ready:
            raise EntityExtractionError("LLM服务未初始化或不可用")
            
        try:
            logger.debug("使用LLM服务进行实体提取...")
            
            # 直接使用LLM服务的实体提取功能
            entities = await self.llm_service.extract_entities(content)
            
            return entities
            
        except (LLMError, LLMConnectionError, LLMContentError) as e:
            error_msg = f"LLM实体提取失败: {e}"
            logger.error(error_msg)
            raise EntityExtractionError(error_msg)
        except Exception as e:
            error_msg = f"LLM实体提取失败: {e}"
            logger.error(error_msg)
            raise EntityExtractionError(error_msg)
    
    # 提示词构建方法已移至LLM模块的LLMPromptTemplates类
    # _build_system_prompt 和 _build_user_prompt 方法已弃用

    # OpenAI API调用方法已移至LLM服务模块
    # _call_openai_with_retry 方法已弃用，功能由LLM服务接管

    # JSON响应解析方法已移至LLM服务模块
    # _parse_entities_response 和 _clean_json_response 方法已弃用
    
    def _validate_and_clean_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        验证和清理提取的实体数据
        
        Args:
            entities: 原始实体列表
            
        Returns:
            List[Dict[str, Any]]: 验证和清理后的实体列表
        """
        valid_entities = []
        
        for i, entity in enumerate(entities):
            try:
                # 验证必需字段
                if not isinstance(entity, dict):
                    logger.warning(f"第 {i+1} 个实体不是对象，跳过")
                    continue
                    
                # 验证实体类型
                entity_type = entity.get("type", "").strip()
                if entity_type not in self.entity_types:
                    logger.warning(f"无效的实体类型: {entity_type}，跳过该实体")
                    continue
                    
                # 验证实体名称
                entity_name = entity.get("name", "").strip()
                if not entity_name or len(entity_name) < 2:
                    logger.warning(f"实体名称无效或过短: '{entity_name}'，跳过")
                    continue
                    
                # 设置默认值
                description = entity.get("description", "").strip()
                if not description:
                    description = f"{entity_type}类型的实体"
                    
                confidence = entity.get("confidence", 0.8)
                if not isinstance(confidence, (int, float)) or not (0.0 <= confidence <= 1.0):
                    confidence = 0.8
                    
                # 构建清理后的实体
                clean_entity = {
                    "type": entity_type,
                    "name": entity_name,
                    "description": description,
                    "confidence": float(confidence),
                    "source": "llm_extraction",  # 标记提取来源
                    "embedding": None  # 将在后续步骤中生成
                }
                
                valid_entities.append(clean_entity)
                
            except Exception as e:
                logger.warning(f"验证第 {i+1} 个实体时出错: {e}，跳过该实体")
                continue
        
        logger.debug(f"实体验证完成 - 原始: {len(entities)}, 有效: {len(valid_entities)}")
        return valid_entities
    
    async def _jieba_extract_entities(self, content: str) -> List[Dict[str, Any]]:
        """
        使用jieba分词进行实体提取的fallback方法
        
        当OpenAI API不可用时，使用基于jieba的词性标注进行基础实体提取
        
        Args:
            content: 输入文本内容
            
        Returns:
            List[Dict[str, Any]]: 提取的实体列表
        """
        try:
            logger.info("🔧 开始jieba fallback实体提取")
            
            # jieba词性标注到实体类型的映射
            pos_to_entity_type = {
                # 人名
                'nr': 'Person',      # 人名
                'nrfg': 'Person',    # 其他专名
                'nrt': 'Person',     # 敬语
                
                # 地点
                'ns': 'Location',    # 地名
                'nt': 'Location',    # 机构团体名
                
                # 组织机构
                'nt': 'Organization', # 机构团体名 (会与Location重复，后面处理)
                'nz': 'Organization', # 其他专名
                
                # 时间
                't': 'Time',         # 时间词
                'tg': 'Time',        # 时间词性语素
                
                # 概念/产品 (名词相关)
                'n': 'Concept',      # 名词
                'ng': 'Concept',     # 名词性语素
                'nw': 'Product',     # 作品名
                
                # 事件 (动词相关的概念)
                'vn': 'Event',       # 动名词
            }
            
            entities = []
            processed_terms = set()  # 避免重复提取
            
            # 使用jieba进行词性标注
            words = pseg.cut(content)
            
            for word, flag in words:
                # 过滤条件
                if (len(word.strip()) < 2 or  # 长度过短
                    word.strip() in processed_terms or  # 已处理
                    word.isdigit() or  # 纯数字
                    word in ['的', '是', '在', '和', '与', '了', '有', '将', '这', '那', '一个', '一家', '一种'] or  # 常见停用词
                    (not any(ord(char) > 127 for char in word) and len(word) < 3)):  # 英文且长度过短
                    continue
                
                # 检查词性是否匹配实体类型
                entity_type = pos_to_entity_type.get(flag)
                if not entity_type:
                    # 对于一些特殊情况的额外判断
                    if flag == 'nt':  # 机构团体名，需要判断是组织还是地点
                        # 简单的启发式规则：包含"公司"、"学院"等关键词的归为组织
                        if any(keyword in word for keyword in ['公司', '集团', '学院', '大学', '协会', '基金会']):
                            entity_type = 'Organization'
                        else:
                            entity_type = 'Location'
                    elif flag.startswith('n') and len(word) >= 3:  # 其他名词类
                        entity_type = 'Concept'
                    else:
                        continue
                
                # 生成置信度（jieba方法置信度相对较低）
                confidence = 0.6  # 基础置信度
                if flag in ['nr', 'ns', 'nt']:  # 专名类置信度较高
                    confidence = 0.75
                elif len(word) >= 4:  # 长词置信度稍高
                    confidence = 0.65
                
                # 生成实体描述
                description = self._generate_jieba_entity_description(word, flag, entity_type)
                
                entity = {
                    "name": word.strip(),
                    "type": entity_type,
                    "description": description,
                    "confidence": confidence,
                    "extraction_method": "jieba_fallback",
                    "pos_tag": flag,
                    "embedding": None,  # 待生成
                    "embedding_dim": 0
                }
                
                entities.append(entity)
                processed_terms.add(word.strip())
            
            # 去重并排序
            entities = self._deduplicate_jieba_entities(entities)
            
            logger.info(f"✅ jieba fallback提取完成 - 提取到 {len(entities)} 个实体")
            return entities
            
        except Exception as e:
            logger.error(f"❌ jieba fallback提取失败: {e}")
            return []
    
    def _generate_jieba_entity_description(self, word: str, pos_tag: str, entity_type: str) -> str:
        """为jieba提取的实体生成描述"""
        descriptions = {
            'Person': f"通过分词识别的人物名称：{word}",
            'Location': f"通过分词识别的地理位置或地点：{word}",
            'Organization': f"通过分词识别的组织机构：{word}",
            'Time': f"通过分词识别的时间表达：{word}",
            'Concept': f"通过分词识别的概念或事物：{word}",
            'Product': f"通过分词识别的产品或作品：{word}",
            'Event': f"通过分词识别的事件或活动：{word}"
        }
        return descriptions.get(entity_type, f"通过分词识别的实体：{word} (词性:{pos_tag})")
    
    def _deduplicate_jieba_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        对jieba提取的实体进行去重
        """
        seen = set()
        unique_entities = []
        
        # 按置信度降序排列
        entities = sorted(entities, key=lambda x: x['confidence'], reverse=True)
        
        for entity in entities:
            key = (entity['name'].lower(), entity['type'])
            if key not in seen:
                seen.add(key)
                unique_entities.append(entity)
        
        return unique_entities
    
    async def _generate_entity_embeddings(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        为每个实体生成BGE-M3向量
        
        Args:
            entities: 实体列表
            
        Returns:
            List[Dict[str, Any]]: 包含向量的实体列表
            
        Raises:
            BGEEmbeddingError: 向量生成失败时
        """
        if not entities:
            return entities
            
        try:
            logger.debug(f"开始为 {len(entities)} 个实体生成向量...")
            
            # 为每个实体构建文本表示
            entity_texts = []
            for entity in entities:
                # 组合实体名称和描述作为向量生成的输入
                entity_text = f"{entity['name']}: {entity['description']}"
                entity_texts.append(entity_text)
            
            # 批量生成向量
            embeddings = await self.get_batch_embeddings(entity_texts)
            
            # 将向量分配给相应的实体
            for i, entity in enumerate(entities):
                if i < len(embeddings):
                    entity["embedding"] = embeddings[i]
                    entity["embedding_dim"] = len(embeddings[i])
                else:
                    logger.warning(f"第 {i+1} 个实体未获得向量，使用空值")
                    entity["embedding"] = None
                    entity["embedding_dim"] = 0
            
            # 统计成功生成向量的实体数量
            successful_count = sum(1 for entity in entities if entity["embedding"] is not None)
            logger.info(f"向量生成完成 - 成功: {successful_count}/{len(entities)}")
            
            return entities
            
        except BGEEmbeddingError:
            logger.error("实体向量生成失败，保留无向量的实体")
            # 即使向量生成失败，也返回实体（只是没有向量）
            for entity in entities:
                if "embedding" not in entity:
                    entity["embedding"] = None
                    entity["embedding_dim"] = 0
            return entities
        except Exception as e:
            logger.error(f"实体向量生成过程出错: {e}")
            # 设置空向量
            for entity in entities:
                entity["embedding"] = None
                entity["embedding_dim"] = 0
            return entities

    async def _extract_statements(self, content: str, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        提取知识陈述 - 使用 LLM 进行智能提取
        
        Args:
            content: 文本内容
            entities: 已提取的实体列表
            
        Returns:
            List[Dict[str, Any]]: 知识陈述列表
        """
        statements = []
        
        if self.openai_client:
            try:
                # 使用 OpenAI 进行陈述提取
                statements = await self._llm_extract_statements(content, entities)
            except Exception as e:
                logger.warning(f"LLM陈述提取失败，启用jieba fallback: {e}")
                # 使用jieba fallback方法
                statements = await self._jieba_extract_statements(content, entities)
        else:
            logger.info("OpenAI客户端未配置，使用jieba fallback陈述提取")
            statements = await self._jieba_extract_statements(content, entities)
            
        return statements
    
    async def _llm_extract_statements(self, content: str, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        使用 LLM 提取知识陈述
        
        Args:
            content: 文本内容
            entities: 已提取的实体列表
            
        Returns:
            List[Dict[str, Any]]: 知识陈述列表
            
        Raises:
            OpenAIClientError: LLM调用失败
        """
        entity_names = [e["name"] for e in entities]
        
        # 构建智能提示词
        prompt = f"""请从以下文本中提取关键的知识陈述（事实），返回JSON格式：

文本: {content}

已识别实体: {', '.join(entity_names) if entity_names else '无'}

请提取重要的事实陈述，每个陈述应该包含：
- subject: 主语（优先使用已识别的实体）
- predicate: 谓语动词或关系
- object: 宾语
- fact: 完整的事实描述
- confidence: 置信度(0.0-1.0)

返回格式：
{{
  "statements": [
    {{
      "subject": "主语",
      "predicate": "关系/动作", 
      "object": "宾语",
      "fact": "完整事实描述",
      "confidence": 0.9
    }}
  ]
}}

只返回JSON，不要其他解释。"""
        
        try:
            logger.debug(f"正在使用LLM提取陈述，内容长度: {len(content)}")
            
            response = await self.openai_client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[{"role": "user", "content": prompt}],
                temperature=settings.OPENAI_TEMPERATURE,
                max_tokens=settings.OPENAI_MAX_TOKENS
            )
            
            result_text = response.choices[0].message.content
            logger.debug(f"LLM陈述提取响应: {result_text[:200]}...")
            
            # 清理和解析JSON
            clean_text = self._clean_json_response(result_text)
            result = json.loads(clean_text)
            
            # 验证和清理陈述数据
            raw_statements = result.get("statements", [])
            clean_statements = self._validate_and_clean_statements(raw_statements)
            
            # 为每个陈述生成向量
            statements_with_embeddings = await self._generate_statement_embeddings(clean_statements)
            
            logger.info(f"成功提取 {len(statements_with_embeddings)} 个知识陈述")
            return statements_with_embeddings
            
        except json.JSONDecodeError as e:
            error_msg = f"LLM陈述提取JSON解析失败: {e}"
            logger.error(error_msg)
            raise OpenAIClientError(error_msg)
        except Exception as e:
            error_msg = f"LLM陈述提取失败: {e}"
            logger.error(error_msg)
            raise OpenAIClientError(error_msg)
    
    def _validate_and_clean_statements(self, statements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        验证和清理提取的陈述数据
        
        Args:
            statements: 原始陈述列表
            
        Returns:
            List[Dict[str, Any]]: 验证和清理后的陈述列表
        """
        valid_statements = []
        
        for i, statement in enumerate(statements):
            try:
                # 验证必需字段
                if not isinstance(statement, dict):
                    logger.warning(f"第 {i+1} 个陈述不是对象，跳过")
                    continue
                    
                # 验证主要字段
                subject = statement.get("subject", "").strip()
                predicate = statement.get("predicate", "").strip()
                obj = statement.get("object", "").strip()
                fact = statement.get("fact", "").strip()
                
                if not all([subject, predicate, obj]):
                    logger.warning(f"陈述缺少必要字段 (主语/谓语/宾语)，跳过")
                    continue
                    
                # 使用fact作为完整描述，如果没有则组合三元组
                if not fact:
                    fact = f"{subject}{predicate}{obj}"
                    
                # 验证置信度
                confidence = statement.get("confidence", 0.8)
                if not isinstance(confidence, (int, float)) or not (0.0 <= confidence <= 1.0):
                    confidence = 0.8
                    
                # 构建清理后的陈述
                clean_statement = {
                    "subject": subject,
                    "predicate": predicate,
                    "object": obj,
                    "fact": fact,
                    "confidence": float(confidence),
                    "source": "llm_extraction",  # 标记提取来源
                    "embedding": None  # 将在后续步骤中生成
                }
                
                valid_statements.append(clean_statement)
                
            except Exception as e:
                logger.warning(f"验证第 {i+1} 个陈述时出错: {e}，跳过该陈述")
                continue
        
        logger.debug(f"陈述验证完成 - 原始: {len(statements)}, 有效: {len(valid_statements)}")
        return valid_statements
    
    async def _jieba_extract_statements(self, content: str, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        使用jieba进行知识陈述提取的fallback方法
        
        基于分词和语法规则构建基础的知识关系
        
        Args:
            content: 文本内容
            entities: 已提取的实体列表
            
        Returns:
            List[Dict[str, Any]]: 知识陈述列表
        """
        try:
            logger.info("🔧 开始jieba fallback陈述提取")
            
            if not entities:
                logger.warning("没有实体，无法构建陈述")
                return []
            
            statements = []
            entity_names = [e["name"] for e in entities]
            entity_dict = {e["name"]: e for e in entities}
            
            # 分句处理
            sentences = self._split_sentences(content)
            
            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) < 10:  # 句子过短跳过
                    continue
                
                # 找出句子中的实体
                found_entities = []
                for entity_name in entity_names:
                    if entity_name in sentence:
                        found_entities.append(entity_dict[entity_name])
                
                # 至少要有一个实体才构建陈述
                if not found_entities:
                    continue
                
                # 使用jieba进行词性标注
                words_with_pos = list(pseg.cut(sentence))
                
                # 提取动词和形容词作为关系
                relations = []
                for word, pos in words_with_pos:
                    if pos.startswith('v') or pos.startswith('a'):  # 动词或形容词
                        if len(word) >= 2:  # 长度过滤
                            relations.append(word)
                
                # 构建陈述
                if len(found_entities) >= 2:
                    # 多个实体：构建实体间关系陈述
                    for i in range(len(found_entities)):
                        for j in range(i + 1, len(found_entities)):
                            entity1 = found_entities[i]
                            entity2 = found_entities[j]
                            
                            # 确定关系类型
                            relation = self._determine_jieba_relation(sentence, entity1["name"], entity2["name"], relations)
                            
                            statement = {
                                "fact": sentence,
                                "subject": entity1["name"],
                                "subject_type": entity1["type"],
                                "predicate": relation,
                                "object": entity2["name"],
                                "object_type": entity2["type"],
                                "confidence": 0.5,  # jieba方法置信度较低
                                "extraction_method": "jieba_fallback",
                                "source": "text_analysis",
                                "embedding": None,
                                "embedding_dim": 0
                            }
                            statements.append(statement)
                            
                elif len(found_entities) == 1:
                    # 单个实体：构建实体属性陈述
                    entity = found_entities[0]
                    
                    # 查找描述性关系
                    descriptive_relation = self._find_descriptive_relation(sentence, entity["name"], relations)
                    
                    if descriptive_relation:
                        statement = {
                            "fact": sentence,
                            "subject": entity["name"],
                            "subject_type": entity["type"],
                            "predicate": descriptive_relation,
                            "object": "相关描述",
                            "object_type": "Description",
                            "confidence": 0.4,
                            "extraction_method": "jieba_fallback",
                            "source": "text_analysis",
                            "embedding": None,
                            "embedding_dim": 0
                        }
                        statements.append(statement)
            
            # 去重
            statements = self._deduplicate_jieba_statements(statements)
            
            logger.info(f"✅ jieba fallback陈述提取完成 - 提取到 {len(statements)} 个陈述")
            return statements
            
        except Exception as e:
            logger.error(f"❌ jieba fallback陈述提取失败: {e}")
            return []
    
    def _split_sentences(self, content: str) -> List[str]:
        """简单的分句方法"""
        import re
        # 按标点符号分句
        sentences = re.split(r'[。！？；]\s*', content)
        return [s.strip() for s in sentences if s.strip()]
    
    def _determine_jieba_relation(self, sentence: str, entity1: str, entity2: str, relations: List[str]) -> str:
        """
        基于句子内容和提取的关系词确定实体间关系
        """
        # 优先使用提取的动词
        if relations:
            return relations[0]
        
        # 基于实体位置和句子结构推测关系
        entity1_pos = sentence.find(entity1)
        entity2_pos = sentence.find(entity2)
        
        if entity1_pos >= 0 and entity2_pos >= 0:
            between_text = sentence[min(entity1_pos, entity2_pos):max(entity1_pos, entity2_pos)]
            
            # 简单的关系映射
            if any(word in between_text for word in ['是', '为', '称为']):
                return "是"
            elif any(word in between_text for word in ['有', '具有', '包含']):
                return "拥有"
            elif any(word in between_text for word in ['来自', '来源', '出自']):
                return "来源于"
            elif any(word in between_text for word in ['创立', '成立', '建立']):
                return "创建"
            elif any(word in between_text for word in ['位于', '在']):
                return "位于"
        
        return "相关"
    
    def _find_descriptive_relation(self, sentence: str, entity: str, relations: List[str]) -> str:
        """为单个实体找到描述性关系"""
        if relations:
            return relations[0]
        
        # 基于句子内容推测描述性关系
        if any(word in sentence for word in ['是', '为']):
            return "描述为"
        elif any(word in sentence for word in ['具有', '包含']):
            return "具有特征"
        elif any(word in sentence for word in ['发生', '进行']):
            return "发生"
        
        return "相关描述"
    
    def _deduplicate_jieba_statements(self, statements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        对jieba提取的陈述进行去重
        """
        seen = set()
        unique_statements = []
        
        for statement in statements:
            # 使用主语-谓语-宾语作为去重键
            key = (statement['subject'].lower(), statement['predicate'], statement['object'].lower())
            if key not in seen:
                seen.add(key)
                unique_statements.append(statement)
        
        return unique_statements
    
    async def _generate_statement_embeddings(self, statements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        为每个陈述生成BGE-M3向量
        
        Args:
            statements: 陈述列表
            
        Returns:
            List[Dict[str, Any]]: 包含向量的陈述列表
            
        Raises:
            BGEEmbeddingError: 向量生成失败时
        """
        if not statements:
            return statements
            
        try:
            logger.debug(f"开始为 {len(statements)} 个陈述生成向量...")
            
            # 使用fact字段作为向量生成的文本
            statement_texts = [stmt["fact"] for stmt in statements]
            
            # 批量生成向量
            embeddings = await self.get_batch_embeddings(statement_texts)
            
            # 将向量添加到陈述中
            for statement, embedding in zip(statements, embeddings):
                statement["embedding"] = embedding
                statement["embedding_dim"] = len(embedding)
                
            logger.info(f"成功为 {len(statements)} 个陈述生成向量")
            return statements
            
        except Exception as e:
            logger.error(f"陈述向量生成失败: {e}")
            # 即使向量生成失败，也返回没有向量的陈述
            for statement in statements:
                statement["embedding"] = None
                statement["embedding_dim"] = 0
            return statements
    
    async def extract_knowledge(self, content: str, context: Dict = None) -> Dict[str, Any]:
        """
        从文本中提取知识结构 - 核心知识提取工作流程
        
        整合实体提取和关系提取，返回完整的知识结构。
        
        Args:
            content: 文本内容
            context: 上下文信息 (可选)
            
        Returns:
            Dict[str, Any]: 包含以下字段的知识提取结果:
                - content_embedding: 内容向量 (List[float])
                - entities: 实体列表 (List[Dict])
                - statements: 知识陈述列表 (List[Dict])
                - confidence_scores: 置信度分数列表 (List[float])
                - processing_stats: 处理统计信息 (Dict)
        """
        if not content or not content.strip():
            raise ValueError("内容不能为空")
            
        start_time = time.time()
        logger.info(f"开始知识提取，内容长度: {len(content)}")
        
        try:
            # 1. 生成内容向量（使用 BGE-M3 服务）
            logger.debug("步骤1: 生成内容向量")
            content_embedding = await self.get_embedding(content)
            
            # 2. 提取实体
            logger.debug("步骤2: 提取实体")
            entities = await self.extract_entities(content)
            
            # 3. 提取关系和陈述
            logger.debug("步骤3: 提取知识陈述")
            statements = await self._extract_statements(content, entities)
            
            # 4. 计算置信度统计
            entity_confidences = [e.get("confidence", 0.5) for e in entities]
            statement_confidences = [s.get("confidence", 0.5) for s in statements]
            all_confidences = entity_confidences + statement_confidences
            
            # 5. 构建处理统计信息
            processing_time = time.time() - start_time
            processing_stats = {
                "processing_time_seconds": processing_time,
                "entities_extracted": len(entities),
                "statements_extracted": len(statements),
                "avg_confidence": sum(all_confidences) / len(all_confidences) if all_confidences else 0.0,
                "content_length": len(content)
            }
            
            result = {
                "content_embedding": content_embedding,
                "entities": entities,
                "statements": statements,
                "confidence_scores": all_confidences,
                "processing_stats": processing_stats
            }
            
            logger.info(f"知识提取完成 - 实体: {len(entities)}, 陈述: {len(statements)}, 耗时: {processing_time:.2f}s")
            return result
            
        except Exception as e:
            error_msg = f"知识提取失败: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    async def check_health(self) -> Dict[str, Any]:
        """
        检查AI服务健康状态
        
        Returns:
            Dict[str, Any]: 健康状态信息
        """
        health_status = {
            "ai_service": {
                "status": "unknown",
                "initialized": self._is_initialized,
                "services": {}
            }
        }
        
        try:
            # 检查BGE-M3服务
            if self.embedding_service:
                try:
                    bge_health = await self.embedding_service.check_health()
                    health_status["ai_service"]["services"]["bge_m3"] = bge_health
                except Exception as e:
                    health_status["ai_service"]["services"]["bge_m3"] = {
                        "status": "error",
                        "error": str(e)
                    }
            else:
                health_status["ai_service"]["services"]["bge_m3"] = {
                    "status": "not_initialized"
                }
            
            # 检查LLM服务
            if self.llm_service:
                try:
                    llm_health = await self.llm_service.health_check()
                    health_status["ai_service"]["services"]["llm"] = llm_health.to_dict()
                except Exception as e:
                    health_status["ai_service"]["services"]["llm"] = {
                        "status": "error",
                        "error": str(e)
                    }
            else:
                health_status["ai_service"]["services"]["llm"] = {
                    "status": "not_configured",
                    "fallback": "jieba_available"
                }
            
            # 确定总体状态
            bge_status = health_status["ai_service"]["services"].get("bge_m3", {}).get("status", "unknown")
            llm_status = health_status["ai_service"]["services"].get("llm", {}).get("status", "unknown")
            
            if bge_status == "healthy":
                health_status["ai_service"]["status"] = "healthy"
            elif bge_status == "unhealthy" and llm_status in ["ready", "running", "configured"]:
                health_status["ai_service"]["status"] = "degraded"
            else:
                health_status["ai_service"]["status"] = "unhealthy"
                
            return health_status
            
        except Exception as e:
            health_status["ai_service"]["status"] = "error"
            health_status["ai_service"]["error"] = str(e)
            return health_status

    async def cleanup(self):
        """清理资源和关闭连接"""
        logger.info("开始清理AI服务资源...")

        try:
            # 关闭BGE向量服务
            if self.embedding_service:
                await self.embedding_service.cleanup()
                self.embedding_service = None
                logger.debug("BGE向量服务连接已关闭")

            # 关闭LLM服务
            if self.llm_service:
                await self.llm_service.cleanup()
                self.llm_service = None
                logger.debug("LLM服务连接已关闭")

            # 重置状态
            self._is_initialized = False
            self._service_available = False
            self._last_health_check = 0

            logger.info("✅ AI服务资源清理完成")

        except Exception as e:
            logger.error(f"❌ AI服务资源清理异常: {e}")

    @asynccontextmanager
    async def managed_service(self):
        """
        上下文管理器，自动管理服务生命周期

        使用示例:
            async with ai_service.managed_service():
                embedding = await ai_service.get_embedding("测试文本")
        """
        try:
            await self.initialize()
            yield self
        finally:
            await self.cleanup()

    def get_service_info(self) -> Dict[str, Any]:
        """
        获取服务状态信息

        Returns:
            Dict[str, Any]: 服务状态详细信息
        """
        service_info = {
            "service_name": "AI知识提取服务",
            "version": "v2.0",
            "is_initialized": self._is_initialized,
            "service_available": self._service_available,
            "last_health_check": self._last_health_check,
        }
        
        # 添加BGE服务信息
        if self.embedding_service:
            service_info["embedding_service"] = self.embedding_service.get_service_info()
        else:
            service_info["embedding_service"] = {"status": "未初始化"}
            
        # 添加LLM服务信息
        if self.llm_service:
            service_info["llm_service"] = self.llm_service.get_service_info()
        else:
            openai_config = settings.get_openai_config()
            service_info["llm_service"] = {
                "status": "未配置",
                "model": openai_config["model"],
                "api_key_configured": bool(openai_config["api_key"]),
                "timeout": openai_config["timeout"],
                "temperature": openai_config["temperature"],
                "max_tokens": openai_config["max_tokens"]
            }
        
        return service_info


# 全局服务实例（单例模式）
_ai_service_instance: Optional[AIExtractionService] = None


def get_ai_service() -> AIExtractionService:
    """
    获取AI服务实例（单例模式）

    Returns:
        AIExtractionService: AI服务实例
    """
    global _ai_service_instance
    if _ai_service_instance is None:
        _ai_service_instance = AIExtractionService()
    return _ai_service_instance


# 便捷函数接口
async def get_text_embedding(text: str) -> List[float]:
    """
    便捷函数：生成单个文本的向量表示

    Args:
        text: 输入文本

    Returns:
        List[float]: 向量表示
    """
    service = get_ai_service()
    return await service.get_embedding(text)


async def get_batch_text_embeddings(texts: List[str]) -> List[List[float]]:
    """
    便捷函数：批量生成文本向量表示

    Args:
        texts: 文本列表

    Returns:
        List[List[float]]: 向量列表
    """
    service = get_ai_service()
    return await service.get_batch_embeddings(texts)


async def extract_text_entities(content: str) -> List[Dict[str, Any]]:
    """
    便捷函数：从文本中提取实体
    
    Args:
        content: 输入文本内容
    
    Returns:
        List[Dict[str, Any]]: 提取的实体列表，包含类型、名称、描述和向量
    """
    service = get_ai_service()
    return await service.extract_entities(content)


async def extract_text_knowledge(content: str, context: Dict = None) -> Dict[str, Any]:
    """
    便捷函数：从文本中提取完整知识结构
    
    Args:
        content: 输入文本内容
        context: 上下文信息 (可选)
    
    Returns:
        Dict[str, Any]: 完整的知识提取结果，包含实体、陈述、向量等
    """
    service = get_ai_service()
    return await service.extract_knowledge(content, context)


if __name__ == "__main__":
    """AI服务模块测试"""

    async def test_ai_service():
        """测试AI服务功能"""
        print("🚀 开始测试AI服务模块...")

        try:
            # 获取服务实例
            service = get_ai_service()
            print(f"📊 服务信息: {service.get_service_info()}")

            # 使用上下文管理器测试
            async with service.managed_service():
                print("✅ 服务初始化成功")

                # 测试单个文本向量生成
                test_text = "这是一个测试文本，用于验证BGE-M3向量生成功能。"
                print(f"🧪 测试文本: {test_text}")

                embedding = await service.get_embedding(test_text)
                print(f"✅ 单个向量生成成功 - 维度: {len(embedding)}")
                print(f"📋 向量前5维: {embedding[:5]}")

                # 测试批量向量生成
                test_texts = [
                    "第一个测试文本",
                    "第二个测试文本",
                    "第三个测试文本"
                ]
                print(f"🧪 批量测试文本数量: {len(test_texts)}")

                batch_embeddings = await service.get_batch_embeddings(test_texts)
                print(f"✅ 批量向量生成成功 - 向量数量: {len(batch_embeddings)}")

                for i, emb in enumerate(batch_embeddings):
                    print(f"📋 第{i+1}个向量维度: {len(emb)}, 前3维: {emb[:3]}")

                # 测试服务健康检查
                is_healthy = await service.is_service_healthy()
                print(f"🏥 服务健康状态: {'✅ 健康' if is_healthy else '❌ 异常'}")
                
                # 测试实体提取功能（如果LLM服务可用）
                if service.llm_service and service.llm_service.is_ready:
                    print("\\n🧠 测试实体提取功能...")
                    test_content = """
                    苹果公司是一家位于美国加利福尼亚州库比蒂诺的科技公司，由史蒂夫·乔布斯、史蒂夫·沃兹尼亚克和罗纳德·韦恩于1976年创立。
                    公司最著名的产品包括iPhone、iPad和MacBook等，这些产品在全球科技市场具有重要影响力。
                    2007年发布的第一代iPhone彻底改变了智能手机行业，开启了移动互联网时代。
                    """
                    
                    try:
                        entities = await service.extract_entities(test_content.strip())
                        print(f"✅ 实体提取成功 - 发现 {len(entities)} 个实体:")
                        
                        for i, entity in enumerate(entities):
                            embedding_status = "✅ 有向量" if entity.get("embedding") else "❌ 无向量"
                            print(f"  {i+1}. [{entity['type']}] {entity['name']} - {embedding_status}")
                            print(f"     描述: {entity['description']}")
                            print(f"     置信度: {entity['confidence']:.2f}")
                            
                    except Exception as e:
                        print(f"⚠️ 实体提取测试失败: {e}")
                else:
                    print("⚠️ LLM服务未配置或不可用，跳过实体提取测试")

            print("🎉 AI服务模块测试完成！")

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()

    # 运行测试
    asyncio.run(test_ai_service())
