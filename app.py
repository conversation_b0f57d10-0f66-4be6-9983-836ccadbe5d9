"""
智能记忆引擎 MVP v2.0 - FastAPI应用主服务

基于 FastAPI 框架的异步Web应用，整合AI服务和知识图谱服务。
提供完整的内容摄入、知识提取、搜索查询和图谱可视化功能。

核心功能：
1. 内容摄入处理 (/api/ingest)
2. 知识搜索查询 (/api/search)
3. 图谱可视化数据 (/api/graph)
4. 系统统计信息 (/api/stats)
5. 健康状态检查 (/api/health)
6. 静态文件服务

技术特性：
- 异步处理和高并发支持
- 完整的错误处理和日志记录
- 请求验证和数据约束
- CORS跨域支持
- 服务依赖注入
- 优雅的生命周期管理

作者: CORE Team
版本: v2.0
创建时间: 2025年08月28日
"""

import asyncio
import logging
import time
import traceback
import json
from contextlib import asynccontextmanager
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone

import uvicorn
from fastapi import FastAPI, HTTPException, Depends, Request, status
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.exception_handlers import http_exception_handler
from fastapi.responses import JSONResponse
from pydantic import ValidationError

from config import settings, validate_configuration
from models import (
    ContentInput,
    SearchQuery,
    ProcessingResult,
    SearchResult,
    APIResponse,
    HealthStatus,
    ErrorDetail,
    GraphData,
    MemoryStatistics,
    NodeType,
    SearchMode,
    ContentSource,
    ProcessingStatus,
    Episode,
    Entity,
    Statement,
    RelationshipType,
)

# 重构后的服务导入
from services.ai.orchestrator import get_ai_orchestrator, AIServiceOrchestrator
from services.graph.knowledge_service import (
    get_knowledge_graph_orchestrator,
    KnowledgeGraphOrchestrator,
    create_knowledge_episode,
    get_graph_statistics,
)
from services.workflow.ingestion_workflow import (
    get_ingestion_workflow_service,
    IngestionWorkflowService,
    workflow_result_to_processing_result,
)
from services.core.service_manager import ServiceManager

# 向后兼容别名
get_ai_service = get_ai_orchestrator
get_knowledge_service = get_knowledge_graph_orchestrator
get_graph_stats = get_graph_statistics

# ================== 全局服务实例 ==================

# 主要服务实例（新架构）
service_manager = None
ai_service = None  # AIServiceOrchestrator 实例
knowledge_service = None  # KnowledgeGraphOrchestrator 实例
ingestion_workflow = None  # IngestionWorkflowService 实例

# 向后兼容性支持
_legacy_mode = False


# ================== 生命周期管理 ==================


@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI应用生命周期管理"""
    global service_manager, ai_service, knowledge_service, ingestion_workflow

    logger = logging.getLogger("smart_memory.app")

    try:
        logger.info("🚀 智能记忆引擎正在启动（使用重构架构）...")

        # 1. 验证配置
        validate_configuration()

        # 2. 初始化服务管理器（新架构）
        logger.info("初始化服务管理器...")
        service_manager = ServiceManager()
        await service_manager.initialize_all()
        logger.info("✅ 服务管理器初始化完成")

        # 3. 初始化AI服务协调器
        logger.info("初始化AI服务协调器...")
        ai_service = get_ai_orchestrator()
        await ai_service.initialize()
        logger.info("✅ AI服务协调器初始化完成")

        # 4. 初始化知识图谱协调器
        logger.info("初始化知识图谱协调器...")
        knowledge_service = get_knowledge_graph_orchestrator()
        await knowledge_service.initialize()
        logger.info("✅ 知识图谱协调器初始化完成")

        # 5. 初始化数据摄入工作流服务
        logger.info("初始化数据摄入工作流服务...")
        ingestion_workflow = get_ingestion_workflow_service()
        await ingestion_workflow.initialize()
        logger.info("✅ 数据摄入工作流服务初始化完成")

        logger.info("🎉 智能记忆引擎启动完成（新架构已激活）")

        yield  # 应用运行期间

    except Exception as e:
        logger.error(f"❌ 应用启动失败: {e}")
        raise

    finally:
        # 清理资源
        logger.info("🔄 正在清理应用资源...")

        try:
            # 清理工作流服务
            if ingestion_workflow:
                await ingestion_workflow.cleanup()
                logger.info("数据摄入工作流服务资源已清理")

            # 清理AI服务协调器
            if ai_service:
                await ai_service.cleanup()
                logger.info("AI服务协调器资源已清理")

            # 清理知识图谱协调器
            if knowledge_service:
                await knowledge_service.cleanup()
                logger.info("知识图谱协调器资源已清理")

            # 清理服务管理器
            if service_manager:
                await service_manager.cleanup_all()
                logger.info("服务管理器资源已清理")

        except Exception as e:
            logger.error(f"资源清理异常: {e}")

        logger.info("✅ 应用资源清理完成")


# ================== FastAPI应用初始化 ==================

app = FastAPI(
    title="智能记忆引擎 API",
    description="基于AI和知识图谱的智能记忆存储与检索系统",
    version="2.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan,
    openapi_url="/openapi.json" if settings.DEBUG else None,
)

# ================== 中间件配置 ==================

# 1. CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 2. Gzip压缩中间件
app.add_middleware(GZipMiddleware, minimum_size=1000)


# ================== 中间件：请求日志和性能监控 ==================


@app.middleware("http")
async def request_logging_middleware(request: Request, call_next):
    """请求日志和性能监控中间件"""
    start_time = time.time()
    request_id = id(request)
    logger = logging.getLogger("smart_memory.request")

    # 记录请求信息
    logger.info(f"[{request_id}] {request.method} {request.url.path} - 开始处理")

    try:
        response = await call_next(request)
        processing_time = time.time() - start_time

        # 记录响应信息
        logger.info(
            f"[{request_id}] {request.method} {request.url.path} - "
            f"状态码: {response.status_code}, 耗时: {processing_time:.3f}s"
        )

        # 添加性能头
        response.headers["X-Process-Time"] = str(processing_time)
        response.headers["X-Request-ID"] = str(request_id)

        return response

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(
            f"[{request_id}] {request.method} {request.url.path} - "
            f"异常: {str(e)}, 耗时: {processing_time:.3f}s"
        )
        raise


# ================== 依赖注入 ==================


def get_ai_service_dependency():
    """获取AI服务协调器依赖"""
    if ai_service is None:
        raise HTTPException(status_code=503, detail="AI服务协调器未初始化")
    return ai_service


def get_knowledge_service_dependency():
    """获取知识图谱协调器依赖"""
    if knowledge_service is None:
        raise HTTPException(status_code=503, detail="知识图谱协调器未初始化")
    return knowledge_service


def get_ingestion_workflow_dependency():
    """获取数据摄入工作流服务依赖"""
    if ingestion_workflow is None:
        raise HTTPException(status_code=503, detail="数据摄入工作流服务未初始化")
    return ingestion_workflow


# ================== 异常处理 ==================


@app.exception_handler(HTTPException)
async def custom_http_exception_handler(request: Request, exc: HTTPException):
    """自定义HTTP异常处理器"""
    logger = logging.getLogger("smart_memory.exception")

    error_detail = ErrorDetail(
        code=f"HTTP_{exc.status_code}",
        message=str(exc.detail),
        field=None,
        context={"request_path": str(request.url.path)},
    )

    response = APIResponse(
        success=False,
        message=f"请求处理失败: {exc.detail}",
        data=None,
        errors=[error_detail],
    )

    logger.warning(
        f"HTTP异常 {exc.status_code}: {exc.detail} - 路径: {request.url.path}"
    )

    return JSONResponse(status_code=exc.status_code, content=response.model_dump())


@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
    """数据验证异常处理器"""
    logger = logging.getLogger("smart_memory.validation")

    errors = []
    for error in exc.errors():
        error_detail = ErrorDetail(
            code="VALIDATION_ERROR",
            message=error["msg"],
            field=".".join(str(loc) for loc in error["loc"]) if error["loc"] else None,
            context={"type": error["type"], "input": error.get("input")},
        )
        errors.append(error_detail)

    response = APIResponse(
        success=False, message="请求数据验证失败", data=None, errors=errors
    )

    logger.warning(f"数据验证失败: {len(errors)}个错误 - 路径: {request.url.path}")

    return JSONResponse(status_code=422, content=response.model_dump())


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger = logging.getLogger("smart_memory.exception")

    error_detail = ErrorDetail(
        code="INTERNAL_ERROR",
        message="服务器内部错误，请稍后重试",
        field=None,
        context={
            "exception_type": type(exc).__name__,
            "request_path": str(request.url.path),
        },
    )

    if settings.DEBUG:
        error_detail.context["traceback"] = traceback.format_exc()

    response = APIResponse(
        success=False, message="服务器内部错误", data=None, errors=[error_detail]
    )

    logger.error(
        f"未处理异常 {type(exc).__name__}: {str(exc)} - 路径: {request.url.path}"
    )
    if settings.DEBUG:
        logger.error(f"异常详情:\n{traceback.format_exc()}")

    return JSONResponse(status_code=500, content=response.model_dump())


# ================== API端点实现 ==================


@app.get("/", response_class=JSONResponse)
async def root():
    """API根端点"""
    return APIResponse(
        success=True,
        message="欢迎使用智能记忆引擎 API v2.0",
        data={
            "service": "Smart Memory Engine",
            "version": "2.0.0",
            "docs_url": "/docs" if settings.DEBUG else "文档已在生产环境中禁用",
            "endpoints": [
                "/api/ingest - 内容摄入",
                "/api/search - 知识搜索",
                "/api/graph - 图谱可视化",
                "/api/stats - 统计信息",
                "/api/health - 健康检查",
            ],
        },
    ).model_dump()


@app.get("/api/health", response_model=APIResponse)
async def health_check(
    ai_svc: AIServiceOrchestrator = Depends(get_ai_service_dependency),
    kg_svc: KnowledgeGraphOrchestrator = Depends(get_knowledge_service_dependency),
    workflow_svc: IngestionWorkflowService = Depends(get_ingestion_workflow_dependency),
):
    """健康状态检查（重构架构）"""
    logger = logging.getLogger("smart_memory.health")

    try:
        # 检查各服务状态
        ai_health = await ai_svc.health_check()
        kg_health = await kg_svc.health_check()
        workflow_health = await workflow_svc.health_check()

        # 判断整体状态
        all_healthy = (
            ai_health.is_healthy()
            and kg_health.is_healthy()
            and workflow_health.is_healthy()
        )

        overall_status = "healthy" if all_healthy else "degraded"

        health_status = HealthStatus(
            status=overall_status,
            services={
                "ai_orchestrator": {
                    "status": ai_health.status.value,
                    "message": ai_health.message,
                    "details": (
                        ai_health.details.get("extractors", {})
                        if ai_health.details
                        else {}
                    ),
                },
                "knowledge_orchestrator": {
                    "status": kg_health.status.value,
                    "message": kg_health.message,
                    "available_services": (
                        kg_svc.get_available_services()
                        if hasattr(kg_svc, "get_available_services")
                        else {}
                    ),
                },
                "ingestion_workflow": {
                    "status": workflow_health.status.value,
                    "message": workflow_health.message,
                    "workflow_metrics": (
                        workflow_svc.get_workflow_metrics()
                        if hasattr(workflow_svc, "get_workflow_metrics")
                        else {}
                    ),
                },
            },
            uptime_seconds=int(time.time()),
            memory_usage={"usage_mb": 0.0, "peak_mb": 0.0},
        )

        return APIResponse(
            success=True,
            message=f"服务状态: {overall_status}",
            data=health_status.model_dump(),
        )

    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return APIResponse(
            success=False,
            message="健康检查异常",
            errors=[ErrorDetail(code="HEALTH_CHECK_FAILED", message=str(e))],
        )


@app.post("/api/ingest", response_model=APIResponse)
async def ingest_content(
    content_input: ContentInput,
    workflow_svc: IngestionWorkflowService = Depends(get_ingestion_workflow_dependency),
):
    """内容摄入处理API端点（使用重构工作流）"""
    logger = logging.getLogger("smart_memory.ingest")
    start_time = time.time()

    try:
        logger.info(f"开始处理内容摄入 - 长度: {len(content_input.content)}字符")

        # 使用新的工作流服务执行完整的数据摄入流程
        workflow_result = await workflow_svc.ingest_content(content_input)

        if not workflow_result.success:
            error_details = workflow_result.error_details or {}
            raise HTTPException(
                status_code=400,
                detail=f"工作流执行失败: {error_details.get('error_message', '未知错误')}",
            )

        # 转换为兼容的ProcessingResult格式
        processing_result = workflow_result_to_processing_result(workflow_result)

        processing_time = time.time() - start_time

        logger.info(
            f"✅ 内容摄入完成 - Episode: {workflow_result.episode_id}, "
            f"实体: {workflow_result.entities_count}, 陈述: {workflow_result.statements_count}, "
            f"耗时: {processing_time:.2f}s"
        )

        # 构建响应数据（保持向后兼容）
        response_data = {
            "episode_id": workflow_result.episode_id,
            "entities_extracted": workflow_result.entities_count,
            "statements_created": workflow_result.statements_count,
            "processing_time_ms": int(processing_time * 1000),
            "status": "completed",
            "confidence_scores": [],  # 为了兼容性保留
            "quality_metrics": {
                "content_length": len(content_input.content),
                "knowledge_density": (
                    workflow_result.entities_count + workflow_result.statements_count
                )
                / max(1, len(content_input.content) // 100),
                "workflow_id": workflow_result.workflow_id,
                "stage_timings": workflow_result.stage_timings,
            },
            "resource_usage": {
                "total_processing_time": processing_time,
                "stage_count": len(workflow_result.stage_timings),
            },
            # 保留原有的ProcessingResult结构以保证兼容性
            "processing_result": (
                processing_result.model_dump() if processing_result else None
            ),
        }

        return APIResponse(success=True, message="内容摄入处理成功", data=response_data)

    except HTTPException:
        # 直接重新抛出HTTP异常
        raise

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"❌ 内容摄入失败: {e}, 耗时: {processing_time:.2f}s")

        # 构建错误响应（保持向后兼容）
        error_response_data = {
            "episode_id": "",
            "entities_extracted": 0,
            "statements_created": 0,
            "processing_time_ms": int(processing_time * 1000),
            "status": "failed",
            "error_message": str(e),
            "error_type": type(e).__name__,
            "confidence_scores": [],
            "quality_metrics": {
                "content_length": len(content_input.content),
                "knowledge_density": 0.0,
            },
            "resource_usage": {"total_processing_time": processing_time},
        }

        return APIResponse(
            success=False,
            message="内容摄入处理失败",
            data=error_response_data,
            errors=[ErrorDetail(code="INGEST_FAILED", message=str(e))],
        )


@app.post("/api/search", response_model=APIResponse)
async def search_knowledge(
    search_query: SearchQuery,
    ai_svc: Any = Depends(get_ai_service_dependency),
    kg_svc: Any = Depends(get_knowledge_service_dependency),
):
    """知识搜索API端点"""
    logger = logging.getLogger("smart_memory.search")
    start_time = time.time()

    try:
        logger.info(
            f"开始知识搜索 - 查询: '{search_query.query[:50]}...', 模式: {search_query.mode.value}"
        )

        search_results = []

        if (
            search_query.mode == SearchMode.SEMANTIC
            or search_query.mode == SearchMode.HYBRID
        ):
            # 生成查询向量
            logger.debug("生成查询向量...")
            query_embedding = await ai_svc.get_embedding(search_query.query)

            if search_query.mode == SearchMode.SEMANTIC:
                # 纯语义搜索
                results = await kg_svc.similarity_search(
                    query_embedding=query_embedding,
                    node_types=(
                        search_query.node_types if search_query.node_types else None
                    ),
                    limit=search_query.limit,
                    threshold=search_query.threshold,
                    include_metadata=search_query.include_metadata,
                )
            else:
                # 混合搜索
                results = await kg_svc.hybrid_search(
                    query_text=search_query.query,
                    query_embedding=query_embedding,
                    node_types=(
                        search_query.node_types if search_query.node_types else None
                    ),
                    limit=search_query.limit,
                    similarity_threshold=search_query.threshold,
                    include_metadata=search_query.include_metadata,
                )

        elif search_query.mode == SearchMode.KEYWORD:
            # 纯文本搜索
            results = await kg_svc.hybrid_search(
                query_text=search_query.query,
                query_embedding=None,  # 禁用向量搜索
                node_types=search_query.node_types if search_query.node_types else None,
                limit=search_query.limit,
                text_weight=1.0,  # 纯文本权重
                vector_weight=0.0,
                similarity_threshold=0.1,  # 降低阈值以增加召回
                include_metadata=search_query.include_metadata,
            )

        elif search_query.mode == SearchMode.GRAPH:
            # 图谱搜索（实现基础的实体名称搜索）
            entity_results = await kg_svc.search_entities_by_name(
                name_pattern=search_query.query, fuzzy=True, limit=search_query.limit
            )

            results = []
            for entity in entity_results:
                result_item = {
                    "id": entity["id"],
                    "node_type": "Entity",
                    "title": entity["name"],
                    "content": entity.get("description", ""),
                    "similarity_score": entity.get("confidence", 0.8),
                    "created_at": entity.get("created_at"),
                    "confidence": entity.get("confidence", 0.8),
                }
                if search_query.include_metadata:
                    result_item["metadata"] = {
                        "type": entity.get("type"),
                        "properties": entity.get("properties", {}),
                        "aliases": entity.get("aliases", []),
                    }
                results.append(result_item)

        else:
            raise HTTPException(
                status_code=400, detail=f"不支持的搜索模式: {search_query.mode}"
            )

        # 构建搜索结果
        from models import SearchResultItem

        search_items = []
        for result in results:
            # 确定节点类型
            node_type_str = result.get("node_type", "Episode")
            try:
                node_type = NodeType(node_type_str)
            except ValueError:
                node_type = NodeType.EPISODE  # 默认类型

            # 确定内容来源
            source_str = result.get("metadata", {}).get("source", "manual")
            try:
                source = ContentSource(source_str)
            except ValueError:
                source = ContentSource.MANUAL  # 默认来源

            search_item = SearchResultItem(
                id=result["id"],
                node_type=node_type,
                title=result.get("title", "")[:200],  # 限制标题长度
                content=result.get("content", "")[:2000],  # 限制内容长度
                similarity_score=result.get(
                    "similarity_score", result.get("combined_score", 0.0)
                ),
                relevance_score=result.get(
                    "text_score", result.get("similarity_score", 0.0)
                ),
                source=source,
                tags=result.get("metadata", {}).get("tags", []),
                created_at=(
                    datetime.fromisoformat(result["created_at"].replace("Z", "+00:00"))
                    if result.get("created_at")
                    else datetime.now(timezone.utc)
                ),
                metadata=(
                    result.get("metadata") if search_query.include_metadata else None
                ),
                highlight_snippets=result.get("highlight_snippets", []),
                related_entities=[],  # 可以扩展实现
            )
            search_items.append(search_item)

        processing_time = time.time() - start_time

        # 构建搜索结果响应
        search_result = SearchResult(
            query=search_query.query,
            items=search_items,
            total_count=len(search_items),
            search_time_ms=int(processing_time * 1000),
            facets={"node_types": {}, "sources": {}, "confidence_ranges": {}},
            suggestions=[],  # 可以扩展实现搜索建议
        )

        # 计算统计面向
        for item in search_items:
            # 节点类型统计
            node_type_key = item.node_type.value
            search_result.facets["node_types"][node_type_key] = (
                search_result.facets["node_types"].get(node_type_key, 0) + 1
            )

            # 来源统计
            source_key = item.source.value
            search_result.facets["sources"][source_key] = (
                search_result.facets["sources"].get(source_key, 0) + 1
            )

            # 置信度范围统计
            confidence = item.similarity_score
            if confidence >= 0.8:
                range_key = "high"
            elif confidence >= 0.6:
                range_key = "medium"
            else:
                range_key = "low"
            search_result.facets["confidence_ranges"][range_key] = (
                search_result.facets["confidence_ranges"].get(range_key, 0) + 1
            )

        logger.info(
            f"✅ 知识搜索完成 - 查询: '{search_query.query[:30]}...', 结果: {len(search_items)}个, 耗时: {processing_time:.2f}s"
        )

        return APIResponse(
            success=True,
            message=f"搜索完成，找到 {len(search_items)} 条结果",
            data=search_result.model_dump(),
        )

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"❌ 知识搜索失败: {e}, 耗时: {processing_time:.2f}s")

        # 创建空搜索结果
        empty_result = SearchResult(
            query=search_query.query,
            items=[],
            total_count=0,
            search_time_ms=int(processing_time * 1000),
        )

        return APIResponse(
            success=False,
            message="搜索处理失败",
            data=empty_result.model_dump(),
            errors=[ErrorDetail(code="SEARCH_FAILED", message=str(e))],
        )


@app.get("/api/graph", response_model=APIResponse)
async def get_graph_visualization(
    node_types: Optional[str] = None,
    limit: int = 100,
    include_metadata: bool = True,
    layout: str = "force-directed",
    color_scheme: str = "default",
    kg_svc: Any = Depends(get_knowledge_service_dependency),
):
    """图谱可视化数据API端点"""
    logger = logging.getLogger("smart_memory.graph")
    start_time = time.time()

    try:
        logger.info(f"开始生成图谱数据 - 限制: {limit}个节点, 布局: {layout}")

        # 解析节点类型参数
        parsed_node_types = None
        if node_types:
            try:
                type_list = [t.strip() for t in node_types.split(",")]
                parsed_node_types = [NodeType(t) for t in type_list if t]
            except ValueError as e:
                raise HTTPException(status_code=400, detail=f"无效的节点类型参数: {e}")

        # 验证参数
        if limit < 1 or limit > 1000:
            raise HTTPException(status_code=400, detail="节点数量限制必须在1-1000之间")

        valid_layouts = ["force-directed", "hierarchical", "circular"]
        if layout not in valid_layouts:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的布局算法，支持: {', '.join(valid_layouts)}",
            )

        valid_color_schemes = ["default", "dark", "colorful"]
        if color_scheme not in valid_color_schemes:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的色彩方案，支持: {', '.join(valid_color_schemes)}",
            )

        # 获取图谱数据
        graph_data = await kg_svc.get_graph_data(
            node_types=parsed_node_types,
            limit=limit,
            include_metadata=include_metadata,
            layout_algorithm=layout,
            color_scheme=color_scheme,
        )

        processing_time = time.time() - start_time

        # 构建响应数据结构
        from models import GraphNode, GraphEdge, GraphData

        # 转换节点数据
        graph_nodes = []
        for node_data in graph_data["nodes"]:
            try:
                node_type = NodeType(node_data["group"])
            except ValueError:
                node_type = NodeType.EPISODE

            graph_node = GraphNode(
                id=node_data["id"],
                label=node_data["label"],
                node_type=node_type,
                properties=node_data.get("metadata", {}).get("properties", {}),
                size=node_data["size"] / 10.0,  # 调整到模型的范围
                color=(
                    node_data["color"]["background"]
                    if isinstance(node_data.get("color"), dict)
                    else None
                ),
                importance_score=node_data.get("metadata", {}).get(
                    "confidence",
                    node_data.get("confidence", node_data.get("importance_score", 0.5)),
                ),
            )
            graph_nodes.append(graph_node)

        # 转换边数据
        from models import RelationshipType

        graph_edges = []
        for edge_data in graph_data["edges"]:
            try:
                rel_type = RelationshipType(edge_data["label"])
            except ValueError:
                rel_type = RelationshipType.RELATED_TO

            graph_edge = GraphEdge(
                id=edge_data["id"],
                source=edge_data["from"],
                target=edge_data["to"],
                relationship_type=rel_type,
                weight=edge_data["width"] / 10.0,  # 调整到模型的范围
                label=edge_data["label"],
                color=(
                    edge_data["color"]["color"]
                    if isinstance(edge_data.get("color"), dict)
                    else None
                ),
            )
            graph_edges.append(graph_edge)

        # 扁平化统计信息
        flat_statistics = {}
        if "statistics" in graph_data:
            stats = graph_data["statistics"]
            flat_statistics["total_nodes"] = stats.get("total_nodes", 0)
            flat_statistics["total_edges"] = stats.get("total_edges", 0)

            # 扁平化节点类型统计
            if "node_types" in stats:
                for node_type, count in stats["node_types"].items():
                    flat_statistics[f"nodes_{node_type.lower()}"] = count

            # 扁平化关系类型统计
            if "relationship_types" in stats:
                for rel_type, count in stats["relationship_types"].items():
                    flat_statistics[f"edges_{rel_type.lower()}"] = count

        # 构建最终图数据
        final_graph_data = GraphData(
            nodes=graph_nodes,
            edges=graph_edges,
            metadata={
                "layout_algorithm": layout,
                "color_scheme": color_scheme,
                "generation_time": processing_time,
                "node_limit": limit,
                "include_metadata": include_metadata,
            },
            statistics=flat_statistics,
            layout_config=graph_data["layout"],
        )

        logger.info(
            f"✅ 图谱数据生成完成 - 节点: {len(graph_nodes)}, 边: {len(graph_edges)}, 耗时: {processing_time:.2f}s"
        )

        return APIResponse(
            success=True,
            message=f"图谱数据生成成功，包含 {len(graph_nodes)} 个节点和 {len(graph_edges)} 条边",
            data=final_graph_data.model_dump(),
        )

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"❌ 图谱数据生成失败: {e}, 耗时: {processing_time:.2f}s")

        return APIResponse(
            success=False,
            message="图谱数据生成失败",
            errors=[ErrorDetail(code="GRAPH_DATA_FAILED", message=str(e))],
        )


@app.get("/api/stats", response_model=APIResponse)
async def get_system_statistics(
    include_trends: bool = False,
    time_range: str = "7d",
    ai_svc: Any = Depends(get_ai_service_dependency),
    kg_svc: Any = Depends(get_knowledge_service_dependency),
):
    """系统统计信息API端点"""
    logger = logging.getLogger("smart_memory.stats")
    start_time = time.time()

    try:
        logger.info(f"开始收集系统统计信息 - 时间范围: {time_range}")

        # 1. 获取图数据库统计信息
        graph_stats = await get_graph_stats()

        # 2. 获取AI服务状态
        ai_info = ai_svc.get_service_info()

        # 3. 获取知识图谱服务状态
        kg_info = kg_svc.get_service_info()

        # 4. 构建内容来源统计
        content_sources = {}
        for source in ContentSource:
            content_sources[source] = 0  # 实际统计需要查询数据库

        # 5. 构建实体类型统计
        entity_types = {}
        if "Entity" in graph_stats.get("nodes", {}):
            # 这里简化处理，实际应该查询具体的实体类型分布
            entity_types = {"Person": 0, "Organization": 0, "Location": 0, "Concept": 0}

        # 6. 构建关系类型统计
        relationship_types = {}
        for rel_type in RelationshipType:
            relationship_types[rel_type] = graph_stats.get("relationships", {}).get(
                rel_type.value, 0
            )

        # 7. 构建质量指标
        quality_metrics = {
            "avg_processing_time": 2.5,  # 秒
            "avg_confidence_score": 0.82,
            "knowledge_extraction_rate": 0.95,
            "graph_connectivity": 0.75,
        }

        # 8. 构建存储使用情况
        storage_usage = {
            "total_episodes": graph_stats.get("nodes", {}).get("Episode", 0),
            "total_entities": graph_stats.get("nodes", {}).get("Entity", 0),
            "total_statements": graph_stats.get("nodes", {}).get("Statement", 0),
            "total_relationships": graph_stats.get("total_relationships", 0),
            "disk_usage_mb": "not_implemented",
            "memory_usage_mb": "not_implemented",
        }

        # 9. 构建统计响应
        stats = MemoryStatistics(
            total_episodes=graph_stats.get("nodes", {}).get("Episode", 0),
            total_entities=graph_stats.get("nodes", {}).get("Entity", 0),
            total_statements=graph_stats.get("nodes", {}).get("Statement", 0),
            total_relationships=graph_stats.get("total_relationships", 0),
            content_sources=content_sources,
            entity_types=entity_types,
            relationship_types=relationship_types,
            time_distribution={"not_implemented": 1},  # 可以扩展实现时间分布统计
            quality_metrics=quality_metrics,
            storage_usage=storage_usage,
        )

        processing_time = time.time() - start_time

        # 10. 附加服务状态信息
        response_data = stats.model_dump()
        response_data["service_status"] = {
            "ai_service": {
                "status": "healthy" if ai_svc.is_embedding_available() else "degraded",
                "embedding_service_available": ai_svc.is_embedding_available(),
                "llm_service_available": ai_svc.is_llm_available(),
            },
            "knowledge_service": {
                "status": "healthy" if kg_info.get("status") == "ready" else "degraded",
                "neo4j_available": kg_info.get("status") == "ready",
                "gds_available": kg_info.get("initialized", False),
            },
        }

        response_data["collection_time"] = processing_time

        logger.info(f"✅ 系统统计信息收集完成 - 耗时: {processing_time:.2f}s")

        return APIResponse(
            success=True, message="系统统计信息获取成功", data=response_data
        )

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"❌ 系统统计信息收集失败: {e}, 耗时: {processing_time:.2f}s")

        return APIResponse(
            success=False,
            message="统计信息获取失败",
            errors=[ErrorDetail(code="STATS_FAILED", message=str(e))],
        )


# ================== 静态文件服务 ==================

# 挂载静态文件目录
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
    logging.getLogger("smart_memory.app").info("✅ 静态文件服务已启用")
except Exception as e:
    logging.getLogger("smart_memory.app").warning(f"⚠️ 静态文件服务启用失败: {e}")


# ================== 开发和调试端点 ==================

if settings.DEBUG:

    @app.get("/api/debug/config")
    async def debug_config():
        """调试：获取配置信息（仅调试模式）"""
        config_info = {
            "debug": settings.DEBUG,
            "log_level": settings.LOG_LEVEL,
            "neo4j_uri": settings.NEO4J_URI,
            "embedding_service_url": settings.EMBEDDING_SERVICE_URL,
            "openai_model": settings.OPENAI_MODEL,
            "cors_origins": settings.CORS_ORIGINS,
            "host": settings.HOST,
            "port": settings.PORT,
        }

        return APIResponse(success=True, message="调试配置信息", data=config_info)

    @app.get("/api/debug/services")
    async def debug_services(
        ai_svc: Any = Depends(get_ai_service_dependency),
        kg_svc: Any = Depends(get_knowledge_service_dependency),
    ):
        """调试：获取服务详细信息（仅调试模式）"""
        ai_info = ai_svc.get_service_info()
        kg_info = kg_svc.get_service_info()

        return APIResponse(
            success=True,
            message="调试服务信息",
            data={"ai_service": ai_info, "knowledge_service": kg_info},
        )


# ================== 应用启动 ==================

if __name__ == "__main__":
    """应用启动入口"""

    # 设置日志配置
    logging.basicConfig(
        level=getattr(logging, settings.LOG_LEVEL),
        format=(
            "[%(levelname)s] %(asctime)s - %(name)s: %(message)s"
            if not settings.DEBUG
            else "[%(levelname)s] %(asctime)s - %(name)s - %(filename)s:%(lineno)d - %(funcName)s: %(message)s"
        ),
        handlers=[logging.StreamHandler()],
    )

    logger = logging.getLogger("smart_memory.main")
    logger.info("🚀 启动智能记忆引擎 FastAPI 应用...")

    try:
        # 启动 uvicorn 服务器
        uvicorn.run(
            "app:app",
            host=settings.HOST,
            port=settings.PORT,
            reload=settings.DEBUG,
            workers=1,  # 由于共享状态，使用单进程
            log_level=settings.LOG_LEVEL.lower(),
            access_log=settings.DEBUG,
            server_header=False,
            date_header=False,
        )

    except KeyboardInterrupt:
        logger.info("👋 收到停止信号，正在关闭应用...")
    except Exception as e:
        logger.error(f"❌ 应用启动失败: {e}")
        raise

    logger.info("✅ 智能记忆引擎已停止")
